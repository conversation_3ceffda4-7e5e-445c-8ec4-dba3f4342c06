"""
CLI Editor Service
==================

This module provides the Editor class, a high-level service for handling CLI (Common Layer Interface)
file operations in the RecoaterHMI backend. It orchestrates parsing, rendering, and generation tasks
by inheriting functionality from specialized mixins (CliFileParser, CliRenderer, CliGenerator).

Key Features:
- Parse CLI files (inherited from CliFileParser).
- Render CLI layers as PNG images for previews (inherited from CliRenderer).
- Generate new CLI files (binary or ASCII) from layer data (inherited from CliGenerator).
- Supports drum-specific coloring and layer configuration previews.

Usage Workflow:
1. Parse a CLI file: `editor = Editor(); parsed = editor.parse(cli_bytes)`.
2. Render a layer: `png = editor.render_layer_to_png(layer)`.
3. Generate a new CLI: `new_cli = editor.generate_single_layer_cli(layer)`.

This design promotes modularity, allowing independent testing and extension of parsing, rendering,
and generation functionalities.

Raises:
    CliParsingError: For parsing or generation failures.
    CliRenderingError: For rendering failures.
    CliGenerationError: For generation failures.
"""
import logging
from typing import List, Optional

from .cli_exceptions import CliParsingError
from .cli_models import Point, Polyline, Hatch, CliLayer, ParsedCliFile
from .cli_file_parser import CliFileParser
from .cli_renderer import CliRenderer
from .cli_generator import CliGenerator

# Export classes for backward compatibility
__all__ = [
    'Editor',
    'CliParsingError',
    'Point',
    'Polyline',
    'Hatch',
    'CliLayer',
    'ParsedCliFile'
]

# --- Main Service Class ---
class Editor(CliFileParser, CliRenderer, CliGenerator):
    """A service to handle parsing of binary CLI files, rendering of layers, and generation of CLI files."""
    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initializes the parser service."""
        self._logger = logger or logging.getLogger(__name__)

    def parse(self, cli_byte_stream: bytes) -> ParsedCliFile:
        """Parse CLI data using inherited parsing functionality"""
        return super().parse(cli_byte_stream)
    

