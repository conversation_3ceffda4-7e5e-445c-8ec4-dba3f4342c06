"""
CLI Generator Module
====================

This module provides the CliGenerator class that combines binary and ASCII CLI generation
capabilities through mixin inheritance. It supports both binary and ASCII formats, with
options for single layers or layer ranges, and handles header customization.

Key Features:
- Generate binary CLI files for single layers or ranges (via BinaryCliGenerator mixin).
- Generate ASCII CLI files compatible with recoater hardware (via AsciiCliGenerator mixin).
- Support for aligned formats and custom headers.
- Modular architecture with separated concerns for binary vs ASCII generation.

Dependencies:
- BinaryCliGenerator mixin for binary CLI generation functionality.
- AsciiCliGenerator mixin for ASCII CLI generation functionality.

Usage:
    class MyClass(CliGenerator):
        pass
    instance = MyClass()
    cli_bytes = instance.generate_single_layer_cli(layer)  # Binary format
    ascii_bytes = instance.generate_single_layer_ascii_cli(layer)  # ASCII format
"""
from .ascii_cli_generator import AsciiCliGenerator
from .binary_cli_generator import BinaryCliGenerator


class CliGenerator(AsciiCliGenerator, BinaryCliGenerator):
    """
    Combined CLI generator that provides both binary and ASCII CLI generation capabilities.

    This class inherits from both AsciiCliGenerator and BinaryCliGenerator mixins to provide
    a unified interface for CLI file generation. All methods are inherited from the mixins,
    maintaining full backward compatibility.

    Available Methods (inherited):
    - Binary generation: generate_single_layer_cli(), generate_cli_from_layer_range()
    - ASCII generation: generate_single_layer_ascii_cli(), generate_ascii_cli_from_layer_range()
    - ASCII utilities: write_ascii_header(), write_ascii_polylines(), write_ascii_hatchlines()
    """
    pass
