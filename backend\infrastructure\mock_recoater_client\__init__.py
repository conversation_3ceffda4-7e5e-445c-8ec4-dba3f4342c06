"""
Mock Recoater Client Package
============================

This package provides the MockRecoaterClient class and related components for
simulating the Aerosint SPD Recoater hardware API during development and testing.

The package is organized into the following modules:
- mock_client: Main MockRecoaterClient class with core functionality
- mock_blade_controls: Blade control methods
- mock_leveler_controls: Leveler control methods  
- mock_print_controls: Print control methods
- mock_file_management: File management methods
- mock_async_client: Async wrapper methods for multi-material coordination
- mock_drum_controls: Drum control methods

The MockRecoaterClient class implements the same interface as RecoaterClient
but returns mock data instead of making actual HTTP requests to hardware.
"""

from .mock_client import MockRecoaterClient

__all__ = [
    'MockRecoaterClient'
]
