# Multi-Material Print Job Workflow Specification

## Overview
This document outlines the complete workflow for multi-material printing in the Aerosint Recoater HMI system, from CLI file upload to print completion.

## Expected Workflow (Target Implementation)

### Phase 1: File Upload and Caching
1. **Individual Drum Upload**: Operator uploads CLI files to specific drums (0, 1, 2) using drum-specific upload buttons
2. **File Caching**: `MultiMaterialJobService` caches uploaded CLI files associated with their target drums
3. **Capacity Management**: Maximum of 3 CLI files (one per drum)
4. **Optional Files**: Drums without uploaded files will use `empty_layer.cli` for missing layers

### Phase 2: Job Initialization
1. **Start Button Press**: Operator clicks "Start Print Job" button (no parameters needed)
2. **Layer Validation**: Service determines the maximum layer count across all cached CLI files
3. **Job State Creation**: Initialize job tracking with drum assignments and layer counts

### Phase 3: Layer-by-Layer Execution
1. **Layer Upload**: For each layer iteration:
   - Upload layer[n] from cached CLI file for Drum 0 (if available, else `empty_layer.cli`)
   - 1s pause
   - Upload layer[n] from cached CLI file for Drum 1 (if available, else `empty_layer.cli`) 
   - 1s pause
   - Upload layer[n] from cached CLI file for Drum 2 (if available, else `empty_layer.cli`)
2. **Print Execution**: Call `start_print_job()` to begin printing the uploaded layers
3. **Status Monitoring**: Poll job status until hardware transitions from 'printing' to 'ready'
4. **Iteration**: Repeat for layer[n+1] until maximum layer count reached

### Phase 4: Completion
1. **Final Status**: Set job status to completed when all layers processed
2. **Cleanup**: Clear cached files and reset job state

## Current Implementation Issues

### Problem 1: File Upload Targeting
- **Current**: `upload_cli_file` uploads to general cache without drum association
- **Expected**: Files should be uploaded with explicit drum targeting
- **Solution**: Modify upload endpoint to accept drum_id parameter

### Problem 2: Job Start Parameters
- **Current**: `start_multimaterial_job` requires `job_request` with file_ids mapping
- **Expected**: Button press should be parameter-free, using cached files
- **Solution**: Service should use internally cached drum→file associations

### Problem 3: Upload Strategy  
- **Current**: Uploads entire CLI files to drums upfront
- **Expected**: Layer-by-layer upload during execution
- **Solution**: Implement iterative layer extraction and upload

## Proposed API Changes

### Modified CLI Upload Endpoint
```python
@router.post("/cli/upload/{drum_id}")
async def upload_cli_file_to_drum(
    drum_id: int = Path(..., ge=0, le=2),
    file: UploadFile = File(...),
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
):
    """Upload CLI file to specific drum cache."""
```

### Simplified Job Start Endpoint  
```python
@router.post("/multimaterial-job/start")
async def start_multimaterial_job(
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
):
    """Start print job using cached CLI files."""
```

## Service Implementation Requirements

### MultiMaterialJobService Changes
```python
class MultiMaterialJobService:
    def __init__(self):
        self.drum_cli_cache = {0: None, 1: None, 2: None}  # drum_id -> ParsedCliFile
        
    def cache_cli_file(self, drum_id: int, parsed_file: ParsedCliFile):
        """Cache CLI file for specific drum."""
        
    def start_layer_by_layer_job(self):
        """Start iterative layer-by-layer printing."""
        
    def process_next_layer(self, layer_index: int):
        """Upload and print specific layer for all drums."""
        
    def monitor_print_completion(self):
        """Poll status until layer printing completes."""
```

## Frontend Integration

### Expected UI Flow
1. **File Upload Section**: Three drum cards with individual upload buttons
2. **Job Control Section**: Single "Start Print Job" button (parameter-free)
3. **Status Display**: Real-time progress showing current layer and overall progress

### Button Wiring Verification
- **Upload Buttons**: Should call `/cli/upload/{drum_id}` with specific drum targeting
- **Start Button**: Should call `/multimaterial-job/start` without parameters
- **Status Updates**: Should poll `/multimaterial-job/status` for progress tracking

## Layer Management Strategy

### Empty Layer Handling
- **Source**: `backend/app/templates/empty_layer.cli`
- **Usage**: Fill missing layers when drums have different layer counts
- **Logic**: `max_layers = max(len(file.layers) for file in cached_files)`

### Layer Iteration Example
```python
for layer_index in range(max_layers):
    for drum_id in [0, 1, 2]:
        if drum_id in cached_files and layer_index < len(cached_files[drum_id].layers):
            layer_data = extract_layer(cached_files[drum_id], layer_index)
        else:
            layer_data = load_empty_layer_template()
        
        upload_drum_geometry(drum_id, layer_data)
    
    start_print_job()
    wait_for_layer_completion()
```

## Status Flow States

1. **idle**: No active job, ready for file uploads
2. **uploading**: Currently uploading layer data to drums  
3. **printing**: Hardware actively printing current layer
4. **ready**: Layer completed, ready for next iteration
5. **completed**: All layers processed successfully
6. **error**: Recoverable error state requiring operator intervention

## Implementation Priority

1. **High**: Fix file upload drum targeting
2. **High**: Simplify job start to parameter-free
3. **Medium**: Implement layer-by-layer iteration logic
4. **Medium**: Add proper empty layer handling
5. **Low**: Enhanced status reporting and error recovery

This workflow ensures intuitive operation while maintaining robust multi-material coordination capabilities.

What was typed:
"
Here is my expected workflow:
1. User uploads CLI file to 3 individual drums using #sym:upload_cli_file , but this function appears to be missing the target drum.
2. From the attached image, there is one upload button attached to each drum / within the drum card in #file:PrintView.vue and related frontend components
3. Hence, when the operator presses upload in the respective drum's card, #file:multimaterial_job_service.py should cache the file for the respective drum. There should be a max capacity of 3 drums.
4. If operator does not upload file, thats fine, we just repeatedly upload #file:empty_layer.cli into the empty drum for the remainder or rest of the print job. This is also the process if the 3 files have different layer numbers, we just iterate until the max layer of the 3. If the lesser drums run out of layers, just repeatedly upload #file:empty_layer.cli . I think this is the existing process if im not wrong.
5. Now that the 3 CLI files are cached, once operator presses "Start Print Job", #file:multimaterial_job_service.py should upload layer[0] of the cached files for all 3 drums respectively using #sym:upload_drum_geometry. Take note that there should be 2s delay between each layer upload/API call to the recoater server to prevent overloading.
6. #file:multimaterial_job_service.py should then use #sym:start_print_job API call to the recoater server to start the print for the first layer. Unseen to us, the status of the hardware automatically transition over to 'printing' mode if there are no errors.
7. After the print has started, we should poll, with a 2s interval, using #sym:get_multimaterial_job_status API call to the recoater server to see whether the status has transitioned from 'printing' to 'ready', which is the only way that we would know whether the print for the layer is done.
8. We then continue to do the same thing of upload to all respective drums and start print, then monitor for 'ready' until the max layer between the 3 CLI file has been reached.
9. When done, we can set the opcua variable 'JOB_ACTIVE' to False, maybe in #file:multimaterial_job_service.py ?

We are free to deprecate and delete any general upload function, because our system has 3 drums, so it would be an error to not specify the drum to upload to, or upload to an drum number out of bounds.
"
