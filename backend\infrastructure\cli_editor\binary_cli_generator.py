"""
Binary CLI Generator Module
===========================

This module provides the BinaryCliGenerator mixin for generating binary CLI (Common Layer Interface) files
from layer data. It handles binary format generation with struct packing for single layers or layer ranges.

Key Features:
- Generate binary CLI files for single layers or ranges.
- Support for aligned formats and custom headers.
- Uses struct for binary packing and io for in-memory streams.
- Binary format compatible with legacy CLI parsers.

Dependencies:
- Relies on cli_models for data structures and cli_exceptions for error handling.

Usage:
    class MyClass(BinaryCliGenerator):
        pass
    instance = MyClass()
    cli_bytes = instance.generate_single_layer_cli(layer)
"""
import io
import struct
from typing import List

from .cli_exceptions import CliGenerationError
from .cli_models import CliLayer


class BinaryCliGenerator:
    """Mixin for handling generation of binary CLI files from layer data."""

    def generate_single_layer_cli(self, layer: CliLayer, header_lines: List[str] = None, is_aligned: bool = False) -> bytes:
        """
        Generates CLI file data for a single layer.

        Args:
            layer: The CliLayer object to convert to CLI format
            header_lines: Optional header lines to include (defaults to minimal header)
            is_aligned: Whether to use aligned format (defaults to False)

        Returns:
            A byte string containing the CLI file data for the single layer

        Raises:
            CliGenerationError: If the layer data cannot be serialized
        """
        try:
            stream = io.BytesIO()

            # 1. Write Header
            if header_lines is None:
                header_lines = ["$$HEADEREND"]

            header_str = "\n".join(header_lines)
            if not header_str.endswith("$$HEADEREND"):
                header_str += "\n$$HEADEREND"

            stream.write(header_str.encode('ascii'))

            # 2. Write Layer Command (127)
            stream.write(struct.pack("<H", 127))  # Layer command code
            if is_aligned:
                stream.write(b'\x00\x00')  # Alignment bytes
            stream.write(struct.pack("<f", layer.z_height))  # Z height

            # 3. Write Polylines (130)
            for poly in layer.polylines:
                if len(poly.points) > 0:
                    stream.write(struct.pack("<H", 130))  # Polyline command code
                    if is_aligned:
                        stream.write(b'\x00\x00')  # Alignment bytes

                    # Write polyline header
                    stream.write(struct.pack("<iii", poly.part_id, poly.direction, len(poly.points)))

                    # Write points
                    for point in poly.points:
                        stream.write(struct.pack("<f", point.x))
                        stream.write(struct.pack("<f", point.y))

            # 4. Write Hatches (132)
            for hatch in layer.hatches:
                if len(hatch.lines) > 0:
                    stream.write(struct.pack("<H", 132))  # Hatch command code
                    if is_aligned:
                        stream.write(b'\x00\x00')  # Alignment bytes

                    # Write hatch header
                    stream.write(struct.pack("<ii", hatch.group_id, len(hatch.lines)))

                    # Write hatch lines
                    for line in hatch.lines:
                        stream.write(struct.pack("<ffff", line[0].x, line[0].y, line[1].x, line[1].y))

            return stream.getvalue()

        except Exception as e:
            raise CliGenerationError(f"Failed to generate CLI data for layer: {e}")

    def generate_cli_from_layer_range(self, layers: List[CliLayer], header_lines: List[str] = None, is_aligned: bool = False) -> bytes:
        """
        Generates a complete binary CLI file from a range of layers.

        Args:
            layers: A list of CliLayer objects to include in the file.
            header_lines: Optional header lines. A minimal header is used if not provided.
            is_aligned: Whether to use the aligned format.

        Returns:
            A byte string containing the complete CLI file data.

        Raises:
            CliGenerationError: If the layer list is empty or data cannot be serialized.
        """
        if not layers:
            raise CliGenerationError("Cannot generate CLI data from an empty layer range.")

        try:
            stream = io.BytesIO()

            # 1. Write Header
            if header_lines is None:
                # Provide a default minimal header if none is given
                header_lines = ["$$HEADEREND"]

            # Ensure the header is correctly terminated
            header_str = "\n".join(header_lines)
            if not header_str.endswith("$$HEADEREND"):
                header_str += "\n$$HEADEREND"
            
            stream.write(header_str.encode('ascii'))

            # 2. Write each layer's data
            for layer in layers:
                # Write Layer Command (127)
                stream.write(struct.pack("<H", 127))
                if is_aligned:
                    stream.write(b'\x00\x00')
                stream.write(struct.pack("<f", layer.z_height))

                # Write Polylines (130)
                for poly in layer.polylines:
                    if len(poly.points) > 0:
                        stream.write(struct.pack("<H", 130))
                        if is_aligned:
                            stream.write(b'\x00\x00')
                        stream.write(struct.pack("<iii", poly.part_id, poly.direction, len(poly.points)))
                        for point in poly.points:
                            stream.write(struct.pack("<ff", point.x, point.y))

                # Write Hatches (132)
                for hatch in layer.hatches:
                    if len(hatch.lines) > 0:
                        stream.write(struct.pack("<H", 132))
                        if is_aligned:
                            stream.write(b'\x00\x00')
                        stream.write(struct.pack("<ii", hatch.group_id, len(hatch.lines)))
                        for line in hatch.lines:
                            stream.write(struct.pack("<ffff", line[0].x, line[0].y, line[1].x, line[1].y))

            return stream.getvalue()

        except Exception as e:
            raise CliGenerationError(f"Failed to generate CLI data for layer range: {e}")
