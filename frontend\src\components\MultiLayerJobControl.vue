<template>
  <div class="multi-layer-job-control">
    <div class="control-card">
      <div class="card-header">
        <h3 class="card-title heading-lg">Multi-Material Job Control</h3>
        <div class="job-status-indicator">
          <div 
            :class="[
              'status-dot',
              getJobStatusClass()
            ]"
          ></div>
          <span class="status-text">{{ getJobStatusText() }}</span>
        </div>
      </div>

      <div class="card-content">
        <!-- File Upload Section -->
        <div class="file-upload-section">
          <h4 class="section-title">CLI File Upload (3 Drums)</h4>
          <div class="drum-upload-grid">
            <div 
              v-for="drumId in [0, 1, 2]" 
              :key="drumId"
              class="drum-upload-item"
            >
              <div class="drum-header">
                <span class="drum-label">Drum {{ drumId }}</span>
                <div 
                  :class="[
                    'drum-status-dot',
                    getDrumStatusClass(drumId)
                  ]"
                ></div>
              </div>
              
              <div class="file-upload-area">
                <input
                  :id="`file-input-${drumId}`"
                  type="file"
                  accept=".cli"
                  @change="handleFileUpload($event, drumId)"
                  :disabled="printJobStore.isJobActive || isUploading"
                  class="file-input"
                />
                <label 
                  :for="`file-input-${drumId}`"
                  class="file-upload-label"
                  :class="{ 'disabled': printJobStore.isJobActive || isUploading }"
                >
                  <div v-if="printJobStore.uploadedFiles[drumId]" class="file-info">
                    <div class="file-name">{{ printJobStore.uploadedFiles[drumId].fileName }}</div>
                    <div class="file-details">
                      {{ printJobStore.uploadedFiles[drumId].layerCount }} layers
                    </div>
                  </div>
                  <div v-else class="upload-prompt">
                    <span class="upload-icon">📁</span>
                    <span>Choose CLI file</span>
                  </div>
                </label>
              </div>

              <div v-if="uploadErrors[drumId]" class="error-message">
                {{ uploadErrors[drumId] }}
              </div>
            </div>
          </div>
        </div>

        <!-- Job Controls -->
        <div class="job-controls-section">
          <div class="control-buttons">
            <button
              @click="startJob"
              :disabled="!printJobStore.canStartJob || printJobStore.isStartingJob"
              class="btn btn-primary"
              data-testid="start-multimaterial-job-btn"
            >
              <span v-if="printJobStore.isStartingJob">Starting...</span>
              <span v-else>Start Multi-Material Job</span>
            </button>

            <button
              @click="cancelJob"
              :disabled="!printJobStore.hasActiveJob || printJobStore.isCancellingJob"
              class="btn btn-danger"
              data-testid="cancel-multimaterial-job-btn"
            >
              <span v-if="printJobStore.isCancellingJob">Cancelling...</span>
              <span v-else>Cancel Job</span>
            </button>

            <button
              @click="refreshStatus"
              :disabled="isRefreshing"
              class="btn btn-tertiary"
              data-testid="refresh-job-status-btn"
            >
              <span v-if="isRefreshing">Refreshing...</span>
              <span v-else>Refresh Status</span>
            </button>
          </div>

          <!-- Requirements Check -->
          <div class="requirements-check">
            <div class="requirement-item">
              <span :class="['requirement-icon', printJobStore.allFilesUploaded ? 'success' : 'pending']">
                {{ printJobStore.allFilesUploaded ? '✓' : '○' }}
              </span>
              <span class="requirement-text">All 3 CLI files uploaded</span>
            </div>
            <div class="requirement-item">
              <span :class="['requirement-icon', !printJobStore.hasErrors ? 'success' : 'error']">
                {{ !printJobStore.hasErrors ? '✓' : '✗' }}
              </span>
              <span class="requirement-text">No system errors</span>
            </div>
            <div class="requirement-item">
              <span :class="['requirement-icon', !printJobStore.isJobActive ? 'success' : 'pending']">
                {{ !printJobStore.isJobActive ? '✓' : '○' }}
              </span>
              <span class="requirement-text">No active job running</span>
            </div>
          </div>
        </div>

        <!-- Messages -->
        <div v-if="successMessage" class="success-message" data-testid="success-message">
          {{ successMessage }}
        </div>
        <div v-if="errorMessage" class="error-message" data-testid="error-message">
          {{ errorMessage }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { usePrintJobStore } from '../stores/printJobStore'
import { useStatusStore } from '../stores/status'
import apiService from '../services/api'

export default {
  name: 'MultiLayerJobControl',
  setup() {
    const printJobStore = usePrintJobStore()
    const statusStore = useStatusStore()
    
    // Local state
    const isUploading = ref(false)
    const isRefreshing = ref(false)
    const uploadErrors = ref({ 0: '', 1: '', 2: '' })
    const successMessage = ref('')
    const errorMessage = ref('')
    
    // Auto-refresh interval
    let refreshInterval = null

    // Methods
    const showMessage = (message, isError = false) => {
      if (isError) {
        errorMessage.value = message
        successMessage.value = ''
      } else {
        successMessage.value = message
        errorMessage.value = ''
      }
      
      // Auto-clear messages after 5 seconds
      setTimeout(() => {
        successMessage.value = ''
        errorMessage.value = ''
      }, 5000)
    }

    const clearUploadError = (drumId) => {
      uploadErrors.value[drumId] = ''
    }

    const handleFileUpload = async (event, drumId) => {
      const file = event.target.files[0]
      if (!file) return

      // Validate file type
      if (!file.name.toLowerCase().endsWith('.cli')) {
        uploadErrors.value[drumId] = 'Please select a .cli file'
        return
      }

      clearUploadError(drumId)
      isUploading.value = true

      try {
        // Use drum-specific upload endpoint
        const response = await apiService.uploadCliFileToDrum(file, drumId)
        
        if (response.data.success) {
          printJobStore.setFileUploaded(drumId, {
            fileId: response.data.file_id,
            fileName: file.name,
            layerCount: response.data.total_layers
          })

          // Also track in File Management display
          printJobStore.setLastUploadedFile(drumId, file.name, 'direct')

          showMessage(`File uploaded successfully for Drum ${drumId}`)
        } else {
          uploadErrors.value[drumId] = response.data.message || 'Upload failed'
        }
      } catch (error) {
        console.error(`File upload failed for drum ${drumId}:`, error)
        uploadErrors.value[drumId] = error.response?.data?.detail || 'Upload failed'
      } finally {
        isUploading.value = false
        // Clear the input
        event.target.value = ''
      }
    }

    const startJob = async () => {
      try {
        // Use the unified multimaterial job endpoint with no file_ids to trigger drum cache workflow
        const result = await apiService.startMultiMaterialJob()
        showMessage('Layer-by-layer printing job started successfully')
      } catch (error) {
        console.error('Failed to start job:', error)
        showMessage(error.response?.data?.detail || error.message || 'Failed to start job', true)
      }
    }

    const cancelJob = async () => {
      try {
        const result = await printJobStore.cancelMultiMaterialJob()
        showMessage('Job cancelled successfully')
        // File clearing is now handled automatically in the store
      } catch (error) {
        console.error('Failed to cancel job:', error)
        showMessage(error.message || 'Failed to cancel job', true)
      }
    }

    const refreshStatus = async () => {
      isRefreshing.value = true
      try {
        await printJobStore.fetchJobStatus()
        showMessage('Status refreshed')
      } catch (error) {
        console.error('Failed to refresh status:', error)
        showMessage('Failed to refresh status', true)
      } finally {
        isRefreshing.value = false
      }
    }

    const getJobStatusClass = () => {
      const status = printJobStore.multiMaterialJob.status
      if (status === 'printing') return 'status-active'
      if (status === 'error') return 'status-error'
      if (status === 'uploading') return 'status-uploading'
      return 'status-ready'
    }

    const getJobStatusText = () => {
      const status = printJobStore.multiMaterialJob.status
      switch (status) {
        case 'printing': return 'Printing'
        case 'uploading': return 'Uploading'
        case 'waiting': return 'Waiting'
        case 'completed': return 'Completed'
        case 'error': return 'Error'
        default: return 'Ready'
      }
    }

    const getDrumStatusClass = (drumId) => {
      const drum = printJobStore.multiMaterialJob.drums[drumId]
      const hasFile = printJobStore.uploadedFiles[drumId] !== null

      if (drum.errorMessage) return 'status-error'
      if (hasFile && drum.status === 'ready') return 'status-ready'
      if (hasFile) return 'status-uploaded'
      return 'status-idle'
    }

    // Lifecycle
    onMounted(() => {
      // Start auto-refresh for job status
      refreshInterval = setInterval(() => {
        if (printJobStore.hasActiveJob) {
          printJobStore.fetchJobStatus().catch(console.error)
        }
      }, 5000) // Refresh every 5 seconds
    })

    onUnmounted(() => {
      if (refreshInterval) {
        clearInterval(refreshInterval)
      }
    })

    return {
      printJobStore,
      statusStore,
      isUploading,
      isRefreshing,
      uploadErrors,
      successMessage,
      errorMessage,
      handleFileUpload,
      startJob,
      cancelJob,
      refreshStatus,
      getJobStatusClass,
      getJobStatusText,
      getDrumStatusClass
    }
  }
}
</script>

<style scoped>
.multi-layer-job-control {
  margin-bottom: 2rem;
}

.control-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  background: #f8f9fa;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  margin: 0;
  color: #2c3e50;
}

.job-status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-ready { background-color: #28a745; }
.status-active { background-color: #007bff; }
.status-uploading { background-color: #ffc107; }
.status-error { background-color: #dc3545; }
.status-idle { background-color: #6c757d; }
.status-uploaded { background-color: #17a2b8; }

.card-content {
  padding: 1.5rem;
}

.file-upload-section {
  margin-bottom: 2rem;
}

.section-title {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
}

.drum-upload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.drum-upload-item {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
}

.drum-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.drum-label {
  font-weight: 600;
  color: #495057;
}

.drum-status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.file-input {
  display: none;
}

.file-upload-label {
  display: block;
  padding: 1rem;
  border: 2px dashed #dee2e6;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.file-upload-label:hover:not(.disabled) {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.file-upload-label.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.file-info {
  color: #495057;
}

.file-name {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.file-details {
  font-size: 0.875rem;
  color: #6c757d;
}

.upload-prompt {
  color: #6c757d;
}

.upload-icon {
  display: block;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.job-controls-section {
  border-top: 1px solid #e9ecef;
  padding-top: 1.5rem;
}

.control-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 120px;
}

.btn:disabled {
  background-color: #e9ecef !important;
  color: #6c757d !important;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-tertiary {
  background-color: #6c757d;
  color: white;
}

.btn-tertiary:hover:not(:disabled) {
  background-color: #545b62;
}

.requirements-check {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.requirement-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.requirement-icon {
  width: 20px;
  text-align: center;
  font-weight: bold;
}

.requirement-icon.success {
  color: #28a745;
}

.requirement-icon.error {
  color: #dc3545;
}

.requirement-icon.pending {
  color: #6c757d;
}

.requirement-text {
  color: #495057;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 0.75rem;
  border-radius: 4px;
  margin-top: 1rem;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 4px;
  margin-top: 1rem;
}

@media (max-width: 768px) {
  .drum-upload-grid {
    grid-template-columns: 1fr;
  }
  
  .control-buttons {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}
</style>
