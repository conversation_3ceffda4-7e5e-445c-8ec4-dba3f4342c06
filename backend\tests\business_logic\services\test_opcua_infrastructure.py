"""
Unit Tests for OPC UA Service (post-refactor)
============================================

Tests the unified OPCUAService and configuration components.
"""

import pytest
import pytest_asyncio
import asyncio
from unittest.mock import Mock

from app.config.opcua_config import (
    OPCUAServerConfig,
    CoordinationVariable,
    get_opcua_config,
    get_variable_by_name,
    COORDINATION_VARIABLES,
)
from app.services.opcua import OPCUAService, opcua_service


class TestOPCUAConfig:
    def test_opcua_server_config_defaults(self):
        config = OPCUAServerConfig()
        assert config.endpoint.startswith("opc.tcp://")
        assert config.namespace_idx == 2
        assert config.security_policy == "None"
        assert config.auto_restart is True

    def test_coordination_variable_creation(self):
        var = CoordinationVariable(
            name="test_var",
            node_id="ns=2;s=test_var",
            data_type="Boolean",
            initial_value=False,
            description="Test variable",
        )
        assert var.name == "test_var"
        assert var.node_id == "ns=2;s=test_var"
        assert var.data_type == "Boolean"
        assert var.initial_value is False
        assert var.writable is True

    def test_get_variable_by_name(self):
        var = get_variable_by_name("job_active")
        assert var.name == "job_active"
        assert var.data_type == "Boolean"
        assert var.initial_value is False

    def test_coordination_variables_completeness(self):
        required = {"job_active","total_layers","current_layer","recoater_ready_to_print","recoater_layer_complete","backend_error","plc_error"}
        defined = {v.name for v in COORDINATION_VARIABLES}
        assert required.issubset(defined)


@pytest.mark.asyncio
class TestOPCUAService:
    @pytest_asyncio.fixture
    async def service(self):
        svc = OPCUAService()
        yield svc
        # Cleanup after each test
        try:
            await svc.disconnect()
        except Exception:
            pass

    async def test_connect_and_disconnect(self, service):
        assert not service.is_server_running
        ok = await service.connect()
        assert ok is True
        assert service.is_server_running
        assert service.is_connected() is True
        ok2 = await service.disconnect()
        assert ok2 is True
        assert service.is_connected() is False

    async def test_write_read_variables(self, service):
        await service.connect()
        ok = await service.write_variable("job_active", True)
        assert ok is True
        val = await service.read_variable("job_active")
        assert val is True

    async def test_job_helpers(self, service):
        await service.connect()
        ok = await service.set_job_active(10)
        assert ok is True
        assert await service.read_variable("job_active") is True
        assert await service.read_variable("total_layers") == 10
        assert await service.read_variable("current_layer") == 1
        await service.update_layer_progress(3)
        assert await service.read_variable("current_layer") == 3
        await service.set_job_inactive()
        assert await service.read_variable("job_active") is False

    async def test_error_flags_and_clear(self, service):
        await service.connect()
        await service.set_backend_error(True)
        await service.set_plc_error(True)
        assert service.get_backend_error() is True
        assert service.get_plc_error() is True
        await service.clear_error_flags()
        assert service.get_backend_error() is False
        assert service.get_plc_error() is False

    async def test_subscribe_to_changes(self, service):
        await service.connect()
        calls = []
        async def handler(name, value):
            calls.append((name, value))
        await service.subscribe_to_changes(["current_layer"], handler)
        await service.update_layer_progress(5)
        # Allow event loop to run handler
        await asyncio.sleep(0)
        assert ("current_layer", 5) in calls

    async def test_get_server_status(self, service):
        status = service.get_server_status()
        assert set(["connected","server_running","endpoint","namespace","variable_count"]).issubset(status.keys())


@pytest.mark.asyncio
class TestSingletonOPCUAService:
    async def test_singleton_connects(self):
        ok = await opcua_service.connect()
        assert ok is True
        assert opcua_service.is_connected() is True
        await opcua_service.disconnect()
