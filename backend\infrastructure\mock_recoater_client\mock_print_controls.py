"""
Mock Print Control Methods
==========================

This module contains mock print control methods for the MockRecoaterClient.
These methods simulate the print control endpoints from the hardware API.
"""

import time
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class MockPrintControlMixin:
    """Mixin class providing mock print control methods for MockRecoaterClient."""

    def get_layer_parameters(self) -> Dict[str, Any]:
        """
        Mock implementation for getting layer parameters.

        Returns:
            Mock layer parameters
        """
        logger.info("Mock get layer parameters")

        return {
            "filling_id": 1,
            "speed": 30.0,
            "powder_saving": True,
            "x_offset": 0.0,
            "max_x_offset": 100.0
        }

    def set_layer_parameters(self, filling_id: int, speed: float, powder_saving: bool = True, x_offset: float = None) -> Dict[str, Any]:
        """
        Mock implementation for setting layer parameters.

        Args:
            filling_id: The ID of the drum with the filling material powder
            speed: The patterning speed [mm/s]
            powder_saving: Flag indicating if powder saving strategies are used
            x_offset: The offset along the X axis [mm]

        Returns:
            Mock success response
        """
        logger.info(f"Mock set layer parameters: filling_id={filling_id}, speed={speed}, powder_saving={powder_saving}, x_offset={x_offset}")

        return {
            "success": True,
            "filling_id": filling_id,
            "speed": speed,
            "powder_saving": powder_saving,
            "x_offset": x_offset
        }

    def get_layer_preview(self) -> bytes:
        """
        Mock implementation for getting layer preview.
        Returns a color-coded preview showing the current layer configuration
        with proper drum colors matching the Legend component.

        Returns:
            Mock PNG image data as bytes with color-coded geometry
        """
        logger.info("Mock get layer preview - generating color-coded preview")

        try:
            # Import here to avoid circular imports
            from infrastructure.cli_editor.editor import Editor

            # Create CLI parser and generate color-coded preview
            parser = Editor(logger=logger)
            png_bytes = parser.render_layer_configuration_preview(width=800, height=600)

            logger.info("Generated color-coded layer configuration preview")
            return png_bytes

        except Exception as e:
            logger.error(f"Failed to generate color-coded preview: {e}")
            # Fallback to simple PNG if generation fails
            mock_png = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
            return mock_png

    def start_print_job(self) -> Dict[str, Any]:
        """
        Start a mock print of a single layer. Transitions to 'printing' and
        schedules auto-completion to 'ready' after self._layer_time_sec unless
        an injected failure is configured.
        """
        logger.info("Mock start print job")

        # Do not start if currently in error
        if getattr(self, "_print_job_state", "ready") == "error":
            return {"success": False, "error": getattr(self, "_error_reason", "mock_error")}

        # Require that some CLI data has been uploaded in this cycle (dev parity)
        try:
            has_any_upload = isinstance(getattr(self, "_drum_geometries", None), dict) and len(self._drum_geometries) > 0
        except Exception:
            has_any_upload = False
        if not has_any_upload:
            return {"success": False, "error": "No CLI data uploaded to any drum"}

        # Update print job state
        self._print_job_state = "printing"
        self._print_job_id = f"job_{int(time.time())}"
        self._print_job_start_time = time.time()
        # Timer for layer printing
        try:
            layer_time = float(getattr(self, "_layer_time_sec", 4.0))
        except Exception:
            layer_time = 4.0
        self._printing_until = self._print_job_start_time + layer_time

        return {
            "success": True,
            "job_id": self._print_job_id,
            "status": "started"
        }

    def cancel_print_job(self) -> Dict[str, Any]:
        """
        Cancel the current mock print job and return to 'ready'.
        """
        logger.info("Mock cancel print job")

        # Update print job state
        self._print_job_state = "ready"
        self._print_job_id = None
        self._print_job_start_time = None
        self._printing_until = None

        return {"success": True, "status": "cancelled"}

    def get_print_job_status(self) -> Dict[str, Any]:
        """
        Return the current print job status using the same state names as
        get_state(). Does not auto-transition; timing is handled in get_state().
        """
        logger.info("Mock get print job status")
        current_state = getattr(self, "_print_job_state", "ready")
        return {
            "state": current_state,
            "is_printing": current_state == "printing",
            "has_error": current_state == "error",
            "error_reason": getattr(self, "_error_reason", "")
        }

    def clear_print_error(self) -> Dict[str, Any]:
        """Clear any mock print error and return to 'ready' state (dev convenience)."""
        logger.info("Mock clear_print_error called")
        # Reset error state and allow next print cycle to proceed
        self._print_job_state = "ready"
        self._error_reason = ""
        self._printing_until = None
        return {"success": True, "status": "cleared"}
