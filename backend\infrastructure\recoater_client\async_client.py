"""
Async Client Methods
===================

This module contains async wrapper methods for the RecoaterClient.
These methods support Stage 3 coordination logic for multi-drum operations.

!!! Some of the methods in this script should NOT be here. !!!
Refactor methods like get_multimaterial_status() and monitor_drum_state_transitions()
into the service layer (E.g. app/services/multilayer_job_manager.py)
"""


import asyncio
import logging
from typing import Dict, Any

from .exceptions import RecoaterConnectionError

logger = logging.getLogger(__name__)


class AsyncClientMixin:
    """Mixin class providing async methods for RecoaterClient."""

    async def upload_cli_data(self, drum_id: int, cli_data: bytes) -> Dict[str, Any]:
        """
        Upload CLI data to a specific drum (async wrapper).

        Args:
            drum_id: Target drum (0, 1, or 2)
            cli_data: ASCII CLI data to upload

        Returns:
            Response from the API

        Raises:
            RecoaterConnectionError: If connection fails
            RecoaterAPIError: If API returns error
        """
        # Use asyncio.to_thread to make the synchronous call async
        return await asyncio.to_thread(
            self.upload_drum_geometry,
            drum_id,
            cli_data,
            "application/octet-stream"
        )

    async def get_drum_status(self, drum_id: int) -> Dict[str, Any]:
        """
        Get status for a specific drum (async wrapper).

        Args:
            drum_id: Target drum (0, 1, or 2)

        Returns:
            Dictionary containing drum status with 'ready' field

        Raises:
            RecoaterConnectionError: If connection fails
            RecoaterAPIError: If API returns error
        """
        # Get drum information
        drum_info = await asyncio.to_thread(self.get_drum, drum_id)

        # Parse status to determine if drum is ready
        # This is a simplified implementation - actual logic would depend on hardware API
        state = drum_info.get("state", "unknown")
        ready = state in ["ready", "idle", "standby"]

        return {
            "drum_id": drum_id,
            "state": state,
            "ready": ready,
            "raw_info": drum_info
        }




