"""
Blade control endpoints:
- GET /drums/{drum_id}/blade/screws: Get blade screws info.
- GET /drums/{drum_id}/blade/screws/motion: Get blade screws motion.
- POST /drums/{drum_id}/blade/screws/motion: Set blade screws motion.
- DELETE /drums/{drum_id}/blade/screws/motion: Cancel blade screws motion.
- GET /drums/{drum_id}/blade/screws/{screw_id}: Get specific blade screw info.
- GET /drums/{drum_id}/blade/screws/{screw_id}/motion: Get specific blade screw motion.
- POST /drums/{drum_id}/blade/screws/{screw_id}/motion: Set specific blade screw motion.
- DELETE /drums/{drum_id}/blade/screws/{screw_id}/motion: Cancel specific blade screw motion.
"""

from fastapi import APIRouter, HTTPException, Depends, Path
from typing import Dict, Any
import logging

from infrastructure.recoater_client import RecoaterClient, RecoaterConnectionError, RecoaterAPIError
from app.dependencies import get_recoater_client
from .models import BladeMotionRequest, BladeIndividualMotionRequest

logger = logging.getLogger(__name__)

router = APIRouter(tags=["recoater"])

# Blade Control Endpoints

@router.get("/drums/{drum_id}/blade/screws")
async def get_blade_screws_info(
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get information about the two screws of the specified drum's scraping blade.

    Args:
        drum_id: The drum's ID

    Returns:
        Dictionary containing blade screws information

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting blade screws info for drum {drum_id}")

        response_data = client.get_blade_screws_info(drum_id)

        response = {
            "drum_id": drum_id,
            "blade_screws": response_data,
            "connected": True
        }

        logger.info(f"Blade screws info retrieved successfully for drum {drum_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting blade screws info for drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error getting blade screws info for drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting blade screws info for drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/drums/{drum_id}/blade/screws/motion")
async def get_blade_screws_motion(
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the current motion command for the blade screws.

    Args:
        drum_id: The drum's ID

    Returns:
        Dictionary containing current motion information

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting blade screws motion for drum {drum_id}")

        response_data = client.get_blade_screws_motion(drum_id)

        response = {
            "drum_id": drum_id,
            "motion": response_data,
            "connected": True
        }

        logger.info(f"Blade screws motion retrieved successfully for drum {drum_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error getting blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/drums/{drum_id}/blade/screws/motion")
async def set_blade_screws_motion(
    motion_request: BladeMotionRequest,
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Create a motion command for the blade screws.

    Args:
        drum_id: The drum's ID
        motion_request: Motion parameters (mode, distance)

    Returns:
        Dictionary containing motion command response

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Setting blade screws motion for drum {drum_id}: {motion_request}")

        response_data = client.set_blade_screws_motion(
            drum_id=drum_id,
            mode=motion_request.mode,
            distance=motion_request.distance
        )

        response = {
            "drum_id": drum_id,
            "motion_command": motion_request.model_dump(),
            "response": response_data,
            "connected": True
        }

        logger.info(f"Blade screws motion command set successfully for drum {drum_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error setting blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error setting blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/drums/{drum_id}/blade/screws/motion")
async def cancel_blade_screws_motion(
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Cancel the current motion command for the blade screws.

    Args:
        drum_id: The drum's ID

    Returns:
        Dictionary containing cancellation response

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Cancelling blade screws motion for drum {drum_id}")

        response_data = client.cancel_blade_screws_motion(drum_id)

        response = {
            "drum_id": drum_id,
            "action": "motion_cancelled",
            "response": response_data,
            "connected": True
        }

        logger.info(f"Blade screws motion cancelled successfully for drum {drum_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error cancelling blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error cancelling blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error cancelling blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/drums/{drum_id}/blade/screws/{screw_id}")
async def get_blade_screw_info(
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    screw_id: int = Path(..., ge=0, description="The screw's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get information about a specific screw of the drum's scraping blade.

    Args:
        drum_id: The drum's ID
        screw_id: The screw's ID

    Returns:
        Dictionary containing blade screw information

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting blade screw info for drum {drum_id}, screw {screw_id}")

        response_data = client.get_blade_screw_info(drum_id, screw_id)

        response = {
            "drum_id": drum_id,
            "screw_id": screw_id,
            "screw_info": response_data,
            "connected": True
        }

        logger.info(f"Blade screw info retrieved successfully for drum {drum_id}, screw {screw_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting blade screw info for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error getting blade screw info for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting blade screw info for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/drums/{drum_id}/blade/screws/{screw_id}/motion")
async def get_blade_screw_motion(
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    screw_id: int = Path(..., ge=0, description="The screw's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the current motion command for a specific blade screw.

    Args:
        drum_id: The drum's ID
        screw_id: The screw's ID

    Returns:
        Dictionary containing current motion information

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting blade screw motion for drum {drum_id}, screw {screw_id}")

        response_data = client.get_blade_screw_motion(drum_id, screw_id)

        response = {
            "drum_id": drum_id,
            "screw_id": screw_id,
            "motion": response_data,
            "connected": True
        }

        logger.info(f"Blade screw motion retrieved successfully for drum {drum_id}, screw {screw_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error getting blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/drums/{drum_id}/blade/screws/{screw_id}/motion")
async def set_blade_screw_motion(
    motion_request: BladeIndividualMotionRequest,
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    screw_id: int = Path(..., ge=0, description="The screw's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Create a motion command for a specific blade screw.

    Args:
        drum_id: The drum's ID
        screw_id: The screw's ID
        motion_request: Motion parameters (distance)

    Returns:
        Dictionary containing motion command response

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Setting blade screw motion for drum {drum_id}, screw {screw_id}: {motion_request}")

        response_data = client.set_blade_screw_motion(
            drum_id=drum_id,
            screw_id=screw_id,
            distance=motion_request.distance
        )

        response = {
            "drum_id": drum_id,
            "screw_id": screw_id,
            "motion_command": motion_request.model_dump(),
            "response": response_data,
            "connected": True
        }

        logger.info(f"Blade screw motion command set successfully for drum {drum_id}, screw {screw_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error setting blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error setting blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/drums/{drum_id}/blade/screws/{screw_id}/motion")
async def cancel_blade_screw_motion(
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    screw_id: int = Path(..., ge=0, description="The screw's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Cancel the current motion command for a specific blade screw.

    Args:
        drum_id: The drum's ID
        screw_id: The screw's ID

    Returns:
        Dictionary containing cancellation response

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Cancelling blade screw motion for drum {drum_id}, screw {screw_id}")

        response_data = client.cancel_blade_screw_motion(drum_id, screw_id)

        response = {
            "drum_id": drum_id,
            "screw_id": screw_id,
            "action": "motion_cancelled",
            "response": response_data,
            "connected": True
        }

        logger.info(f"Blade screw motion cancelled successfully for drum {drum_id}, screw {screw_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error cancelling blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error cancelling blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error cancelling blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
