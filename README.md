# Aerosint Recoater HMI

The Aerosint Recoater HMI is a modern, intuitive, and reliable web-based Human-Machine Interface (HMI) for the Aerosint SPD Recoater system. It is designed to replace the default SwaggerUI, providing a more user-friendly experience for operators, developers, and stakeholders.

This HMI provides a comprehensive suite of tools for managing all aspects of the recoater's operation, from individual component control to executing a full multi-layer print job. It is built with a robust backend/frontend architecture to ensure high uptime and a responsive user experience, making it suitable for both industrial and laboratory settings.

![Main Interface](docs/images/main-interface.png)

## Features

The Recoater HMI offers a wide range of features to streamline the operation of the Aerosint SPD Recoater:

### Real-time Monitoring
*   **Connection Status:** A clear and prominent indicator shows the connection status to the backend and recoater hardware.
*   **Live Data:** The HMI provides real-time updates for system state, process parameters, and error conditions via WebSockets.

### Recoater Dashboard
*   **Drum Controls:** Monitor and control each of the three drums, including position, motion, and pressure.
*   **Hopper Controls:** Manage the scraping blades with both collective and individual screw control.
*   **Leveler Control:** Monitor and set the powder leveler's pressure and sensor state.

### Print Control
*   **Layer Parameters:** Define and save the parameters for the current layer, including filling drum, patterning speed, and offsets.
*   **Layer Preview:** Visualize the layer geometry with color-coded previews for each drum.
*   **File Management:** Upload, download, and delete PNG or CLI geometry files on the recoater drums.

### Multi-Layer CLI Workflow
*   **Upload & Parse:** Upload multi-layer CLI files (ASCII or binary) and parse them in the backend.
*   **Layer Preview:** Preview individual layers from the parsed CLI file.
*   **Send to Recoater:** Send a single layer or a range of layers to a specific drum.

### Configuration Management
*   **System-wide Settings:** Configure persistent hardware parameters, such as build space dimensions, resolution, and drum gaps.

## Technology Stack

The Recoater HMI is built with a modern technology stack to ensure performance, reliability, and maintainability.

### Backend
*   **Framework:** FastAPI
*   **Language:** Python 3.9+
*   **Communication:** WebSockets, OPC UA
*   **Image Processing:** Pillow

### Frontend
*   **Framework:** Vue.js 3
*   **State Management:** Pinia
*   **HTTP Client:** Axios
*   **Build Tool:** Vite

## Architecture Overview

The system follows a classic client-server architecture with a clear separation of concerns:

*   **Frontend:** A Vue.js single-page application (SPA) that runs in the user's web browser. It communicates with the backend via a REST API and WebSockets.
*   **Backend:** A FastAPI application that serves the frontend, provides the REST API, and communicates with the recoater hardware.
*   **Recoater Hardware:** The physical recoater system, which is controlled by the backend via a REST API.

```mermaid
graph TD
    A[User] -->|Interacts with| B[Web Browser]
    B -->|REST API / WebSocket| C[FastAPI Backend]
    C -->|HTTP REST API| D[Recoater Hardware Server]
    D -->|Hardware Bus| E[Recoater Hardware]
    C -->|Mock Mode| F[Mock Recoater Client]
```

## Getting Started

Follow these instructions to set up and run the Recoater HMI on your local machine.

### Prerequisites
*   Node.js v16+
*   Python v3.9+
*   A running and accessible Aerosint Recoenter code.

### Installation & Running

1.  **Configure Backend:**
    ```bash
    cd backend
    python -m venv venv
    source venv/bin/activate  # On Windows: venv\Scripts\activate
    pip install -r requirements.txt
    cp .env.example .env
    # Edit .env and set the RECOATER_API_ADDRESS
    ```

2.  **Configure Frontend:**
    ```bash
    cd frontend
    npm install
    ```

3.  **Run the Application:**
    *   In one terminal, run the backend:
        ```bash
        cd backend
        uvicorn app.main:app --reload
        ```
    *   In another terminal, run the frontend:
        ```bash
        cd frontend
        npm run dev
        ```

4.  **Access the HMI:**
    Open your web browser (Firefox, Chrome, or Edge) and navigate to the address provided by the Vite development server (usually `http://localhost:5173`).

### Troubleshooting
*   **Connection Issues:** Ensure the recoater hardware is turned on and accessible over the network. Check the `RECOATER_API_ADDRESS` in the `.env` file.
*   **Dependency Errors:** If you encounter errors during installation, make sure you are using the correct versions of Node.js and Python.

## Usage

The Recoater HMI is designed to be intuitive and easy to use. Here are some common operator workflows:

### Production Workflow
1.  Upload a geometry file for each drum using the **File Management** section.
2.  Set the layer parameters in the **Layer Parameters** section.
3.  Start the print job using the **Print Job Management** section.
4.  Monitor the job progress and system status in the **Job Progress Display** and **Status View**.
5.  If necessary, cancel the job or clear any errors.

### Preview/Development Workflow
1.  Upload a multi-layer CLI file in the **CLI Layer Preview** section.
2.  Preview individual layers to validate the geometry.
3.  Send a single layer or a range of layers to a drum.
4.  Use the preview to validate the layer before running a full print job.

## Development Workflow

This section provides guidance for developers who want to work on the Recoater HMI.

### Running Tests
To run the backend tests, use the following command:
```bash
pytest -q backend
```

### Code Conventions
*   **Circular Imports:** Avoid circular imports by using the dependency injection system in `app/dependencies.py`.
*   **Drum Limit:** The system is designed for a a maximum of 3 drums. This should be enforced on both the backend and frontend.
*   **Styling:** Use card-based UI elements and follow the existing styling conventions.

### Key Code Locations
*   **API Endpoints:** `backend/app/api/`
*   **Services:** `backend/app/services/`
*   **Frontend Views:** `frontend/src/views/`
*   **Frontend Stores:** `frontend/src/stores/`

For more detailed information, please refer to the [DEVELOPERS_GUIDE.md](docs/DEVELOPERS_GUIDE.md).

## Contributing

We welcome contributions to the Recoater HMI! If you would like to contribute, please follow these steps:

1.  Fork the repository.
2.  Create a new branch for your feature or bug fix.
3.  Make your changes and commit them with a clear and descriptive message.
4.  Push your changes to your fork.
5.  Create a pull request to the `main` branch of the original repository.

Please make sure to read the [DEVELOPERS_GUIDE.md](docs/DEVELOPERS_GUIDE.md) before you start working on your changes.

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.
