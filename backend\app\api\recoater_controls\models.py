"""
Recoater Controls API Models
============================

This module contains all Pydantic request/response models for the recoater controls API.
"""

from pydantic import BaseModel, Field
from typing import Literal, Optional


class DrumMotionRequest(BaseModel):
    """Request model for drum motion commands."""
    mode: Literal["absolute", "relative", "turns", "speed", "homing"] = Field(..., description="Motion mode")
    speed: float = Field(..., gt=0, description="Movement speed in mm/s")
    distance: Optional[float] = Field(None, description="Distance to move in mm (for absolute/relative modes)")
    turns: Optional[float] = Field(None, description="Number of turns (for turns mode)")


class DrumEjectionRequest(BaseModel):
    """Request model for drum ejection pressure commands."""
    target: float = Field(..., ge=0, description="Target ejection pressure")
    unit: Literal["pascal", "bar"] = Field(default="pascal", description="Pressure unit")


class DrumSuctionRequest(BaseModel):
    """Request model for drum suction pressure commands."""
    target: float = Field(..., ge=0, description="Target suction pressure in Pa")


class BladeMotionRequest(BaseModel):
    """Request model for blade motion commands."""
    mode: Literal["absolute", "relative", "homing"] = Field(..., description="Motion mode")
    distance: Optional[float] = Field(None, description="Distance to move in µm (for absolute/relative modes)")


class BladeIndividualMotionRequest(BaseModel):
    """Request model for individual blade screw motion commands."""
    distance: float = Field(..., description="Relative distance of the motion [µm]")


class LevelerPressureRequest(BaseModel):
    """Request model for leveler pressure commands."""
    target: float = Field(..., ge=0, description="Target leveler pressure [Pa]")
