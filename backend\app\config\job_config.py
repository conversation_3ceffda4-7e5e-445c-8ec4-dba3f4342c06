"""
Job Configuration (Env-Driven)
===============================

Centralized configuration for multi-material job orchestration. Values are read
from environment variables (loaded from backend/.env by app.main).

These names are explicit for non-developer engineers. Units are included in the
variable names and in the documentation below.

Environment Variables (with defaults):
- JOB_MAX_DRUMS (int, default 3)
  Total enabled drums. Drums are addressed 0..(JOB_MAX_DRUMS-1).
- JOB_DRUM_UPLOAD_DELAY_SECONDS (float, default 2.0)
  Delay between uploading a layer to each drum (prevents hardware overload).
- JOB_STATUS_POLL_INTERVAL_SECONDS (float, default 2.0)
  Interval between status polling cycles during a layer print.
- JOB_READY_TIMEOUT_SECONDS (float, default 30.0)
  Maximum time to wait for the recoater "ready-to-print" signal for a layer.
- JOB_COMPLETION_TIMEOUT_SECONDS (float, default 300.0)
  Maximum time to wait for the recoater to finish printing a layer.
- LAYER_COMPLETION_QUERY_INTERVAL_SECONDS (float, default 5.0)
  Interval to poll recoater hardware state for layer completion (must follow hardware).
- JOB_EMPTY_LAYER_TEMPLATE_PATH (str)
  Absolute path to the blank/empty ASCII CLI file used when a drum has no data
  for a given layer. Default is set to your provided template path.
- JOB_CACHE_CLEANUP_MAX_AGE_SECONDS (int, default 3600)
  Retention window for cache cleanup utilities (preview/drum caches).
"""
from __future__ import annotations

import os
from dataclasses import dataclass


@dataclass
class JobConfig:
    """Strongly-typed job configuration loaded from environment variables.

    All time-based values are in seconds.
    """
    MAX_DRUMS: int
    DRUM_UPLOAD_DELAY_SECONDS: float
    STATUS_POLL_INTERVAL_SECONDS: float
    READY_TIMEOUT_SECONDS: float
    COMPLETION_TIMEOUT_SECONDS: float
    LAYER_COMPLETION_QUERY_INTERVAL_SECONDS: float
    EMPTY_LAYER_TEMPLATE_PATH: str
    CACHE_CLEANUP_MAX_AGE_SECONDS: int


def get_job_config() -> JobConfig:
    """Load job configuration from environment with safe defaults.

    Returns:
        JobConfig: configuration object with explicit, engineer-friendly names.
    """
    # Use provided absolute path as default for maximum clarity, overrideable via .env
    default_template_path = os.path.abspath(
        os.getenv(
            "JOB_EMPTY_LAYER_TEMPLATE_PATH",
            r"c:\\Users\\<USER>\\Downloads\\SIMTech_Internship\\RecoaterSearch\\APIRecoater_Ethernet\\backend\\app\\templates\\empty_layer.cli",
        )
    )

    return JobConfig(
        MAX_DRUMS=int(os.getenv("JOB_MAX_DRUMS", "3")),
        DRUM_UPLOAD_DELAY_SECONDS=float(os.getenv("JOB_DRUM_UPLOAD_DELAY_SECONDS", "2.0")),
        STATUS_POLL_INTERVAL_SECONDS=float(os.getenv("JOB_STATUS_POLL_INTERVAL_SECONDS", "2.0")),
        READY_TIMEOUT_SECONDS=float(os.getenv("JOB_READY_TIMEOUT_SECONDS", "30.0")),
        COMPLETION_TIMEOUT_SECONDS=float(os.getenv("JOB_COMPLETION_TIMEOUT_SECONDS", "300.0")),
        LAYER_COMPLETION_QUERY_INTERVAL_SECONDS=float(os.getenv("LAYER_COMPLETION_QUERY_INTERVAL_SECONDS", "5.0")),
        EMPTY_LAYER_TEMPLATE_PATH=default_template_path,
        CACHE_CLEANUP_MAX_AGE_SECONDS=int(os.getenv("JOB_CACHE_CLEANUP_MAX_AGE_SECONDS", "3600")),
    )

