"""
WARNING: This /job endpoint is deprecated.
         Please use multimaterial.py for automated iteration through layers for printing.

Job management endpoints:
- POST /job: Start a new print job.
- DELETE /job: Cancel the current print job.
- GET /job/status: Get the current print job status.
"""
import logging
from fastapi import APIRouter, HTTPException, Depends

from app.dependencies import get_recoater_client
from infrastructure.recoater_client import (
    RecoaterClient,
    RecoaterConnectionError,
    RecoaterAPIError,
)
from .models import PrintJobResponse, PrintJobStatusResponse

logger = logging.getLogger(__name__)
router = APIRouter()

# Direct API call to start print job
@router.post("/job", response_model=PrintJobResponse)
async def start_print_job(
    recoater_client: RecoaterClient = Depends(get_recoater_client),
) -> PrintJobResponse:
    """Create a printing job if the server is ready to start."""
    try:
        logger.info("Starting print job")
        result = recoater_client.start_print_job()
        return PrintJobResponse(success=True, status="started", job_id=result.get("job_id"))
    except RecoaterConnectionError as e:
        logger.error(f"Connection error starting print job: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error starting print job: {e}")
        raise HTTPException(status_code=409, detail=f"Cannot start print job: {e}")
    except Exception as e:
        logger.error(f"Unexpected error starting print job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")

# Direct API call to delete the current print job
@router.delete("/job", response_model=PrintJobResponse)
async def cancel_print_job(
    recoater_client: RecoaterClient = Depends(get_recoater_client),
) -> PrintJobResponse:
    """Cancel and remove the current printing job."""
    try:
        logger.info("Cancelling print job")
        recoater_client.cancel_print_job()
        return PrintJobResponse(success=True, status="cancelled")
    except RecoaterConnectionError as e:
        logger.error(f"Connection error cancelling print job: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error cancelling print job: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error cancelling print job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")

# Direct API call to get the print job status
@router.get("/job/status", response_model=PrintJobStatusResponse)
async def get_print_job_status(
    recoater_client: RecoaterClient = Depends(get_recoater_client),
) -> PrintJobStatusResponse:
    """Get the current print job status."""
    try:
        logger.info("Getting print job status")
        result = recoater_client.get_print_job_status()
        return PrintJobStatusResponse(**result)
    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting print job status: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting print job status: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error getting print job status: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")

