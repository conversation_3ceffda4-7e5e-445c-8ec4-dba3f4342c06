"""
Integration tests asserting OPC UA variable updates during layer-by-layer jobs.

These tests exercise MultiMaterialJobService directly (without REST) to focus on
OPC UA coordination signals introduced in the service layer.
"""
import asyncio
import pytest
from unittest.mock import AsyncMock, Mock, patch

from app.services.job_management import MultiMaterialJobService


class FakeParsed:
    def __init__(self, layers_count: int):
        self.header_lines = ["$$HEADEREND"]
        # minimal layer objects with z_height
        self.layers = [type("L", (), {"z_height": 0.1 * (i + 1), "polylines": [], "hatches": []})() for i in range(layers_count)]


@pytest.mark.asyncio
async def test_opcua_signals_progress_and_flags():
    """Verifies ready/complete flags and progress updates across two layers."""
    # Mock recoater client
    mock_client = Mock()
    mock_client.upload_cli_data = AsyncMock(return_value=True)
    mock_client.start_print_job = Mock(return_value={"success": True})
    mock_client.get_state = Mock(return_value={"state": "ready"})

    service = MultiMaterialJobService(mock_client)

    # Patch CLI generation to avoid heavy editor usage
    service.cli_parser.generate_single_layer_ascii_cli = Mock(return_value=b"ASCII")

    # Cache a simple 2-layer file for drum 0
    service.cache_cli_file_for_drum(0, FakeParsed(2), "a.cli")

    with patch("app.services.opcua.opcua_service.opcua_service") as mock_opcua:
        # Async mocks for opcua methods
        mock_opcua.set_job_active = AsyncMock(return_value=True)
        mock_opcua.set_job_inactive = AsyncMock(return_value=True)
        mock_opcua.update_layer_progress = AsyncMock(return_value=True)
        mock_opcua.set_recoater_ready_to_print = AsyncMock(return_value=True)
        mock_opcua.set_recoater_layer_complete = AsyncMock(return_value=True)
        mock_opcua.set_backend_error = AsyncMock(return_value=True)
        mock_opcua.get_backend_error = Mock(return_value=False)
        mock_opcua.get_plc_error = Mock(return_value=False)
        
        # Override wait_for_layer_completion to avoid polling loop
        service.wait_for_layer_completion = AsyncMock(return_value=True)

        # Run job
        ok = await service.start_layer_by_layer_job()
        assert ok is True

        # Assertions
        mock_opcua.set_job_active.assert_awaited_once()
        # Expect update_layer_progress called at least for initial (1) and after each layer (2, then 3)
        assert mock_opcua.update_layer_progress.await_count >= 2
        # Should toggle ready/complete per layer
        assert mock_opcua.set_recoater_ready_to_print.await_count >= 2
        assert mock_opcua.set_recoater_layer_complete.await_count >= 2
        mock_opcua.set_job_inactive.assert_awaited()


@pytest.mark.asyncio
async def test_backend_error_flag_on_exception():
    """Forces an exception mid-flow and asserts backend_error is set and job_inactive is called."""
    mock_client = Mock()
    mock_client.upload_cli_data = AsyncMock(side_effect=RuntimeError("upload failed"))
    mock_client.start_print_job = Mock(return_value={"success": True})
    mock_client.get_state = Mock(return_value={"state": "ready"})

    service = MultiMaterialJobService(mock_client)
    service.cli_parser.generate_single_layer_ascii_cli = Mock(return_value=b"ASCII")
    service.cache_cli_file_for_drum(0, FakeParsed(1), "a.cli")

    with patch("app.services.opcua.opcua_service.opcua_service") as mock_opcua:
        mock_opcua.set_job_active = AsyncMock(return_value=True)
        mock_opcua.set_job_inactive = AsyncMock(return_value=True)
        mock_opcua.update_layer_progress = AsyncMock(return_value=True)
        mock_opcua.set_recoater_ready_to_print = AsyncMock(return_value=True)
        mock_opcua.set_recoater_layer_complete = AsyncMock(return_value=True)
        mock_opcua.set_backend_error = AsyncMock(return_value=True)
        mock_opcua.get_backend_error = Mock(return_value=False)
        mock_opcua.get_plc_error = Mock(return_value=False)
        
        # Override wait_for_layer_completion to avoid polling loop
        service.wait_for_layer_completion = AsyncMock(return_value=True)

        with pytest.raises(Exception):
            await service.start_layer_by_layer_job()

        mock_opcua.set_backend_error.assert_awaited()
        mock_opcua.set_job_inactive.assert_awaited()
