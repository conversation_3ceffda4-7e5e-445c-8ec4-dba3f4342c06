"""
Pydantic models for API requests and responses.
No endpoints defined here.
"""
from typing import Any, Dict, Optional
from pydantic import BaseModel, Field


class LayerParametersRequest(BaseModel):
    """Request model for setting layer parameters."""
    filling_id: int = Field(..., description="The ID of the drum with the filling material powder. Set to -1 for no filling.")
    speed: float = Field(..., gt=0, description="The patterning speed [mm/s]")
    powder_saving: bool = Field(True, description="Flag indicating if powder saving strategies are used")
    x_offset: Optional[float] = Field(None, ge=0, description="The X offset [mm]")


class LayerParametersResponse(BaseModel):
    """Response model for layer parameters."""
    filling_id: int
    speed: float
    powder_saving: bool
    x_offset: Optional[float] = None
    max_x_offset: Optional[float] = None


class PrintJobResponse(BaseModel):
    """Response model for print job operations."""
    success: bool
    status: str
    job_id: Optional[str] = None


class FileUploadResponse(BaseModel):
    """Response model for file upload operations."""
    success: bool
    message: str
    drum_id: int
    file_size: int
    content_type: str


class FileDeleteResponse(BaseModel):
    """Response model for file deletion operations."""
    success: bool
    message: str
    drum_id: int


class PrintJobStatusResponse(BaseModel):
    """Response model for print job status."""
    state: str
    is_printing: bool
    has_error: bool


class CliUploadResponse(BaseModel):
    """Response model for CLI file upload operations."""
    success: bool
    message: str
    file_id: str
    total_layers: int
    file_size: int
    filename: str




class MultiMaterialJobResponse(BaseModel):
    """Response model for multi-material job operations."""
    success: bool
    message: str
    job_id: Optional[str] = None


class MultiMaterialJobStatusResponse(BaseModel):
    """Response model for multi-material job status."""
    job_id: Optional[str] = None
    is_active: bool
    status: str
    current_layer: int
    total_layers: int
    progress_percentage: float
    error_message: str
    drums: Dict[int, Dict[str, Any]]


class DrumStatusResponse(BaseModel):
    """Response model for individual drum status."""
    drum_id: int
    status: str
    ready: bool
    uploaded: bool
    current_layer: int
    total_layers: int
    error_message: str
    file_id: Optional[str] = None




class LayerRangeRequest(BaseModel):
    """Request model for sending a range of layers from a preview-cached CLI file."""
    start_layer: int = Field(..., ge=1, description="Start layer number (1-based)")
    end_layer: int = Field(..., ge=1, description="End layer number (1-based)")
