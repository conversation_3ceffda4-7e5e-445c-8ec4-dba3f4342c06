"""
CliCachingMixin
================

Unified caching system for CLI files in multi-material printing workflows.

Key Responsibilities:
- Generic CLI file caching for preview operations
- Drum-specific CLI file caching for printing workflows
- Cache management and cleanup operations
- Layer count calculations across cached files

Required Attributes (provided by concrete service):
- cli_parser: CLI editor for parsing and generating CLI files

Cache Types:
- cli_cache: Dict[str, CliCacheEntry] for generic file caching
- drum_cli_cache: Dict[int, Optional[Dict[str, Any]]] for per-drum caching (0-2)

Public Methods:
Generic Cache:
- add_cli_file(file_id, parsed_file, filename): Add file to generic cache
- get_cli_file(file_id): Get parsed CLI file data
- get_cli_file_with_metadata(file_id): Get file with metadata
- cli_file_exists(file_id): Check if file exists in cache

Drum Cache:
- cache_cli_file_for_drum(drum_id, parsed_file, filename): Cache file for specific drum
- get_cached_file_for_drum(drum_id): Get cached file for drum
- get_max_layers(): Get maximum layer count across drums
- has_cached_files(): Check if any drums have cached files
- clear_drum_cache(): Clear all drum caches
- clear_drum_cache_for_drum(drum_id): Clear specific drum cache

Cache Management:
- get_cache_status(): Get comprehensive status of all caches
"""

from __future__ import annotations

import logging
from typing import Dict, Optional, Any

from infrastructure.cli_editor.editor import ParsedCliFile
from ..models import CliCacheEntry

logger = logging.getLogger(__name__)


class CliCachingMixin:
    """
    Unified mixin for CLI file caching operations.

    This mixin provides both generic file caching (for previews) and
    drum-specific caching (for printing workflows) in a cohesive interface.
    """

    # ---- Generic CLI Cache (Preview Operations) ----

    def add_cli_file(self, file_id: str, parsed_file: ParsedCliFile, filename: Optional[str] = None) -> None:
        """Add a parsed CLI file to the generic cache for preview operations.

        Args:
            file_id: Unique identifier for the file
            parsed_file: Parsed CLI file data
            filename: Original filename (optional, defaults to 'unknown.cli')
        """
        if filename is None:
            filename = "unknown.cli"

        self.cli_cache[file_id] = {
            'parsed_file': parsed_file,
            'original_filename': filename,
        }
        logger.info(f"Added CLI file to preview cache: {file_id} ({filename}), layers={len(parsed_file.layers)}")

    def get_cli_file(self, file_id: str) -> Optional[ParsedCliFile]:
        """Get parsed CLI file data from generic cache.

        Args:
            file_id: Unique identifier for the file

        Returns:
            ParsedCliFile if found, None otherwise
        """
        entry = self.cli_cache.get(file_id)
        return entry['parsed_file'] if entry else None

    def get_cli_file_with_metadata(self, file_id: str) -> Optional[CliCacheEntry]:
        """Get CLI file with metadata from generic cache.

        Args:
            file_id: Unique identifier for the file

        Returns:
            CliCacheEntry if found, None otherwise
        """
        return self.cli_cache.get(file_id)

    def cli_file_exists(self, file_id: str) -> bool:
        """Check if CLI file exists in generic cache.

        Args:
            file_id: Unique identifier for the file

        Returns:
            True if file exists, False otherwise
        """
        return file_id in self.cli_cache

    # ---- Drum-Specific CLI Cache (Printing Workflow) ----

    def cache_cli_file_for_drum(self, drum_id: int, parsed_file: ParsedCliFile, filename: str) -> None:
        """Cache CLI file for specific drum in printing workflow.

        Args:
            drum_id: Drum identifier (0, 1, or 2)
            parsed_file: Parsed CLI file data
            filename: Original filename

        Raises:
            ValueError: If drum_id is invalid
        """
        if drum_id not in range(self.job_config.MAX_DRUMS):
            raise ValueError(f"Invalid drum_id: {drum_id}. Must be 0..{self.job_config.MAX_DRUMS-1}")

        self.drum_cli_cache[drum_id] = {
            'parsed_file': parsed_file,
            'filename': filename,
            'layer_count': len(parsed_file.layers)
        }
        logger.info(f"Cached CLI file '{filename}' for drum {drum_id} with {len(parsed_file.layers)} layers")

    def get_cached_file_for_drum(self, drum_id: int) -> Optional[Dict[str, Any]]:
        """Get cached CLI file for specific drum.

        Args:
            drum_id: Drum identifier (0, 1, or 2)

        Returns:
            Cached file data if found, None otherwise
        """
        return self.drum_cli_cache.get(drum_id)

    def get_max_layers(self) -> int:
        """Get maximum layer count across all cached drums.

        Returns:
            Maximum number of layers across all drum caches
        """
        max_layers = 0
        for drum_data in self.drum_cli_cache.values():
            if drum_data is not None:
                max_layers = max(max_layers, drum_data['layer_count'])
        return max_layers

    def has_cached_files(self) -> bool:
        """Check if any drums have cached CLI files.

        Returns:
            True if any drum has cached files, False otherwise
        """
        return any(data is not None for data in self.drum_cli_cache.values())

    def clear_drum_cache(self) -> None:
        """Clear all cached drum files."""
        self.drum_cli_cache = {i: None for i in range(self.job_config.MAX_DRUMS)}
        logger.info("Cleared all drum CLI cache")

    def clear_drum_cache_for_drum(self, drum_id: int) -> None:
        """Clear cached CLI file for a specific drum.

        Args:
            drum_id: Drum identifier (0, 1, or 2)

        Raises:
            ValueError: If drum_id is invalid
        """
        if drum_id not in range(self.job_config.MAX_DRUMS):
            raise ValueError(f"Invalid drum_id: {drum_id}. Must be 0..{self.job_config.MAX_DRUMS-1}")
        self.drum_cli_cache[drum_id] = None
        logger.info(f"Cleared CLI cache for drum {drum_id}")

    # ---- Cache Management Utilities ----

    def get_cache_status(self) -> Dict[str, Any]:
        """Get comprehensive status of all caches.

        Returns:
            Dictionary containing status of both cache types
        """
        generic_files = len(self.cli_cache)
        drum_cache_status = {
            drum_id: data is not None
            for drum_id, data in self.drum_cli_cache.items()
        }

        return {
            'generic_cache': {
                'file_count': generic_files,
                'files': list(self.cli_cache.keys())
            },
            'drum_cache': {
                'status': drum_cache_status,
                'cached_drums': [drum_id for drum_id, data in drum_cache_status.items() if data],
                'max_layers': self.get_max_layers()
            }
        }
