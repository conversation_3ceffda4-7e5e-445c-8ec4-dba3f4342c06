"""
Application Dependencies
========================

This module provides centralized dependency injection for the FastAPI application,
managing global instances of key services to avoid circular imports and ensure
consistent access across the codebase.

It handles the lifecycle of the following global dependencies:
- **Recoater Client**: Communicates with the Aerosint SPD Recoater hardware API.
  Supports both real (`RecoaterClient`) and mock (`MockRecoaterClient`) implementations
  for production and development modes.
- **OPC UA Service**: Manages unified in-process OPC UA-like coordination (server/monitoring/coordination).
- **Multilayer Job Manager**: Coordinates multi-material printing jobs using the recoater client.

Global instances are initialized as `None` to allow lazy loading and error checking.
Use the `initialize_*` functions during app startup (e.g., in `main.py`'s lifespan)
to set them, and the `get_*` functions to retrieve them safely in routers or services.
This pattern ensures dependencies are available when needed without tight coupling.
"""

import os
import logging
from typing import Optional
from fastapi import HTTPException

from app.websockets import WebSocketHandler
from infrastructure.recoater_client import RecoaterClient
from infrastructure.mock_recoater_client import Mock<PERSON>ecoaterClient
from app.services.opcua.opcua_service import opcua_service
from app.services.communication.websocket_manager import WebSocketConnectionManager
from app.services.monitoring.status_poller import StatusPollingService
from app.config.job_config import get_job_config

logger = logging.getLogger(__name__)

# All global variables initialized as None to represent conditionally initialized dependencies
# Global recoater client instance
_recoater_client: Optional[RecoaterClient] = None

# Global OPC UA service instance
_opcua_service = None

# Global multi-material job service (new) and legacy manager (backcompat)
_multimaterial_job_service = None
_multilayer_job_manager = None

_websocket_manager = None
_websocket_handler = None
_status_poller = None

def initialize_websocket_services() -> None:
    global _websocket_manager, _websocket_handler, _status_poller
    _websocket_manager = WebSocketConnectionManager()
    _websocket_handler = WebSocketHandler(_websocket_manager)
    _status_poller = StatusPollingService(_websocket_manager)
    logger.info("WebSocket services initialized")

def get_websocket_handler():
    if _websocket_handler is None:
        raise HTTPException(status_code=503, detail="WebSocket handler not initialized")
    return _websocket_handler

def get_status_poller():
    if _status_poller is None:
        raise HTTPException(status_code=503, detail="Status poller not initialized")
    return _status_poller

def initialize_recoater_client() -> None:
    """
    Initialize the global recoater client instance.

    This should be called during application startup.
    """
    global _recoater_client

    base_url = os.getenv("RECOATER_API_BASE_URL", "http://172.16.17.224:8080")
    development_mode = os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"

    if development_mode:
        logger.info("Initializing mock recoater client for development mode")
        _recoater_client = MockRecoaterClient(base_url)
    else:
        logger.info("Initializing real recoater client for production mode")
        _recoater_client = RecoaterClient(base_url)


async def initialize_opcua_service() -> None:
    """
    Initialize the global OPC UA service instance.

    This should be called during application startup.
    """
    global _opcua_service

    try:
        _opcua_service = opcua_service

        # Connect to OPC UA service
        connected = await _opcua_service.connect()
        if connected:
            logger.info("OPC UA service initialized and connected successfully")
        else:
            logger.warning("OPC UA service initialized but connection failed")

    except Exception as e:
        logger.error(f"Failed to initialize OPC UA service: {e}")
        _opcua_service = None


def initialize_multilayer_job_manager() -> None:
    """
    Initialize the global multi-material job service instance.

    This should be called during application startup after recoater client is initialized.
    Maintains legacy global for backward compatibility.
    """
    global _multimaterial_job_service, _multilayer_job_manager

    try:
        from app.services.job_management.multimaterial_job_service import MultiMaterialJobService

        if _recoater_client is None:
            raise RuntimeError("Recoater client must be initialized before job service")

        job_config = get_job_config()
        _multimaterial_job_service = MultiMaterialJobService(_recoater_client, opcua=_opcua_service or opcua_service, job_config=job_config)
        # Backward compatibility: legacy name returns the unified service facade
        _multilayer_job_manager = _multimaterial_job_service
        logger.info("MultiMaterialJobService initialized successfully (backcompat alias set)")

    except Exception as e:
        logger.error(f"Failed to initialize multi-material job service: {e}")
        _multimaterial_job_service = None
        _multilayer_job_manager = None


def initialize_multimaterial_job_service() -> None:
    """
    Preferred initializer for the unified multi-material job service.
    Backward-compatible with initialize_multilayer_job_manager().
    """
    # Delegate to legacy-named function for compatibility
    initialize_multilayer_job_manager()



def get_recoater_client() -> RecoaterClient:
    """
    Dependency function to get the recoater client instance.

    This function can be used with FastAPI's Depends() to inject
    the recoater client into API endpoints.

    Returns:
        RecoaterClient: The initialized recoater client instance

    Raises:
        HTTPException: If the recoater client is not initialized
    """
    if _recoater_client is None:
        logger.error("Recoater client not initialized")
        raise HTTPException(
            status_code=503,
            detail="Recoater client not initialized"
        )
    return _recoater_client


def get_opcua_service():
    """
    Dependency function to get the OPC UA service instance.

    This function can be used with FastAPI's Depends() to inject
    the OPC UA service into API endpoints.

    Returns:
        OPCUAService: The initialized OPC UA service instance

    Raises:
        HTTPException: If the OPC UA coordinator is not initialized
    """
    if _opcua_service is None:
        logger.error("OPC UA service not initialized")
        raise HTTPException(
            status_code=503,
            detail="OPC UA service not initialized"
        )
    return _opcua_service


def get_multilayer_job_manager():
    """
    Dependency function to get the multi-material job service (legacy alias).

    This function can be used with FastAPI's Depends() to inject the
    unified job service into API endpoints. Backward compatible name.

    Returns:
        MultiMaterialJobService: The initialized multi-material job service instance

    Raises:
        HTTPException: If the service is not initialized
    """
    if _multimaterial_job_service is None:
        logger.error("Multi-material job service not initialized")
        raise HTTPException(
            status_code=503,
            detail="Multi-material job service not initialized"
        )
    return _multimaterial_job_service


def get_recoater_client_instance() -> Optional[RecoaterClient]:
    """
    Get the recoater client instance without raising exceptions.

    This is useful for internal application code that needs to access
    the client but doesn't want to handle HTTP exceptions.

    Returns:
        Optional[RecoaterClient]: The recoater client instance or None if not initialized
    """
    return _recoater_client
