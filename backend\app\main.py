"""
Recoater HMI Backend - Main Application
=======================================

This is the main FastAPI application that serves as the backend for the
Recoater Custom HMI. It provides REST API endpoints and WebSocket connections
for real-time communication with the frontend.

The application acts as a proxy between the frontend and the recoater hardware,
providing a simplified and stable interface while handling all the complexity
of hardware communication.
"""

import os
import json
import logging
from contextlib import asynccontextmanager # Manages setup and teardown in asynchronous code

from fastapi import FastAPI, WebSocket
from fastapi.middleware.cors import CORSMiddleware # Tool to handle a web security feature called Cross-Origin Resource Sharing (CORS)
from dotenv import load_dotenv # Helper library to load configuration settings from a separate file (usually .env)

from app.dependencies import (
    initialize_recoater_client,
    initialize_opcua_service,
    initialize_multilayer_job_manager,
    initialize_websocket_services,
    get_websocket_handler,
    get_status_poller,
)
from app.utils.heartbeat import start_heartbeat_task, stop_heartbeat_task
from app.api.status import router as status_router
from app.api.recoater_controls import router as recoater_router
from app.api.print import router as print_router
from app.api.configuration import router as config_router
from app.api.errors import router as errors_router

# Looks for .env in the project folder and loads any variables from it into the environment
load_dotenv()

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO")),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Decorator from Python's contextlib module to turn an async generator function into a context manager
# which is a pattern for managing resources. 
# Handles init and teardown in async code internally with the _aenter_ and _aexit_ methods
# The name "lifespan"  is arbitrary, but FastAPI expects a callable function that behaves like a context manager
@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager. 
    Not unique to FastAPI but is a FastAPI-specific parameter for the FastAPI() constructor.
    The lifespan parameter initializes dependencies before serving requests and cleans up afterward.
    Context managers: Set up backend > yield to main logic > tear down
    """
    logger.info("Starting Recoater HMI Backend...")

    initialize_recoater_client()
    initialize_multilayer_job_manager()
    await initialize_opcua_service()
    initialize_websocket_services()
    await get_status_poller().start()
    heartbeat_task = start_heartbeat_task() # Sends "keep alive" signals to hardware to prevent auto-disconnect
    logger.info("Heartbeat task started")
    logger.info("Backend startup complete")

    # In async contexts, yield pauses execution and returns control, allowing other tasks to run
    # This effectively "yields" control to the app's runtime, then resumes for cleanup
    yield

    # Shutdown
    logger.info("Shutting down Recoater HMI Backend...")

    # Stop status polling task
    await get_status_poller().stop()

    # Stop heartbeat task
    try:
        await stop_heartbeat_task()
    except Exception as e:
        logger.error(f"Error stopping heartbeat task: {e}")
    else:
        logger.info("Heartbeat task stopped")

    logger.info("Backend shutdown complete")

# Creates an instance of the FastAPI web framework to build APIs, which builds on Starlette for handling HTTP requests asynchronously. Runs on Uvicron, a production-ready ASGI web server
# Sets up routing, middleware and event handling. We use our own custom lifespan for startup/shutdown, otherwise uses a no operation (no-op), empty lifespan by default, skipping startup/shutdown logic
app = FastAPI(
    title="Recoater HMI Backend",
    description="Backend API for the Aerosint SPD Recoater Custom HMI",
    version="1.0.0",
    lifespan=lifespan
)

# Middlewares intercepts requests/responses for cross-cutting concerns
# Cross-Origin Resource Sharing (CORS) is a web security policy enforced by browsers & prevents websites from making requests to different domains/ports without permission, to avoid cross-site attacks
# CORSMiddleware is FastAPI's tool to configure CORS rules, to accept requests from our local Vite dev server
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("CORS_ORIGINS", "http://localhost:5173").split(","),  # Vite dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# How endpoints work in FastAPI:
# - HTTP Endpoints (e.g., @app.get, @app.post): Handle stateless request-response cycles. Client sends a request, server responds, and the connection closes. No persistent connection or real-time pushing.
# - WebSocket Endpoints (e.g., @app.websocket): Enable persistent, bidirectional communication.
#   1. Handshake: Client sends an HTTP request to upgrade to WebSocket (handled automatically by FastAPI).
#   2. Connection Establishment: Once upgraded, the connection stays open, and the function runs asynchronously.
#   3. Message Exchange: Server and client can send messages bidirectionally using .send_text() and .receive_text().
#   4. Disconnection: When the client disconnects, WebSocketDisconnect is raised for cleanup.
#   5. Real-Time Nature: Server can push updates to connected clients without client requests.


# An endpoint is a URL path that handles requests. 
# When a client connects to this path, FastAPI handles the handshake, establishes the connection 
# and routes messages to the defined function at the end of the path
# Web framework routers group related endpoints into modular units, making code more organized
# include_router() adds a router to the main app, prefixing its endpoints
# Create routers in new files using @router.get
app.include_router(status_router, prefix="/api/v1")
app.include_router(recoater_router, prefix="/api/v1")
app.include_router(print_router, prefix="/api/v1")
app.include_router(config_router, prefix="/api/v1")
app.include_router(errors_router, prefix="/api/v1")

# FastAPI decorator that registers a WebSocket endpoint at /ws. Enables full-duplex, real-time communication over a single TCP connection
# Handles incoming connections, messages and disconnections asynchronously
# The endpoint at (ws://localhost)/ws is where the Vue app served by the Vite dev server (localhost:5173) connects to.
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time status updates."""
    handler = get_websocket_handler()
    await handler.websocket_endpoint(websocket)

# We can tell that this endpoint is not websocket, so it's a normal HTTP endpoint
# that handles stateless request-response cycles.
# We use these "normal" endpoints simply by using something like curl http://localhost:8000/ as a client
# The server then processes the request and returns the response
# Our frontend uses Axios to call these endpoints for CRUD operations
# This defines the "/" endpoint directly in main.py, but many of these can clutter the file, hence .include_router() exists to import and add a group of endpoints from a separate router file like app/api/status.py
@app.get("/")
async def root():
    """Root endpoint for basic health check."""
    return {
        "message": "Recoater HMI Backend",
        "version": "1.0.0",
        "status": "running"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=os.getenv("UVICORN_HOST", "0.0.0.0"),
        port=os.getenv("UVICORN_PORT", 8000),
        reload=True,
        log_level="info"
    )