"""
Job Management Models
====================

Shared type definitions and models used across the job management module.
This module centralizes common types to reduce redundancy and ensure consistency
across mixins and services.

Exposed Types:
- CliCacheEntry: Structure for CLI file cache entries
- MultiMaterialJobError: Common exception for job operations
"""
from __future__ import annotations

from typing import TypedDict

from infrastructure.cli_editor.editor import ParsedCliFile


class CliCacheEntry(TypedDict):
    """
    Type definition for CLI cache entries.
    
    Each cache entry contains:
    - parsed_file: The parsed CLI file data with layers and geometry
    - original_filename: The original filename for reference and API responses
    """
    parsed_file: ParsedCliFile
    original_filename: str


class MultiMaterialJobError(Exception):
    """
    Raised when multi-material job operations fail.

    This exception is used throughout the job management system for:
    - Job creation failures
    - Job lifecycle errors
    - Layer operation errors
    - Coordination failures
    """
    pass


__all__ = [
    "CliCacheEntry",
    "MultiMaterialJobError",
]
