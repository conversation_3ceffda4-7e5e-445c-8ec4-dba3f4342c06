#!/usr/bin/env python3
"""
Import Checker Script
====================

A standalone script to quickly check for import issues in the codebase.
This can be run independently of the test suite for quick validation.

Usage:
    python scripts/check_imports.py
"""

import os
import sys
import importlib
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))


def check_main_imports():
    """Check that main application components can be imported."""
    print("🔍 Checking main application imports...")
    
    try:
        from app.main import app
        print("✅ Main FastAPI application imported successfully")
    except Exception as e:
        print(f"❌ Failed to import main application: {e}")
        return False
    
    return True


def check_service_imports():
    """Check that all services can be imported."""
    print("\n🔍 Checking service imports...")
    
    services = [
        'app.services.communication.websocket_manager',
        'app.services.job_management.multimaterial_job_service',
        'app.services.monitoring.data_gatherer',
        'app.services.monitoring.status_poller',
        'app.services.opcua.opcua_service',
    ]
    
    all_passed = True
    for service in services:
        try:
            importlib.import_module(service)
            print(f"✅ {service}")
        except Exception as e:
            print(f"❌ {service}: {e}")
            all_passed = False
    
    return all_passed


def check_circular_dependencies():
    """Check for circular dependencies."""
    print("\n🔍 Checking for circular dependencies...")
    
    try:
        from app.services.job_management import MultiMaterialJobService
        from app.services.opcua import OPCUAService
        from app.services.monitoring import RecoaterDataGatherer
        from app.services.communication import WebSocketConnectionManager

        print("✅ No circular dependencies detected")
        return True
    except Exception as e:
        print(f"❌ Circular dependency detected: {e}")
        return False


def check_backend_prefix_imports():
    """Check for incorrect 'backend.' prefix imports."""
    print("\n🔍 Checking for incorrect 'backend.' prefix imports...")
    
    backend_dir = Path(__file__).parent.parent
    files_with_backend_imports = []
    
    for file_path in backend_dir.rglob("*.py"):
        # Skip __pycache__ and hidden directories
        if any(part.startswith('__pycache__') or part.startswith('.') for part in file_path.parts):
            continue
            
        # Skip this script itself and test_imports.py (which contains the search pattern)
        if file_path.name in ['check_imports.py', 'test_imports.py']:
            continue
            
        try:
            content = file_path.read_text(encoding='utf-8')
            if 'from backend.' in content or 'import backend.' in content:
                rel_path = file_path.relative_to(backend_dir)
                files_with_backend_imports.append(str(rel_path))
        except Exception:
            continue
    
    if files_with_backend_imports:
        print("❌ Found files with 'backend.' prefix imports:")
        for file_path in files_with_backend_imports:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ No incorrect 'backend.' prefix imports found")
        return True


def main():
    """Run all import checks."""
    print("🚀 Running import checks for Recoater HMI Backend\n")
    
    checks = [
        check_main_imports,
        check_service_imports,
        check_circular_dependencies,
        check_backend_prefix_imports,
    ]
    
    results = []
    for check in checks:
        results.append(check())
    
    print("\n" + "="*50)
    if all(results):
        print("🎉 All import checks passed!")
        return 0
    else:
        print("💥 Some import checks failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
