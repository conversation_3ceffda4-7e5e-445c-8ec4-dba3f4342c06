"""
Unified OPC UA Service (Mixin-based)
====================================

This service composes server management, coordination, and monitoring into a single
beginner-friendly API, following the same Clean Architecture pattern as MultiMaterialJobService.

Architecture: Clean Architecture with Mixin Composition
Pattern: Identical to MultiMaterialJobService (composition over inheritance)
Dependencies: Configuration loaded lazily via dynamic import to avoid circular dependencies

Mixin Composition Order (Critical for Method Resolution):
========================================================
CoordinationMixin comes FIRST because it overrides write_variable() to add event handling.
ServerMixin comes SECOND to provide the base write_variable() implementation.
MonitoringMixin comes LAST as it provides independent monitoring functionality.

This order ensures that CoordinationMixin.write_variable() is called instead of
ServerMixin.write_variable(), allowing event handlers to be triggered when variables change.

7-Variable OPC UA Coordination Protocol:
======================================
1. job_active: Backend sets TRUE at start, FALSE at end
2. total_layers: Backend sets once at job start
3. current_layer: Backend manages, PLC reads for progress tracking
4. recoater_ready_to_print: Backend writes when Aerosint is ready
5. recoater_layer_complete: Backend writes when deposition complete
6. backend_error: Backend writes if any issue arises
7. plc_error: PLC writes if any issues

Service Lifecycle:
================
1. Initialize with ServerConfig (or load from app.config.opcua_config)
2. Call initialize() or manually: start_server() + connect()
3. Use coordination methods for job management
4. Call shutdown() or manually: disconnect() + stop_server()

Public Methods:
- initialize(): Combined server start and coordination connect
- shutdown(): Combined coordination disconnect and server stop
- connect(): Establish coordination connection with monitoring
- disconnect(): Close coordination connection and cleanup
- is_connected(): Check logical connection status
- start_server(): Start the OPC UA server infrastructure
- stop_server(): Stop the OPC UA server infrastructure
- write_variable(name, value): Write value to OPC UA variable
- read_variable(name): Read value from OPC UA variable
- subscribe_to_changes(variables, handler): Subscribe to variable change events
- start_monitoring(interval): Start health monitoring loop
- stop_monitoring(): Stop health monitoring loop

Coordination Methods (via CoordinationMixin):
- set_job_active(total_layers): Initialize job with total layers
- set_job_inactive(): Deactivate job and clear all flags
- update_layer_progress(current_layer): Update current layer progress
- set_recoater_ready_to_print(ready): Signal recoater readiness
- set_recoater_layer_complete(complete): Signal layer completion
- set_backend_error(error): Set/clear backend error flag
- set_plc_error(error): Set/clear PLC error flag
- clear_error_flags(): Clear both error flags
- get_backend_error(): Get backend error status
- get_plc_error(): Get PLC error status
- get_server_status(): Get comprehensive server status

Private Helper Methods:
- _trigger_event_handlers(name, value): Execute subscribed event handlers

Configuration:
- Dynamic import of app.config.opcua_config to avoid circular dependencies
- ServerConfig conversion from external config format
- Lazy loading ensures config is available when service is instantiated
"""
from __future__ import annotations

import logging
from typing import Optional

from .mixins import ServerMixin, CoordinationMixin, MonitoringMixin
from .models import ServerConfig


class OPCUAService(CoordinationMixin, ServerMixin, MonitoringMixin):
    """Unified OPC UA service combining server, coordination, and monitoring.

    Inheritance Order Reasoning:
    ===========================
    CoordinationMixin comes FIRST because it overrides write_variable() to add event handling.
    ServerMixin comes SECOND to provide the base write_variable() implementation.
    MonitoringMixin comes LAST as it provides independent monitoring functionality.

    This order ensures that CoordinationMixin.write_variable() is called instead of
    ServerMixin.write_variable(), allowing event handlers to be triggered when variables change.
    """
    """Unified OPC UA service combining server, coordination, and monitoring."""

    def __init__(self, config: Optional[ServerConfig] = None):
        # Dynamic configuration import (matches job_management style)
        if config is None:
            config_module = __import__('app.config.opcua_config', fromlist=['OPCUAServerConfig'])
            get_config = getattr(config_module, 'get_opcua_config')
            cfg = get_config()
            self.config = ServerConfig(
                endpoint=cfg.endpoint,
                server_name=cfg.server_name,
                namespace_uri=cfg.namespace_uri,
                namespace_idx=cfg.namespace_idx,
                security_policy=cfg.security_policy,
                security_mode=cfg.security_mode,
                certificate_path=cfg.certificate_path,
                private_key_path=cfg.private_key_path,
                connection_timeout=cfg.connection_timeout,
                session_timeout=cfg.session_timeout,
                auto_restart=cfg.auto_restart,
                restart_delay=cfg.restart_delay,
                max_restart_attempts=cfg.max_restart_attempts,
            )
        else:
            self.config = config

        # Initialize state required by mixins
        self._logger = logging.getLogger(__name__)
        self._server_running = False
        self._opcua_server = None
        self._variable_nodes = {}
        # Stores local copy of last known values of OPC UA variables, no network overhead for await call
        # May be stale for plc_error
        self._variable_cache = {}
        self._connected = False
        self._monitoring_task = None
        self.monitoring_active = False
        self._event_handlers = {}  # Initialize event handlers

        super().__init__()
        self._logger.info("OPCUAService initialized (clean architecture, mixin-based)")

    # Convenience combined operations
    async def initialize(self) -> bool:
        ok = await self.start_server()
        if not ok:
            return False
        return await self.connect()

    async def shutdown(self) -> bool:
        await self.disconnect()
        return await self.stop_server()

    # Synchronous convenience getters (read from variable cache)
    def get_variable(self, name: str):
        """Get variable value synchronously from cache. Returns None if server not running or variable not found."""
        if not self._server_running:
            return None
        return self._variable_cache.get(name)

    def get_job_active(self) -> bool:
        v = self.get_variable("job_active")
        return bool(v) if v is not None else False

    def get_total_layers(self) -> int:
        v = self.get_variable("total_layers")
        return int(v or 0)

    def get_current_layer(self) -> int:
        v = self.get_variable("current_layer")
        return int(v or 0)

    def get_backend_error(self) -> bool:
        v = self.get_variable("backend_error")
        return bool(v) if v is not None else False

    def get_plc_error(self) -> bool:
        v = self.get_variable("plc_error")
        return bool(v) if v is not None else False

    def get_recoater_ready_to_print(self) -> bool:
        v = self.get_variable("recoater_ready_to_print")
        return bool(v) if v is not None else False

    def get_recoater_layer_complete(self) -> bool:
        v = self.get_variable("recoater_layer_complete")
        return bool(v) if v is not None else False

    def get_server_status(self) -> dict:
        return {
            "server_running": self._server_running,
            "connected": self._connected,
            "endpoint": getattr(self.config, "endpoint", None),
            "namespace": getattr(self.config, "namespace_uri", None),
            "variable_count": len(self._variable_nodes),
        }


    async def __aenter__(self):
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.shutdown()


# Global singleton instance for convenience (similar to previous pattern)
opcua_service = OPCUAService()

