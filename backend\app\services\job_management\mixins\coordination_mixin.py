"""
OPCUACoordinationMixin
=====================
Unified OPC UA coordination mixin that handles all OPC UA variable management
and coordination logic for the 7-variable architecture. This consolidates
coordination functionality from the old CoordinationMixin and removes
backward-compatibility cruft.

Key Responsibilities:
- OPC UA Communication: Interface with PLC via 7-variable architecture
- Job Lifecycle: Setup and cleanup OPC UA state for jobs
- Layer Coordination: Manage layer-by-layer OPC UA variable updates
- Error Management: Handle backend_error and plc_error flags
- Completion Monitoring: Wait for layer completion via hardware polling

7-Variable Architecture:
- job_active: Backend sets TRUE at start, FALSE at end
- total_layers: Backend sets once at job start
- current_layer: Backend manages, PLC reads
- recoater_ready_to_print: Backend writes when Aerosint is ready
- recoater_layer_complete: Backend writes when deposition complete
- backend_error: Backend writes if any issue arises
- plc_error: PLC writes if any issues

Public Methods:
- setup_opcua_job(total_layers): Setup OPC UA state for job
- cleanup_opcua_job(): Cleanup OPC UA state after job completion
- reset_opcua_layer_flags(): Reset per-layer flags before uploads/print cycle
- update_opcua_layer_progress(progress): Update current layer progress
- signal_opcua_layer_complete(): Signal completion of current layer
- signal_opcua_ready_to_print(): Signal that recoater is ready to print
- handle_job_error(error): Set backend_error flag and update state
- wait_for_layer_completion(): Wait for layer completion via hardware polling
- clear_error_flags(): Clear all error flags

Private Helper Methods:
- _resolve_opcua(): Resolve the OPC UA service instance
- _set_error_state(error_message): Set coordination error state
"""
from __future__ import annotations

import asyncio
import logging
from enum import Enum

from app.models.multilayer_job import JobStatus

logger = logging.getLogger(__name__)


class OPCUACoordinationMixin:

    def _resolve_opcua(self):
        """Resolve the OPC UA service instance for both production and tests."""
        # Simple resolution: prefer injected opcua, fallback to module-level
        if getattr(self, 'opcua', None):
            return self.opcua

        # Fallback to module-level opcua_service (allows test patching)
        try:
            from app.services.opcua.opcua_service import opcua_service
            return opcua_service
        except Exception:
            return None

    async def wait_for_layer_completion(self) -> bool:
        """Wait for layer completion by polling recoater state and checking error flags.

        Returns:
            True if layer completed successfully, False if error occurred or was cancelled
        """
        polling_interval = self.status_poll_interval
        opcua = self._resolve_opcua()

        # Safety timeout to avoid infinite wait in dev/mock environments
        try:
            from app.config.job_config import get_job_config
            max_wait = float(get_job_config().COMPLETION_TIMEOUT_SECONDS)
        except Exception:
            max_wait = 300.0
        start_time = None

        while True:
            try:
                # Respect cancellation promptly
                try:
                    if getattr(self, 'current_job', None) and not self.current_job.is_active:
                        logger.info("Layer wait aborted: job cancelled")
                        return False
                except Exception:
                    pass

                # Check OPC UA error flags first
                if opcua:
                    backend_error = opcua.get_backend_error()
                    plc_error = opcua.get_plc_error()
                    if backend_error or plc_error:
                        error_message = f"Error during layer completion: backend_error={backend_error}, plc_error={plc_error}"
                        logger.error(error_message)
                        await self._set_error_state(error_message)
                        return False

                # Poll recoater hardware state
                try:
                    recoater_state_response = await asyncio.to_thread(self.recoater_client.get_state)
                except Exception as hardware_exception:
                    logger.warning(f"Failed to get recoater state, will retry: {hardware_exception}")
                    recoater_state_response = None

                if isinstance(recoater_state_response, dict):
                    # Accept either 'state' or (fallback) 'status' from mock/real client
                    recoater_state = recoater_state_response.get('state') or (
                        'printing' if recoater_state_response.get('status') == 'running' else recoater_state_response.get('status')
                    )
                    if recoater_state == 'ready':
                        return True
                    if recoater_state == 'error':
                        await self._set_error_state("Recoater reported error during layer completion")
                        return False

                # Initialize start time after first poll
                if start_time is None:
                    import time as _t
                    start_time = _t.time()
                else:
                    import time as _t
                    if (_t.time() - start_time) > max_wait:
                        logger.warning("Layer wait timed out; treating as completed (dev/mock safety)")
                        return True

                await asyncio.sleep(polling_interval)
            except Exception as general_exception:
                logger.error(f"Error waiting for layer completion: {general_exception}")
                await asyncio.sleep(polling_interval)


    async def _set_error_state(self, error_message: str) -> None:
        """Set error state and update OPC UA backend_error flag."""
        logger.error(f"OPC UA coordination error: {error_message}")

        # Update job state if available (keep job active; pause until cleared)
        if hasattr(self, 'current_job') and self.current_job:
            self.current_job.status = JobStatus.ERROR

        # Set OPC UA backend error flag
        opcua = self._resolve_opcua()
        if opcua:
            await opcua.set_backend_error(True)

    async def clear_error_flags(self) -> bool:
        """Clear all error flags and reset error state."""
        try:
            logger.info("Clearing OPC UA error flags")

            opcua = self._resolve_opcua()
            if opcua:
                await opcua.clear_error_flags()
            return True
        except Exception as e:
            logger.error(f"Failed to clear error flags: {e}")
            return False

    async def wait_until_error_cleared(self, poll_interval: float | None = None) -> bool:
        """Block until backend_error and plc_error are both False.

        Returns True when cleared. Intended for pause/resume behavior on errors.
        """
        try:
            import asyncio
            interval = poll_interval or getattr(self, 'status_poll_interval', 1.0)
            opcua = self._resolve_opcua()
            while True:
                if not opcua:
                    return True  # Nothing to wait for
                try:
                    backend_error = bool(getattr(opcua, 'get_backend_error', lambda: False)())
                    plc_error = bool(getattr(opcua, 'get_plc_error', lambda: False)())
                except Exception:
                    backend_error = False
                    plc_error = False
                if not backend_error and not plc_error:
                    return True
                await asyncio.sleep(interval)
        except Exception as e:
            logger.warning(f"Error while waiting for error flags to clear: {e}")
            return True

    async def setup_opcua_job(self, total_layers: int) -> None:
        """Setup OPC UA job_active=True, total_layers=max_layers, current_layer=1."""
        try:
            opcua = self._resolve_opcua()
            if opcua:
                await opcua.set_job_active(total_layers)
                await opcua.update_layer_progress(1)  # Start at layer 1
                logger.info(f"OPC UA job setup complete: {total_layers} layers")
        except Exception as e:
            logger.warning(f"Failed to setup OPC UA job: {e}")

    async def cleanup_opcua_job(self) -> None:
        """Cleanup OPC UA state after job completion or error."""
        try:
            opcua = self._resolve_opcua()
            if opcua:
                await opcua.set_job_inactive()
                logger.info("OPC UA job cleanup complete")
        except Exception as e:
            logger.warning(f"Failed to cleanup OPC UA job: {e}")

    async def reset_opcua_layer_flags(self) -> None:
        """Reset per-layer flags before uploads/print cycle."""
        try:
            opcua = self._resolve_opcua()
            if opcua:
                await opcua.set_recoater_ready_to_print(False)
                await opcua.set_recoater_layer_complete(False)
        except Exception as e:
            logger.warning(f"Failed to reset OPC UA layer flags: {e}")

    async def update_opcua_layer_progress(self, progress: int) -> None:
        """Update current OPC UA layer progress (1..total_layers)."""
        try:
            opcua = self._resolve_opcua()
            if opcua:
                await opcua.update_layer_progress(progress)
        except Exception as e:
            logger.warning(f"Failed to update layer progress: {e}")

    async def signal_opcua_layer_complete(self) -> None:
        """Signal completion of the current layer."""
        try:
            opcua = self._resolve_opcua()
            if opcua:
                await opcua.set_recoater_layer_complete(True)
        except Exception as e:
            logger.warning(f"Failed to signal layer complete: {e}")

    async def signal_opcua_ready_to_print(self) -> None:
        """Signal that recoater is ready to print."""
        try:
            opcua = self._resolve_opcua()
            if opcua:
                await opcua.set_recoater_ready_to_print(True)
        except Exception as e:
            logger.warning(f"Failed to signal ready to print: {e}")

    async def handle_job_error(self, error: Exception) -> None:
        """Handle job error by setting backend_error flag."""
        error_message = str(error)
        logger.error(f"Job error: {error_message}")
        await self._set_error_state(error_message)
