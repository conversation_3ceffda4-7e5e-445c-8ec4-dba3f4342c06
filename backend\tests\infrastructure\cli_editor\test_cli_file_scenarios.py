"""
Tests for CLI File Upload Scenarios
===================================

This module contains comprehensive tests for different CLI file upload scenarios
including single drum uploads, dual material printing, triple material printing,
layer range selections, and empty layer template handling.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from io import BytesIO, StringIO

from app.main import app
from app.dependencies import get_recoater_client, get_multilayer_job_manager
from app.services.job_management import MultiMaterialJobService, MultiMaterialJobError
from infrastructure.cli_editor.editor import Editor, ParsedCliFile, CliLayer, Point, Polyline, Hatch
from infrastructure.recoater_client import RecoaterConnectionError, RecoaterAPIError


@pytest.fixture
def mock_recoater_client():
    """Create a mock recoater client."""
    client = Mock()
    client.upload_drum_geometry = Mock(return_value={"success": True})
    client.get_drum_status = Mock(return_value={"status": "ready"})
    return client


@pytest.fixture
def mock_multilayer_job_manager():
    """Create a mock multilayer job manager."""
    manager = Mock(spec=MultiMaterialJobService)
    manager.cli_cache = {}
    manager.current_job = None
    # Return a mock job object with a stable job_id so API response validation passes
    manager.create_job = AsyncMock(return_value=Mock(job_id="test_job_123"))
    # start_job should return True to indicate job start success
    manager.start_job = AsyncMock(return_value=True)
    manager._load_empty_layer_template = AsyncMock()
    manager.cli_parser = Mock()
    manager.cli_parser.parse = Mock()
    return manager


@pytest.fixture
def client(mock_recoater_client, mock_multilayer_job_manager):
    """Create a test client for the FastAPI app with mocked dependencies."""
    # Override the dependencies
    app.dependency_overrides[get_recoater_client] = lambda: mock_recoater_client
    app.dependency_overrides[get_multilayer_job_manager] = lambda: mock_multilayer_job_manager
    
    # Create test client
    test_client = TestClient(app)
    
    yield test_client
    
    # Clean up dependency overrides
    app.dependency_overrides.clear()


@pytest.fixture
def sample_cli_content():
    """Sample CLI file content for testing."""
    return """$$HEADERSTART
$$ASCII
$$UNITS/00000000.005000
$$VERSION/200
$$LABEL/1,test_part
$$LAYERS/000003
$$HEADEREND
$$GEOMETRYSTART
$$LAYER/0.0
$$POLYLINE/1,0,0,0,0
0.0,0.0
10.0,0.0
10.0,10.0
0.0,10.0
0.0,0.0
$$LAYER/0.1
$$POLYLINE/1,0,0,0,0
1.0,1.0
9.0,1.0
9.0,9.0
1.0,9.0
1.0,1.0
$$LAYER/0.2
$$POLYLINE/1,0,0,0,0
2.0,2.0
8.0,2.0
8.0,8.0
2.0,8.0
2.0,2.0
$$HEADEREND"""


@pytest.fixture
def empty_cli_content():
    """Empty CLI file content for testing."""
    return """$$HEADERSTART
$$ASCII
$$UNITS/00000000.005000
$$VERSION/200
$$LABEL/1,empty
$$LAYERS/000001
$$HEADEREND
$$GEOMETRYSTART
$$LAYER/0.0
$$HEADEREND"""



class TestSingleDrumCLIUploads:
    """Test single drum CLI upload scenarios."""

    def test_single_drum_upload_success(self, client, sample_cli_content):
        """Test successful single drum CLI upload."""
        # Create file-like object
        cli_file = BytesIO(sample_cli_content.encode('utf-8'))
        cli_file.name = "test_part.cli"

        # Upload CLI file
        response = client.post(
            "/api/v1/print/cli/upload",
            files={"file": ("test_part.cli", cli_file, "application/octet-stream")}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["total_layers"] == 3
        assert "file_id" in data

    def test_single_drum_upload_invalid_file(self, client):
        """Test single drum upload with invalid file type."""
        # Create non-CLI file
        invalid_file = BytesIO(b"not a cli file")
        invalid_file.name = "test.txt"

        response = client.post(
            "/api/v1/print/cli/upload",
            files={"file": ("test.txt", invalid_file, "text/plain")}
        )

        assert response.status_code == 400
        assert "Only .cli files are supported" in response.json()["detail"]

    def test_single_drum_upload_empty_file(self, client):
        """Test single drum upload with empty file."""
        empty_file = BytesIO(b"")
        empty_file.name = "empty.cli"

        response = client.post(
            "/api/v1/print/cli/upload",
            files={"file": ("empty.cli", empty_file, "application/octet-stream")}
        )

        assert response.status_code == 400
        assert "Uploaded file is empty" in response.json()["detail"]


class TestDualMaterialPrinting:
    """Test dual material printing scenarios (2 drums with files, 1 empty)."""

    @pytest.mark.asyncio
    async def test_dual_material_job_creation(self, sample_cli_content):
        """Test creating a dual material job with 2 files."""
        # Create a real-like mock that will actually call _load_empty_layer_template
        manager = Mock(spec=MultiMaterialJobService)
        manager.cli_cache = {
            "file1": Mock(layers=[Mock() for _ in range(3)]),
            "file2": Mock(layers=[Mock() for _ in range(3)])
        }
        
        # Mock the method and configure it to be called
        manager._load_empty_layer_template = AsyncMock(return_value="empty_template_123")
        
        # Test dual material modern workflow
        file_ids = {0: "file1", 1: "file2"}
        
        # Mock the cache_cli_file_for_drum and start_layer_by_layer_job methods
        manager.cache_cli_file_for_drum = Mock()
        manager.start_layer_by_layer_job = AsyncMock(return_value=True)
        
        # Cache files for drums using actual CLI content
        manager.cache_cli_file_for_drum(0, manager.cli_cache["file1"], "file1")
        manager.cache_cli_file_for_drum(1, manager.cli_cache["file2"], "file2")
        
        # Start modern workflow
        result = await manager.start_layer_by_layer_job()
        
        # Verify modern workflow was used instead of legacy methods
        assert result is True
        assert manager.cache_cli_file_for_drum.call_count == 2
        manager.start_layer_by_layer_job.assert_called_once()

    @pytest.mark.asyncio
    async def test_dual_material_validation_error(self):
        """Test dual material job with insufficient cached files."""
        manager = Mock(spec=MultiMaterialJobService)
        
        # Mock the modern workflow to raise error for insufficient cached files
        manager.start_layer_by_layer_job = AsyncMock(
            side_effect=MultiMaterialJobError("No CLI files cached for any drums")
        )
        
        # Don't cache any files, then try to start workflow
        with pytest.raises(MultiMaterialJobError, match="No CLI files cached"):
            await manager.start_layer_by_layer_job()


class TestTripleMaterialPrinting:
    """Test triple material printing scenarios (3 drums with files)."""

    @pytest.mark.asyncio
    async def test_triple_material_job_creation(self, mock_multilayer_job_manager):
        """Test creating a triple material job using modern workflow."""
        # Setup mock parsed files
        mock_parsed_file = Mock(spec=ParsedCliFile)
        mock_parsed_file.layers = [Mock(spec=CliLayer) for _ in range(5)]
        
        # Cache files for all 3 drums using modern approach
        mock_multilayer_job_manager.cache_cli_file_for_drum(0, mock_parsed_file, "file1")
        mock_multilayer_job_manager.cache_cli_file_for_drum(1, mock_parsed_file, "file2")
        mock_multilayer_job_manager.cache_cli_file_for_drum(2, mock_parsed_file, "file3")
        
        # Test triple material job using modern unified workflow
        result = await mock_multilayer_job_manager.start_layer_by_layer_job()
        
        # Verify no empty template was needed
        assert not hasattr(mock_multilayer_job_manager, '_load_empty_layer_template') or \
               not mock_multilayer_job_manager._load_empty_layer_template.called

    def test_triple_material_api_endpoint(self, client, sample_cli_content):
        """Test triple material job via API endpoint (per-drum cached workflow)."""
        # Upload a CLI file to all three drums
        from io import BytesIO
        content = sample_cli_content.encode('utf-8')
        files = {"file": ("triple.cli", BytesIO(content), "application/octet-stream")}
        for drum_id in [0, 1, 2]:
            upload_resp = client.post(f"/api/v1/print/cli/upload/{drum_id}", files=files)
            assert upload_resp.status_code == 200

        # Start job with cached drum files (no payload)
        response = client.post("/api/v1/print/cli/start-multimaterial-job")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["job_id"] == "layer-by-layer-job"


class TestLayerRangeManagement:
    """Test layer range selections and management."""

    def test_layer_range_validation(self, client):
        """Test layer range validation."""
        # Import dependencies here
        from app.dependencies import get_multilayer_job_manager, get_recoater_client
        from app.main import app
        
        # Setup cache mock with proper layer structure
        mock_layer = Mock(spec=CliLayer)
        mock_layer.z_height = 0.1
        mock_layer.polylines = []
        mock_layer.hatches = []
        
        mock_parsed_file = Mock(spec=ParsedCliFile)
        mock_parsed_file.layers = [mock_layer for _ in range(10)]  # 10 layers
        mock_parsed_file.header_lines = ["$$HEADERSTART", "$$HEADEREND"]
        
        # Cache stores dictionaries with 'parsed_file' and 'original_filename'
        cache_entry = {
            'parsed_file': mock_parsed_file,
            'original_filename': 'test_file.cli'
        }
        
        # Setup job manager mock
        mock_manager = Mock()
        mock_manager.get_cli_file_with_metadata.return_value = cache_entry
        
        # Setup recoater client mock
        mock_client = Mock()
        mock_client.upload_drum_geometry = Mock(return_value={"success": True})
        
        # Override dependencies directly
        app.dependency_overrides[get_multilayer_job_manager] = lambda: mock_manager
        app.dependency_overrides[get_recoater_client] = lambda: mock_client
        
        try:
            # Test valid layer range using the correct endpoint
            response = client.post(
                "/api/v1/print/cli/test_file/layers/send/0",
                json={"start_layer": 3, "end_layer": 7}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["layer_range"] == [3, 7]
        finally:
            # Clean up dependency overrides
            if get_multilayer_job_manager in app.dependency_overrides:
                del app.dependency_overrides[get_multilayer_job_manager]
            if get_recoater_client in app.dependency_overrides:
                del app.dependency_overrides[get_recoater_client]

    def test_invalid_layer_range(self, client):
        """Test invalid layer range handling."""
        from app.dependencies import get_multilayer_job_manager
        from app.main import app
        
        # Setup job manager mock to return None (file not found)
        mock_manager = Mock()
        mock_manager.get_cli_file_with_metadata.return_value = None
        
        # Override dependency
        app.dependency_overrides[get_multilayer_job_manager] = lambda: mock_manager
        
        try:
            response = client.post(
                "/api/v1/print/cli/nonexistent_file/layers/send/0",
                json={"start_layer": 1, "end_layer": 5}
            )
            
            assert response.status_code == 404
        finally:
            # Clean up dependency override
            if get_multilayer_job_manager in app.dependency_overrides:
                del app.dependency_overrides[get_multilayer_job_manager]
            assert "CLI file not found" in response.json()["detail"]


class TestEmptyLayerTemplateHandling:
    """Test empty layer template handling."""

    @pytest.mark.asyncio
    async def test_empty_template_loading(self, empty_cli_content):
        """Test loading empty layer template."""
        manager = Mock(spec=MultiMaterialJobService)
        manager.cli_cache = {}
        
        # Setup CLI parser mock properly
        mock_parser = Mock()
        mock_parsed_template = Mock(spec=ParsedCliFile)
        mock_parsed_template.layers = [Mock(spec=CliLayer)]
        mock_parser.parse = Mock(return_value=mock_parsed_template)
        manager.cli_parser = mock_parser
        
        # Mock the actual method
        async def load_empty_template():
            template_id = "empty_template_123"
            manager.cli_cache[template_id] = mock_parsed_template
            return template_id
        
        manager._load_empty_layer_template = AsyncMock(side_effect=load_empty_template)
        
        template_id = await manager._load_empty_layer_template()
        
        assert template_id == "empty_template_123"
        assert template_id in manager.cli_cache

    @pytest.mark.asyncio
    async def test_empty_template_missing_file(self):
        """Test handling missing empty template file."""
        manager = Mock(spec=MultiMaterialJobService)
        
        # Configure the method to raise the expected error
        manager._load_empty_layer_template = AsyncMock(
            side_effect=MultiMaterialJobError("Empty layer template not found")
        )
        
        with pytest.raises(MultiMaterialJobError, match="Empty layer template not found"):
            await manager._load_empty_layer_template()


def mock_open(read_data):
    """Helper function to create a mock open context manager."""
    from unittest.mock import mock_open as _mock_open
    return _mock_open(read_data=read_data)


class TestCLIFileIntegration:
    """Test CLI file integration scenarios."""

    def test_cli_upload_and_layer_send_workflow(self, client, sample_cli_content):
        """Test complete workflow: upload CLI file and send specific layer."""
        # Step 1: Upload CLI file
        cli_file = BytesIO(sample_cli_content.encode('utf-8'))
        cli_file.name = "workflow_test.cli"

        upload_response = client.post(
            "/api/v1/print/cli/upload",
            files={"file": ("workflow_test.cli", cli_file, "application/octet-stream")}
        )

        assert upload_response.status_code == 200
        file_id = upload_response.json()["file_id"]

        # Step 2: Mock job manager and recoater client for layer upload
        from app.dependencies import get_multilayer_job_manager, get_recoater_client
        from app.main import app
        
        # Setup cache mock with proper layer structure
        mock_layer = Mock(spec=CliLayer)
        mock_layer.z_height = 0.1
        mock_layer.polylines = []
        mock_layer.hatches = []

        mock_parsed_file = Mock(spec=ParsedCliFile)
        mock_parsed_file.layers = [mock_layer for _ in range(3)]
        mock_parsed_file.header_lines = ["$$HEADERSTART", "$$HEADEREND"]

        # Job manager returns metadata with parsed_file and original_filename
        metadata_entry = {
            'parsed_file': mock_parsed_file,
            'original_filename': 'workflow_test.cli'
        }

        mock_manager = Mock()
        mock_manager.cli_file_exists = Mock(return_value=True)
        mock_manager.get_cli_file_with_metadata = Mock(return_value=metadata_entry)

        # Setup recoater client mock
        mock_client = Mock()
        mock_client.upload_drum_geometry.return_value = {"success": True}

        # Override dependencies directly
        app.dependency_overrides[get_multilayer_job_manager] = lambda: mock_manager
        app.dependency_overrides[get_recoater_client] = lambda: mock_client

        try:
            # Step 3: Send specific layer to drum
            layer_response = client.post(f"/api/v1/print/cli/{file_id}/layer/2/send/0")

            assert layer_response.status_code == 200
            data = layer_response.json()
            assert data["success"] is True
            assert data["layer_num"] == 2
        finally:
            # Clean up dependency overrides
            if get_multilayer_job_manager in app.dependency_overrides:
                del app.dependency_overrides[get_multilayer_job_manager]
            if get_recoater_client in app.dependency_overrides:
                del app.dependency_overrides[get_recoater_client]
            assert data["drum_id"] == 0

    def test_cli_upload_and_range_send_workflow(self, client, sample_cli_content):
        """Test complete workflow: upload CLI file and send layer range."""
        # Step 1: Upload CLI file
        cli_file = BytesIO(sample_cli_content.encode('utf-8'))
        cli_file.name = "range_test.cli"

        upload_response = client.post(
            "/api/v1/print/cli/upload",
            files={"file": ("range_test.cli", cli_file, "application/octet-stream")}
        )

        assert upload_response.status_code == 200
        file_id = upload_response.json()["file_id"]

        # Step 2: Mock job manager and recoater client for range upload
        from app.dependencies import get_multilayer_job_manager, get_recoater_client
        from app.main import app
        
        # Setup cache mock with proper layer structure
        mock_layer = Mock(spec=CliLayer)
        mock_layer.z_height = 0.1
        mock_layer.polylines = []
        mock_layer.hatches = []

        mock_parsed_file = Mock(spec=ParsedCliFile)
        mock_parsed_file.layers = [mock_layer for _ in range(3)]
        mock_parsed_file.header_lines = ["$$HEADERSTART", "$$HEADEREND"]

        # Job manager returns metadata with parsed_file and original_filename
        metadata_entry = {
            'parsed_file': mock_parsed_file,
            'original_filename': 'range_test.cli'
        }

        mock_manager = Mock()
        mock_manager.cli_file_exists = Mock(return_value=True)
        mock_manager.get_cli_file_with_metadata = Mock(return_value=metadata_entry)

        # Setup recoater client mock
        mock_client = Mock()
        mock_client.upload_drum_geometry.return_value = {"success": True}

        # Override dependencies directly
        app.dependency_overrides[get_multilayer_job_manager] = lambda: mock_manager
        app.dependency_overrides[get_recoater_client] = lambda: mock_client

        try:
            # Step 3: Send layer range to drum
            range_response = client.post(
                f"/api/v1/print/cli/{file_id}/layers/send/1",
                json={"start_layer": 1, "end_layer": 3}
            )

            assert range_response.status_code == 200
            data = range_response.json()
            assert data["success"] is True
            assert data["layer_range"] == [1, 3]
        finally:
            # Clean up dependency overrides
            if get_multilayer_job_manager in app.dependency_overrides:
                del app.dependency_overrides[get_multilayer_job_manager]
            if get_recoater_client in app.dependency_overrides:
                del app.dependency_overrides[get_recoater_client]


class TestErrorHandlingScenarios:
    """Test error handling in various CLI scenarios."""

    def test_multimaterial_job_starts_after_single_cached_drum(self, client, sample_cli_content):
        """Start job should succeed when at least one drum has a cached file (no payload)."""
        from io import BytesIO
        content = sample_cli_content.encode('utf-8')
        files = {"file": ("single.cli", BytesIO(content), "application/octet-stream")}
        # Cache a single drum
        assert client.post("/api/v1/print/cli/upload/0", files=files).status_code == 200

        response = client.post("/api/v1/print/cli/start-multimaterial-job")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["job_id"] == "layer-by-layer-job"

    def test_multimaterial_job_start_with_cached_drums(self, client, sample_cli_content):
        """Start job should succeed after caching files to at least two drums."""
        from io import BytesIO
        content = sample_cli_content.encode('utf-8')
        files = {"file": ("dual.cli", BytesIO(content), "application/octet-stream")}
        # Cache two drums
        assert client.post("/api/v1/print/cli/upload/0", files=files).status_code == 200
        assert client.post("/api/v1/print/cli/upload/1", files=files).status_code == 200

        response = client.post("/api/v1/print/cli/start-multimaterial-job")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["job_id"] == "layer-by-layer-job"

    def test_layer_send_with_connection_error(self, client, sample_cli_content):
        """Test layer send with recoater connection error."""
        # Upload CLI file first
        cli_file = BytesIO(sample_cli_content.encode('utf-8'))
        cli_file.name = "error_test.cli"

        upload_response = client.post(
            "/api/v1/print/cli/upload",
            files={"file": ("error_test.cli", cli_file, "application/octet-stream")}
        )
        
        # Ensure upload was successful first
        assert upload_response.status_code == 200
        file_id = upload_response.json()["file_id"]

        # Configure the recoater client to raise a connection error
        from app.dependencies import get_recoater_client, get_multilayer_job_manager
        from app.main import app
        from infrastructure.recoater_client import RecoaterConnectionError
        from infrastructure.cli_editor.editor import CliLayer, ParsedCliFile
        
        mock_client = Mock()
        mock_client.upload_drum_geometry.side_effect = RecoaterConnectionError("Connection failed")
        
        # Create a proper mock layer and parsed file for this test
        mock_layer = Mock(spec=CliLayer)
        mock_layer.z_height = 0.1
        mock_layer.polylines = []
        mock_layer.hatches = []
        
        mock_parsed_file = Mock(spec=ParsedCliFile)
        mock_parsed_file.layers = [mock_layer, mock_layer, mock_layer]  # 3 layers
        mock_parsed_file.header_lines = ["$$HEADERSTART", "$$HEADEREND"]
        
        mock_manager = Mock()
        mock_manager.get_cli_file_with_metadata.return_value = {
            'parsed_file': mock_parsed_file,
            'original_filename': 'error_test.cli'
        }
        
        # Mock the CLI generation to return actual CLI data string (not Mock)
        # This prevents the "object of type 'Mock' has no len()" error  
        with patch('infrastructure.cli_editor.editor.Editor.generate_single_layer_ascii_cli') as mock_generate:
            mock_generate.return_value = b"$$LAYER/0.1\n$$POLYLINE/1,0,0,0,0\n0.0,0.0\n$$HEADEREND"
            
            # Override both the recoater client and job manager
            app.dependency_overrides[get_recoater_client] = lambda: mock_client
            app.dependency_overrides[get_multilayer_job_manager] = lambda: mock_manager
            
            try:
                response = client.post(f"/api/v1/print/cli/{file_id}/layer/1/send/0")
                assert response.status_code == 503
                assert "Connection error" in response.json()["detail"]
            finally:
                # Clean up dependency overrides
                if get_recoater_client in app.dependency_overrides:
                    del app.dependency_overrides[get_recoater_client]
                if get_multilayer_job_manager in app.dependency_overrides:
                    del app.dependency_overrides[get_multilayer_job_manager]


class TestPerformanceScenarios:
    """Test performance-related CLI scenarios."""

    def test_large_cli_file_handling(self, client):
        """Test handling of large CLI files."""
        # Create a large CLI file content (simulate many layers)
        large_cli_content = """$$HEADERSTART
$$ASCII
$$UNITS/00000000.005000
$$VERSION/200
$$LABEL/1,large_part
$$LAYERS/000100
$$HEADEREND
$$GEOMETRYSTART"""

        # Add 100 layers
        for i in range(100):
            layer_height = i * 0.1
            large_cli_content += f"""
$$LAYER/{layer_height:.1f}
$$POLYLINE/1,0,0,0,0
{i}.0,{i}.0
{i+10}.0,{i}.0
{i+10}.0,{i+10}.0
{i}.0,{i+10}.0
{i}.0,{i}.0"""

        large_cli_content += "\n$$HEADEREND"

        # Test upload
        cli_file = BytesIO(large_cli_content.encode('utf-8'))
        cli_file.name = "large_part.cli"

        response = client.post(
            "/api/v1/print/cli/upload",
            files={"file": ("large_part.cli", cli_file, "application/octet-stream")}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["total_layers"] == 100

    def test_concurrent_cli_uploads(self, client, sample_cli_content):
        """Test concurrent CLI file uploads."""
        import threading
        import time

        results = []

        def upload_cli_file(file_suffix):
            cli_file = BytesIO(sample_cli_content.encode('utf-8'))
            cli_file.name = f"concurrent_test_{file_suffix}.cli"

            response = client.post(
                "/api/v1/print/cli/upload",
                files={"file": (f"concurrent_test_{file_suffix}.cli", cli_file, "application/octet-stream")}
            )
            results.append(response.status_code)

        # Create multiple threads for concurrent uploads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=upload_cli_file, args=(i,))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # All uploads should succeed
        assert all(status == 200 for status in results)
        assert len(results) == 5
