"""
Utility functions for print API.
No endpoints defined here.
"""
from __future__ import annotations

import logging
import uuid
from pathlib import Path as PathLib

logger = logging.getLogger(__name__)

# Directory for temporary CLI files (for verification purposes)
TEMP_CLI_DIR = PathLib("temp_cli_files")


def create_temp_cli_file(cli_data: bytes, file_prefix: str = "layer_range") -> PathLib:
    """
    Create a temporary CLI file for verification purposes.

    Args:
        cli_data: The ASCII CLI file data as bytes
        file_prefix: Prefix for the temporary file name

    Returns:
        Path to the created temporary file
    """
    # Ensure temp directory exists
    TEMP_CLI_DIR.mkdir(exist_ok=True)

    # Create temporary file with timestamp
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    temp_filename = f"{file_prefix}_{timestamp}_{uuid.uuid4().hex[:8]}.cli"
    temp_file_path = TEMP_CLI_DIR / temp_filename

    # Write CLI data to file
    with open(temp_file_path, 'wb') as f:
        f.write(cli_data)

    logger.info(f"Created temporary CLI file: {temp_file_path}")
    return temp_file_path


def cleanup_temp_cli_file(file_path: PathLib) -> bool:
    """
    Delete a temporary CLI file and remove the temp directory if it becomes empty.

    Args:
        file_path: Path to the temporary file to delete

    Returns:
        True if file was deleted successfully, False otherwise
    """
    try:
        if file_path.exists():
            file_path.unlink()
            logger.info(f"Deleted temporary CLI file: {file_path}")
            # Attempt to remove temp directory if it's empty
            try:
                temp_dir = file_path.parent
                if temp_dir.exists() and not any(temp_dir.iterdir()):
                    temp_dir.rmdir()
                    logger.info(f"Removed temporary CLI directory: {temp_dir}")
            except Exception:
                # Non-fatal if we can't remove the directory (might not be empty or in use)
                pass
            return True
        else:
            logger.warning(f"Temporary CLI file not found for deletion: {file_path}")
            return False
    except Exception as e:
        logger.error(f"Failed to delete temporary CLI file {file_path}: {e}")
        return False


def cleanup_all_temp_cli_files() -> bool:
    """
    Delete all temporary CLI files and the temp directory.

    This function is useful for cleanup after test runs or when
    a complete reset of temporary files is needed.

    Returns:
        True if cleanup was successful, False otherwise
    """
    try:
        if TEMP_CLI_DIR.exists():
            import shutil
            shutil.rmtree(TEMP_CLI_DIR)
            logger.info(f"Removed all temporary CLI files and directory: {TEMP_CLI_DIR}")
            return True
        else:
            logger.info("No temporary CLI directory to clean up")
            return True
    except Exception as e:
        logger.error(f"Failed to cleanup all temporary CLI files: {e}")
        return False

