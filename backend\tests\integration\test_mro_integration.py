"""
Integration Tests for Method Resolution Order
============================================

These tests validate MRO behavior in realistic integration scenarios,
ensuring that method calls work correctly across complex inheritance hierarchies.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock

from app.services.opcua.opcua_service import OPCUAService
from app.services.job_management.multimaterial_job_service import MultiMaterialJobService


class TestOPCUAServiceIntegration:
    """Integration tests for OPCUAService MRO behavior"""

    @pytest.mark.asyncio
    async def test_variable_write_triggers_events_in_realistic_scenario(self):
        """Test event triggering in a realistic job scenario"""
        service = OPCUAService()
        await service.connect()
        
        try:
            # Simulate a realistic job monitoring scenario
            job_events = []
            layer_events = []
            error_events = []
            
            async def job_handler(name, value):
                job_events.append((name, value))
            
            async def layer_handler(name, value):
                layer_events.append((name, value))
            
            async def error_handler(name, value):
                error_events.append((name, value))
            
            # Subscribe to different types of events
            await service.subscribe_to_changes(["job_active"], job_handler)
            await service.subscribe_to_changes(["current_layer", "total_layers"], layer_handler)
            await service.subscribe_to_changes(["backend_error", "plc_error"], error_handler)
            
            # Simulate a job lifecycle
            await service.set_job_active(10)  # Should trigger job_active and total_layers
            await service.update_layer_progress(3)  # Should trigger current_layer
            await service.set_backend_error(True)  # Should trigger backend_error
            
            # Allow events to propagate
            await asyncio.sleep(0.1)
            
            # Verify all events were triggered due to correct MRO
            assert any(name == "job_active" and value is True for name, value in job_events)
            assert any(name == "total_layers" and value == 10 for name, value in layer_events)
            assert any(name == "current_layer" and value == 3 for name, value in layer_events)
            assert any(name == "backend_error" and value is True for name, value in error_events)
            
        finally:
            await service.disconnect()

    @pytest.mark.asyncio 
    async def test_method_chaining_works_correctly(self):
        """Test that method chaining works correctly with the MRO"""
        service = OPCUAService()
        await service.connect()
        
        try:
            # Test chaining of related operations
            success1 = await service.set_job_active(5)
            success2 = await service.update_layer_progress(1)
            success3 = await service.set_recoater_ready_to_print(True)
            
            assert all([success1, success2, success3])
            
            # Verify state is consistent
            assert await service.read_variable("job_active") is True
            assert await service.read_variable("current_layer") == 1
            assert await service.read_variable("total_layers") == 5
            assert await service.read_variable("recoater_ready_to_print") is True
            
        finally:
            await service.disconnect()


class TestMultiMaterialJobServiceIntegration:
    """Integration tests for MultiMaterialJobService MRO behavior"""

    def test_method_resolution_in_complex_workflow(self):
        """Test that methods resolve correctly in a complex job workflow"""
        # Mock dependencies
        mock_recoater = Mock()
        mock_recoater.upload_layer = AsyncMock(return_value=True)
        mock_recoater.get_drum_status = Mock(return_value={"ready": True})
        
        service = MultiMaterialJobService(mock_recoater)
        
        # Test that all expected methods are available and come from correct mixins

        # LayerProcessingMixin methods (consolidated job lifecycle + layer operations)
        assert hasattr(service, 'get_job_status')
        assert hasattr(service, 'cancel_job')
        assert hasattr(service, 'get_drum_status')
        assert hasattr(service, 'clear_job_error_flags')

        # OPCUACoordinationMixin methods
        assert hasattr(service, 'setup_opcua_job')
        assert hasattr(service, 'wait_for_layer_completion')
        assert hasattr(service, 'clear_error_flags')

        # CliCachingMixin methods
        assert hasattr(service, 'add_cli_file')
        assert hasattr(service, 'cache_cli_file_for_drum')

    def test_error_handling_method_resolution(self):
        """Test that error handling methods resolve to correct implementations"""
        mock_recoater = Mock()
        service = MultiMaterialJobService(mock_recoater)
        
        # Test that error clearing methods exist and are different
        opcua_clear = service.clear_error_flags
        job_clear = service.clear_job_error_flags
        unified_clear = service.clear_all_error_flags

        assert opcua_clear != job_clear
        assert opcua_clear != unified_clear
        assert callable(opcua_clear)
        assert callable(job_clear)
        assert callable(unified_clear)


class TestCrossServiceIntegration:
    """Test MRO behavior when services interact with each other"""

    @pytest.mark.asyncio
    async def test_opcua_job_service_interaction(self):
        """Test MRO when OPCUAService and job services interact"""
        opcua_service = OPCUAService()
        await opcua_service.connect()
        
        try:
            # Mock job service
            mock_recoater = Mock()
            job_service = MultiMaterialJobService(mock_recoater)
            
            # Test that OPC UA events can be monitored during job operations
            opcua_events = []
            
            async def opcua_event_handler(name, value):
                opcua_events.append((name, value))
            
            await opcua_service.subscribe_to_changes(["job_active", "current_layer"], opcua_event_handler)
            
            # Simulate job service updating OPC UA variables
            await opcua_service.set_job_active(5)
            await opcua_service.update_layer_progress(2)
            
            await asyncio.sleep(0.1)
            
            # Verify events were captured correctly
            assert len(opcua_events) >= 2
            assert any(name == "job_active" for name, value in opcua_events)
            assert any(name == "current_layer" for name, value in opcua_events)
            
        finally:
            await opcua_service.disconnect()


class TestMROEdgeCases:
    """Test edge cases and potential MRO issues"""

    def test_multiple_inheritance_diamond_problem_prevention(self):
        """Test that diamond inheritance problems are avoided"""
        # Verify that our mixin hierarchies don't create diamond inheritance
        
        # OPCUAService hierarchy
        opcua_mro = OPCUAService.__mro__
        opcua_names = [cls.__name__ for cls in opcua_mro]
        
        # Should not have duplicates (diamond problem indicator)
        assert len(opcua_names) == len(set(opcua_names)), \
            f"OPCUAService MRO has duplicates: {opcua_names}"
        
        # Job service hierarchy
        job_mro = MultiMaterialJobService.__mro__
        job_names = [cls.__name__ for cls in job_mro]
        
        assert len(job_names) == len(set(job_names)), \
            f"MultiMaterialJobService MRO has duplicates: {job_names}"

    @pytest.mark.asyncio
    async def test_super_calls_work_correctly(self):
        """Test that super() calls work correctly in mixin chains"""
        service = OPCUAService()
        await service.connect()
        
        try:
            # This tests the super() chain: CoordinationMixin -> ServerMixin
            success = await service.write_variable("current_layer", 42)
            assert success
            
            # Verify the value was actually stored (tests ServerMixin functionality)
            value = await service.read_variable("current_layer")
            assert value == 42
            
        finally:
            await service.disconnect()

    def test_method_signature_consistency(self):
        """Test that overridden methods have consistent signatures"""
        from app.services.opcua.mixins import ServerMixin, CoordinationMixin
        import inspect
        
        # Get method signatures
        server_write_sig = inspect.signature(ServerMixin.write_variable)
        coord_write_sig = inspect.signature(CoordinationMixin.write_variable)
        
        # Parameters should be compatible (allowing for different return type annotations)
        server_params = list(server_write_sig.parameters.keys())
        coord_params = list(coord_write_sig.parameters.keys())
        
        assert server_params == coord_params, \
            f"write_variable signatures should be compatible: {server_params} vs {coord_params}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
