"""
OPC UA Services
===============

Primary entry point for OPC UA industrial automation coordination and communication.
Provides unified service for server management, variable coordination, and system monitoring.

Exposed Public API:
- OPCUAService: Main service class for OPC UA operations
- opcua_service: Global singleton instance for convenience

Public Methods (via OPCUAService):
- initialize(): Start server and establish connection
- shutdown(): Disconnect and stop server
- connect(): Establish coordination connection
- disconnect(): Close coordination connection
- is_connected(): Check connection status
- start_server(): Start the OPC UA server
- stop_server(): Stop the OPC UA server
- write_variable(name, value): Write value to OPC UA variable
- read_variable(name): Read value from OPC UA variable
- subscribe_to_changes(variables, handler): Subscribe to variable change events

CoordinationMixin Methods (Business Logic Coordination):
- set_job_active(total_layers): Set job active with total layers
- set_job_inactive(): Set job inactive and clear all flags
- update_layer_progress(current_layer): Update current layer progress
- set_recoater_ready_to_print(ready): Signal recoater ready to print
- set_recoater_layer_complete(complete): Signal layer completion
- set_backend_error(error): Set backend error flag
- set_plc_error(error): Set PLC error flag
- clear_error_flags(): Clear all error flags
- get_backend_error(): Get backend error status
- get_plc_error(): Get PLC error status
- get_server_status(): Get comprehensive server status

ServerMixin Methods (Server Lifecycle):
- start_server(): Start the OPC UA server
- stop_server(): Stop the OPC UA server
- is_server_running: Property to check server status
- get_variable_names(): Get list of available variables

MonitoringMixin Methods (Health Monitoring):
- start_monitoring(interval): Start health monitoring loop
- stop_monitoring(): Stop health monitoring

Private Helper Methods (via OPCUAService):
- _trigger_event_handlers(name, value): Trigger subscribed event handlers
"""

from .opcua_service import OPCUAService, opcua_service

__all__ = [
    'OPCUAService',
    'opcua_service',
]
