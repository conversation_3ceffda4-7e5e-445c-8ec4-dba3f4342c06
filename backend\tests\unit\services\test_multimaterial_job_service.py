"""
Unit tests for MultiMaterialJobService public API
=================================================

Focus: method availability, basic happy path delegation, and error handling paths
with mocked dependencies.
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch

from app.services.job_management import MultiMaterialJobService, MultiMaterialJobError
from app.models.multilayer_job import MultiMaterialJobState


@pytest.fixture
def mock_recoater_client():
    client = Mock()
    client.upload_drum_geometry = AsyncMock(return_value={"success": True})
    client.get_drum_status = AsyncMock(return_value={"status": "ready"})
    return client


@pytest.fixture
def service(mock_recoater_client):
    return MultiMaterialJobService(mock_recoater_client)


@pytest.mark.asyncio
async def test_service_has_public_api(service):
    # Modern workflow (from MultiMaterialJobService)
    assert hasattr(service, "start_layer_by_layer_job")
    
    # Lifecycle (from LayerProcessingMixin)
    assert hasattr(service, "cancel_job")
    assert hasattr(service, "get_job_status")
    assert hasattr(service, "clear_job_error_flags")
    
    # Legacy methods removed in Phase 3
    assert not hasattr(service, "create_job")  # Replaced by start_layer_by_layer_job
    assert not hasattr(service, "start_job")  # Replaced by start_layer_by_layer_job
    assert not hasattr(service, "upload_layer_to_all_drums")  # Internal to modern workflow
    
    # OPC UA Coordination (from OPCUACoordinationMixin)
    assert hasattr(service, "setup_opcua_job")
    assert hasattr(service, "cleanup_opcua_job")
    assert hasattr(service, "clear_error_flags")
    assert hasattr(service, "wait_for_layer_completion")
    
    # CLI operations (from CliCachingMixin)
    assert hasattr(service, "add_cli_file")
    assert hasattr(service, "get_max_layers")
    
    # Unified error handling (from main service)
    assert hasattr(service, "clear_all_error_flags")


@pytest.mark.asyncio
async def test_modern_workflow_sets_state(service, mocker):
    # Test the modern cache-based workflow (same as production)
    parsed_file = Mock()
    parsed_file.layers = [Mock(polylines=[], hatches=[], z_height=0.1) for _ in range(3)]
    parsed_file.header_lines = ["$$HEADERSTART", "$$HEADEREND"]
    
    # Use modern drum cache approach
    service.cache_cli_file_for_drum(0, parsed_file, "test.cli")
    
    # Avoid heavy editor path by patching CLI generation
    service.cli_parser.generate_single_layer_ascii_cli = Mock(return_value=b"CLI")
    
    # Patch OPC UA service to avoid real calls
    with patch("app.services.opcua.opcua_service.opcua_service") as mock_opcua:
        mock_opcua.set_job_active = AsyncMock()
        mock_opcua.update_layer_progress = AsyncMock()
        mock_opcua.clear_error_flags = AsyncMock()
        mock_opcua.set_recoater_ready_to_print = AsyncMock()
        mock_opcua.set_recoater_layer_complete = AsyncMock()
        mock_opcua.get_backend_error = Mock(return_value=False)
        mock_opcua.get_plc_error = Mock(return_value=False)
        
        # Mock recoater state and methods
        service.recoater_client.get_state = Mock(return_value={"state": "ready"})
        service.recoater_client.upload_cli_data = AsyncMock()
        service.recoater_client.start_print_job = Mock()
        
        # Test modern unified workflow
        result = await service.start_layer_by_layer_job()
        assert result is True
        
        # Verify the job state was created internally
        assert service.current_job is not None
        assert service.current_job.total_layers == 3


@pytest.mark.asyncio
async def test_clear_errors_transitions_state(service):
    # Test the unified error clearing functionality
    with patch("app.services.opcua.opcua_service.opcua_service") as mock_opcua:
        mock_opcua.clear_error_flags = AsyncMock(return_value=True)

        # Test unified error clearing
        result = await service.clear_all_error_flags()
        assert result is True

        # Verify OPC UA error clearing was called
        mock_opcua.clear_error_flags.assert_called_once()


@pytest.mark.asyncio
async def test_layer_completion_workflow(service):
    """Test the layer completion workflow using the modern unified approach."""
    # Set up using modern drum cache approach
    parsed_file = Mock()
    parsed_file.layers = [Mock(polylines=[], hatches=[], z_height=0.1)]
    parsed_file.header_lines = ["$$HEADERSTART", "$$HEADEREND"]
    
    # Use modern cache approach
    service.cache_cli_file_for_drum(0, parsed_file, "test.cli")
    service.cli_parser.generate_single_layer_ascii_cli = Mock(return_value=b"CLI")

    # Test layer completion workflow with mocked OPC UA and recoater
    with patch("app.services.opcua.opcua_service.opcua_service") as mock_opcua:
        mock_opcua.get_backend_error = Mock(return_value=False)
        mock_opcua.get_plc_error = Mock(return_value=False)
        mock_opcua.set_job_active = AsyncMock()
        mock_opcua.update_layer_progress = AsyncMock()
        mock_opcua.set_recoater_ready_to_print = AsyncMock()
        mock_opcua.set_recoater_layer_complete = AsyncMock()
        
        # Mock recoater state to return 'ready' immediately
        service.recoater_client.get_state = Mock(return_value={"state": "ready"})
        service.recoater_client.upload_cli_data = AsyncMock()
        service.recoater_client.start_print_job = Mock()

        # Test waiting for layer completion (this tests the layer completion logic)
        result = await service.wait_for_layer_completion()
        assert result is True

