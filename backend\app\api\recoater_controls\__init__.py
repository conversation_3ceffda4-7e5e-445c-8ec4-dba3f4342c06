"""
Recoater Controls API Package
============================

This package provides the recoater control API endpoints for the Recoater HMI.
It handles requests for drum motion, ejection pressure, suction pressure, blade controls, and leveler operations.

The package is organized into the following modules:
- models: Pydantic request/response models
- drum: Drum-related endpoints (motion, ejection, suction)
- blade: Blade-related endpoints (screws info, motion)
- leveler: Leveler-related endpoints (pressure, sensor)
"""

from fastapi import APIRouter

from .drum import router as drum_router
from .blade import router as blade_router
from .leveler import router as leveler_router

# Create main router with /recoater prefix
router = APIRouter(prefix="/recoater", tags=["recoater"])

# Include all sub-routers
router.include_router(drum_router)
router.include_router(blade_router)
router.include_router(leveler_router)
