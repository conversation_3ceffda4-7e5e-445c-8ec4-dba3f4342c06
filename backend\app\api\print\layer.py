"""
Layer Management Endpoints
==========================

Provides endpoints for managing layer parameters and preview generation
for print job configuration and visualization.

Endpoints:
- GET /layer/parameters: Retrieve current layer printing parameters.
- PUT /layer/parameters: Update layer printing parameters.
- GET /layer/preview: Generate and retrieve layer preview as PNG image.
"""
import logging
from fastapi import APIRouter, HTTPException, Depends, Response

from app.dependencies import get_recoater_client
from infrastructure.recoater_client import (
    RecoaterClient,
    RecoaterConnectionError,
    RecoaterAPIError,
)
from .models import LayerParametersRequest, LayerParametersResponse

logger = logging.getLogger(__name__)
router = APIRouter()

# Direct API call to get layer parameters
@router.get("/layer/parameters", response_model=LayerParametersResponse)
async def get_layer_parameters(
    recoater_client: RecoaterClient = Depends(get_recoater_client),
) -> LayerParametersResponse:
    """Get the current parameters of the layer."""
    try:
        logger.info("Getting layer parameters")
        result = recoater_client.get_layer_parameters()
        return LayerParametersResponse(**result)
    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting layer parameters: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting layer parameters: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error getting layer parameters: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")

# Direct API call to set layer parameters using JSON payload
@router.put("/layer/parameters")
async def set_layer_parameters(
    parameters: LayerParametersRequest,
    recoater_client: RecoaterClient = Depends(get_recoater_client),
):
    """Set the parameters of the current layer."""
    try:
        logger.info(f"Setting layer parameters: {parameters.model_dump()}")
        recoater_client.set_layer_parameters(
            filling_id=parameters.filling_id,
            speed=parameters.speed,
            powder_saving=parameters.powder_saving,
            x_offset=parameters.x_offset,
        )
        return {
            "success": True,
            "message": "Layer parameters set successfully",
            "parameters": parameters.model_dump(),
        }
    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting layer parameters: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error setting layer parameters: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error setting layer parameters: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")

# Direct API call to get layer preview from recoater as a PNG byte stream for direct output
@router.get("/layer/preview")
async def get_layer_preview(
    recoater_client: RecoaterClient = Depends(get_recoater_client),
) -> Response:
    """Get layer preview as PNG image."""
    try:
        logger.info("Getting layer preview")
        image_data = recoater_client.get_layer_preview()
        return Response(
            content=image_data,
            media_type="image/png",
            headers={"Content-Disposition": "inline; filename=layer_preview.png"},
        )
    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting layer preview: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting layer preview: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error getting layer preview: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")

