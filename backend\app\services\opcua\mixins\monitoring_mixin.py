"""
MonitoringMixin
===============

Provides health monitoring and system reliability features for OPC UA operations.

Clean Architecture Role: Cross-cutting Infrastructure Concerns
Responsibilities:
- System health monitoring with configurable heartbeat intervals
- Error detection, logging, and basic recovery strategies
- Connection health validation and status reporting
- Lightweight performance metrics and diagnostic information
- Graceful shutdown handling and resource cleanup

Architecture Pattern: Background Task Management
- Uses asyncio.create_task() for non-blocking monitoring loops
- Implements proper cancellation and cleanup patterns
- Maintains separation from business logic in other mixins

Key Features:
- Configurable monitoring intervals (default 2.0 seconds)
- Automatic task cancellation on shutdown
- Exception handling to prevent monitoring failures from affecting main operations
- Lightweight design suitable for production environments

Dependencies:
- asyncio: For background task management and cancellation
- logging: For health status and error reporting
- No external protocol libraries (keeps infrastructure layer clean)

Public Methods:
- start_monitoring(interval): Start background monitoring loop with specified interval
- stop_monitoring(): Stop monitoring loop and cleanup resources gracefully

Private Methods:
- _monitoring_loop(interval): Background monitoring task implementation

Integration Notes:
- Designed to work alongside ServerMixin and CoordinationMixin
- Can be extended with concrete health checks in production implementations
- Monitoring failures are logged but don't interrupt main service operations
"""
from __future__ import annotations

import asyncio
import logging
from typing import Optional


class MonitoringMixin:
    monitoring_active: bool = False
    _monitor_task: Optional[asyncio.Task] = None
    _logger: logging.Logger

    async def start_monitoring(self, interval: float = 2.0) -> None:
        if self.monitoring_active:
            return
        self.monitoring_active = True
        self._monitor_task = asyncio.create_task(self._monitoring_loop(interval))
        self._logger.info("OPC UA monitoring started")

    async def stop_monitoring(self) -> None:
        self.monitoring_active = False
        if self._monitor_task and not self._monitor_task.done():
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
            finally:
                self._monitor_task = None
        self._logger.info("OPC UA monitoring stopped")

    async def _monitoring_loop(self, interval: float) -> None:
        try:
            while self.monitoring_active:
                # Minimal health check placeholders; adapters can be consulted here
                # without importing protocol libraries in this mixin
                await asyncio.sleep(interval)
        except asyncio.CancelledError:
            self._logger.debug("Monitoring loop cancelled")
        except Exception as e:
            self._logger.error(f"Monitoring loop error: {e}")

