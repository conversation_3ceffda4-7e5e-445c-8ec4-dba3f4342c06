"""
CLI File Parser Mixin
=====================

This module provides the main CliFileParser mixin class that inherits ASCII and binary parsing
capabilities through multiple inheritance from specialized parser mixins.

Key Features:
- Auto-detect ASCII vs binary CLI formats
- Inherit from AsciiCliParser and BinaryCliParser mixins
- Maintain backward compatibility with existing interfaces
- Robust error handling with detailed error messages

Architecture:
- CliFileParser: Main mixin that inherits from specialized parser mixins
- AsciiCliParser: Mixin for ASCII CLI file parsing (inherited)
- BinaryCliParser: Mixin for binary CLI file parsing (inherited)

Dependencies:
- Relies on cli_models for data structures and cli_exceptions for error handling.
- Inherits from ascii_cli_parser and binary_cli_parser mixins.

Usage:
    class MyClass(CliFileParser):
        pass
    instance = MyClass()
    parsed = instance.parse(cli_bytes)
"""
import logging
from typing import Optional

from .cli_models import ParsedCliFile
from .ascii_cli_parser import AsciiCliParser
from .binary_cli_parser import BinaryCliParser



class CliFileParser(AsciiCliParser, BinaryCliParser):
    """Main CLI file parser mixin that inherits ASCII and binary parsing capabilities."""

    @property
    def logger(self):
        """Get logger from parent class or create a default one."""
        if hasattr(self, '_logger') and self._logger:
            return self._logger
        return logging.getLogger(__name__)



    def parse(self, cli_byte_stream: bytes) -> ParsedCliFile:
        """Auto‐detect ASCII vs. binary CLI and delegate to appropriate parser method."""
        try:
            text = cli_byte_stream.decode("ascii", errors="strict")
            # Look for ASCII CLI patterns (commands followed by parameters on same line)
            # Support both space and slash delimited formats
            if "$$HEADEREND" in text and ("$$LAYER " in text or "$$POLYLINE " in text or "$$LAYER/" in text or "$$POLYLINE/" in text):
                return self.parse_ascii(text)
        except UnicodeDecodeError:
            pass
        
        # Default to binary parsing since decoding binary as ascii would return gibberish
        return self.parse_binary(cli_byte_stream)
    