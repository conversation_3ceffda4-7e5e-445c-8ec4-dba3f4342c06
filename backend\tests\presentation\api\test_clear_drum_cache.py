import io
from fastapi.testclient import TestClient

from app.main import app

client = TestClient(app)


def sample_cli_text():
    return """$$HEADERSTART
$$ASCII
$$UNITS/00000000.005000
$$VERSION/200
$$LABEL/1,test_part
$$LAYERS/000003
$$HEADEREND
$$GEOMETRYSTART
$$LAYER/0.0
$$POLYLINE/1,0,0,0,0
0.0,0.0
10.0,0.0
10.0,10.0
0.0,10.0
0.0,0.0
$$LAYER/0.1
$$POLYLINE/1,0,0,0,0
1.0,1.0
9.0,1.0
9.0,9.0
1.0,9.0
1.0,1.0
$$LAYER/0.2
$$POLYLINE/1,0,0,0,0
2.0,2.0
8.0,2.0
8.0,8.0
2.0,8.0
2.0,2.0
$$HEADEREND"""


def upload_cli_to_drum(drum_id: int, content: bytes = b"solid cli"):
    files = {"file": ("test.cli", io.BytesIO(content), "application/octet-stream")}
    return client.post(f"/api/v1/print/cli/upload/{drum_id}", files=files)


def get_cache_status():
    return client.get("/api/v1/print/cli/drum-cache-status")


def test_clear_drum_cache_endpoint_clears_specific_drum_only():
    # Upload to two drums using a valid sample CLI content
    content = sample_cli_text().encode('utf-8')
    assert upload_cli_to_drum(0, content).status_code == 200
    assert upload_cli_to_drum(1, content).status_code == 200

    # Verify cache status shows cached for both 0 and 1, not 2
    status_before = get_cache_status()
    assert status_before.status_code == 200
    data_before = status_before.json()
    assert data_before["has_cached_files"] is True
    assert data_before["drums"]["0"]["cached"] is True
    assert data_before["drums"]["1"]["cached"] is True
    assert data_before["drums"]["2"]["cached"] is False

    # Clear cache for drum 0 only
    resp0 = client.post("/api/v1/print/cli/clear-drum-cache/0")
    assert resp0.status_code == 200
    assert resp0.json()["success"] is True

    # Verify only drum 0 cleared
    status_mid = get_cache_status()
    data_mid = status_mid.json()
    assert data_mid["drums"]["0"]["cached"] is False
    assert data_mid["drums"]["1"]["cached"] is True

    # Clear cache for drum 1 only
    resp1 = client.post("/api/v1/print/cli/clear-drum-cache/1")
    assert resp1.status_code == 200
    assert resp1.json()["success"] is True

    # Verify both 0 and 1 cleared; 2 remains unchanged
    status_after = get_cache_status()
    assert status_after.status_code == 200
    data_after = status_after.json()
    assert data_after["has_cached_files"] in (False, True)  # depends if other cached states exist
    assert data_after["drums"]["0"]["cached"] is False
    assert data_after["drums"]["1"]["cached"] is False

