"""
Unit Tests for MultiMaterial Job Service MRO
===========================================

These tests ensure that the MultiMaterialJobService mixin architecture
doesn't suffer from similar MRO issues as the OPCUAService had.
"""

import pytest
from unittest.mock import Mock, AsyncMock

from app.services.job_management.multimaterial_job_service import MultiMaterialJobService


class TestMultiMaterialJobServiceMRO:
    """Test MRO-related behavior for MultiMaterialJobService"""

    @pytest.fixture
    def mock_recoater_client(self):
        """Mock recoater client for testing"""
        mock = Mock()
        mock.upload_layer = AsyncMock(return_value=True)
        mock.get_drum_status = Mock(return_value={"ready": True})
        mock.send_layer_to_drum = AsyncMock(return_value=True)
        return mock

    @pytest.fixture
    def job_service(self, mock_recoater_client):
        """Create job service with mocked dependencies"""
        return MultiMaterialJobService(mock_recoater_client)

    def test_mro_order_is_correct(self, job_service):
        """Test that MRO order is as expected for proper method resolution"""
        mro_names = [cls.__name__ for cls in job_service.__class__.__mro__]

        expected_order = [
            'MultiMaterialJobService',
            'LayerProcessingMixin',
            'OPCUACoordinationMixin',
            'CliCachingMixin',
            'object'
        ]

        assert mro_names == expected_order, f"MRO order should be {expected_order}, got {mro_names}"

    def test_all_expected_methods_available(self, job_service):
        """Test that all expected methods from consolidated mixins are available"""

        # From LayerProcessingMixin (consolidated job lifecycle + layer operations)
        assert hasattr(job_service, 'cancel_job')
        assert hasattr(job_service, 'get_job_status')
        assert hasattr(job_service, 'get_drum_status')
        assert hasattr(job_service, 'clear_job_error_flags')
        
        # Legacy methods removed in Phase 3 cleanup
        assert not hasattr(job_service, 'create_job')  # Replaced by start_layer_by_layer_job
        assert not hasattr(job_service, 'start_job')  # Replaced by start_layer_by_layer_job  
        assert not hasattr(job_service, 'upload_layer_to_all_drums')  # Internal to modern workflow

        # From OPCUACoordinationMixin
        assert hasattr(job_service, 'setup_opcua_job')
        assert hasattr(job_service, 'cleanup_opcua_job')
        assert hasattr(job_service, 'reset_opcua_layer_flags')
        assert hasattr(job_service, 'update_opcua_layer_progress')
        assert hasattr(job_service, 'signal_opcua_layer_complete')
        assert hasattr(job_service, 'wait_for_layer_completion')
        assert hasattr(job_service, 'clear_error_flags')

        # From CliCachingMixin
        assert hasattr(job_service, 'add_cli_file')
        assert hasattr(job_service, 'get_cli_file')
        assert hasattr(job_service, 'cache_cli_file_for_drum')
        assert hasattr(job_service, 'get_cached_file_for_drum')
        assert hasattr(job_service, 'get_max_layers')
        assert hasattr(job_service, 'has_cached_files')

    def test_unified_error_handling_methods(self, job_service):
        """Test that unified error handling methods exist"""

        # OPCUACoordinationMixin has clear_error_flags for OPC UA errors
        assert hasattr(job_service, 'clear_error_flags')

        # LayerProcessingMixin has clear_job_error_flags for job-specific errors
        assert hasattr(job_service, 'clear_job_error_flags')

        # Main service has unified clear_all_error_flags
        assert hasattr(job_service, 'clear_all_error_flags')

        # They should be different methods
        assert job_service.clear_error_flags != job_service.clear_job_error_flags
        assert job_service.clear_error_flags != job_service.clear_all_error_flags

    def test_method_resolution_precedence(self, job_service):
        """Test that method resolution follows expected precedence"""

        # For methods that exist in multiple mixins, the first one in MRO should win
        # Test that job status comes from LayerProcessingMixin (first in MRO)
        job_method = job_service.get_job_status
        assert hasattr(job_method, '__self__')

        # Test that OPC UA methods come from OPCUACoordinationMixin
        opcua_method = job_service.clear_error_flags
        assert hasattr(opcua_method, '__self__')

        # Test that CLI methods come from CliCachingMixin
        cli_method = job_service.add_cli_file
        assert hasattr(cli_method, '__self__')

    @pytest.mark.asyncio
    async def test_async_method_compatibility(self, job_service):
        """Test that async methods work correctly across the MRO"""
        
        # Test that async methods from different mixins can be called
        try:
            # This would normally fail without proper setup, but we're testing MRO
            result = await job_service.upload_layer_to_all_drums(1)
            # We expect this to fail due to missing job state, which is correct behavior
        except Exception as e:
            # This is expected since we don't have a current job set up
            assert "current_job" in str(e).lower() or "job" in str(e).lower()

    def test_mixin_state_initialization(self, job_service):
        """Test that mixin state is properly initialized"""
        
        # Check that required attributes are initialized
        assert hasattr(job_service, 'recoater_client')
        assert hasattr(job_service, 'cli_parser')
        assert hasattr(job_service, 'current_job')
        assert hasattr(job_service, 'cli_cache')
        
        # Check coordination attributes  
        assert hasattr(job_service, 'drum_upload_delay')
        assert hasattr(job_service, 'status_poll_interval')

    def test_no_method_conflicts_in_inheritance(self, job_service):
        """Test that there are no unintended method conflicts"""
        
        # Get all methods from the service
        service_methods = {name for name in dir(job_service) 
                          if not name.startswith('_') and callable(getattr(job_service, name))}
        
        # Import consolidated mixins to check their methods
        from app.services.job_management.mixins.layer_operations_mixin import LayerProcessingMixin
        from app.services.job_management.mixins.coordination_mixin import OPCUACoordinationMixin
        from app.services.job_management.mixins.cli_caching_mixin import CliCachingMixin

        processing_methods = {name for name in dir(LayerProcessingMixin)
                            if not name.startswith('_') and callable(getattr(LayerProcessingMixin, name))}
        coordination_methods = {name for name in dir(OPCUACoordinationMixin)
                              if not name.startswith('_') and callable(getattr(OPCUACoordinationMixin, name))}
        caching_methods = {name for name in dir(CliCachingMixin)
                          if not name.startswith('_') and callable(getattr(CliCachingMixin, name))}

        # All mixin methods should be available in the service
        expected_methods = processing_methods | coordination_methods | caching_methods

        # Remove methods that are intentionally different (error clearing)
        expected_methods.discard('clear_error_flags')  # From OPCUACoordinationMixin
        expected_methods.discard('clear_job_error_flags')  # From LayerProcessingMixin
        
        missing_methods = expected_methods - service_methods
        assert not missing_methods, f"Service is missing methods from mixins: {missing_methods}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
