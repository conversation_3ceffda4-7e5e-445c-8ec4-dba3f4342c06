# Multi‑Material End‑to‑End Workflow (Frontend → Backend Services → Infrastructure → OPC UA)

This diagram shows the complete, layer‑by‑layer print workflow, mapping every major function call in backend/app/services to infrastructure components. OPC UA variable changes are annotated inline as notes.

```mermaid
sequenceDiagram
    autonumber
    participant FE as Frontend (HMI)
    participant API as FastAPI Router (Jobs/CLI)
    participant SVC as MultiMaterialJobService
    participant L<PERSON> as LayerOperationsMixin
    participant COM as CoordinationMixin
    participant CCM as CliCachingMixin
    participant LCM as JobLifecycleMixin
    participant OPC as OPCUAService
    participant RC as RecoaterClient
    participant CLI as CLI Editor (infrastructure.cli_editor.editor)
    participant FS as Filesystem

    Note over FE,API: Operator uploads per‑drum CLI files and starts job

    rect rgba(60, 120, 200, 0.08)
      FE->>API: POST /cli/upload/{drum_id} (ASCII CLI)
      API->>SVC: CCM.cache_cli_file_for_drum(drum_id, parsed_file, filename)
      API-->>FE: 200 OK (cached)

      FE->>API: POST /jobs/start
      API->>SVC: SVC.start_layer_by_layer_job()
    end

    %% Validation and setup
    SVC->>SVC: _validate_and_setup_job()
    SVC->>SVC: get_max_layers() (from cached drums)
    SVC->>CLI: generate_single_layer_ascii_cli(...) (parser presence check)
    SVC->>SVC: current_job = MultiMaterialJobState(...)

    %% Setup job (OPC UA 7‑var arch)
    SVC->>COM: setup_job(total_layers)
    COM->>OPC: set_job_active(total_layers)
    Note over OPC: job_active=True, total_layers=N, current_layer=1

    loop For each layer = 1..N
      SVC->>COM: reset_layer_flags()
      COM->>OPC: set_recoater_layer_complete(False)
      Note over OPC: recoater_layer_complete=False

      %% Upload layer to each drum (paced, sequential)
      loop drum_id in 0..MAX_DRUMS-1
        SVC->>SVC: _get_layer_data_for_drum(drum_id, layer_idx)
        alt drum has data for layer
          SVC->>CLI: generate_single_layer_ascii_cli(layer)
          CLI-->>SVC: bytes (ASCII CLI)
        else depleted/missing layer
          SVC->>LOM: load empty template
          LOM->>FS: open(JobConfig.EMPTY_LAYER_TEMPLATE_PATH)
          FS-->>LOM: bytes
          LOM-->>SVC: bytes (empty layer)
        end
        SVC->>RC: upload_cli_data(drum_id, bytes)
        Note over RC: Low‑level HTTP/ethernet upload
        alt not last drum
          SVC->>SVC: await sleep(JobConfig.DRUM_UPLOAD_DELAY_SECONDS)
        end
      end

      %% Prepare & print
      SVC->>SVC: _prepare_and_start_print_job(layer_idx)
      SVC->>COM: update_layer_progress(current_layer)
      COM->>OPC: update_layer_progress(value)
      Note over OPC: current_layer = K (1‑based)

      COM->>OPC: set_recoater_ready_to_print(True)
      Note over OPC: recoater_ready_to_print=True

      SVC->>RC: start_print_job()
      Note over RC: Low‑level start (infrastructure client)

      %% Wait for layer completion with polling
      par Readiness/Completion monitoring
        COM->>COM: _wait_for_all_drums_ready()
        loop every JobConfig.STATUS_POLL_INTERVAL_SECONDS until READY or timeout
          COM->>OPC: read recoater_ready_to_print
        end
        Note over OPC: ready state observed
      and Completion polling
        COM->>COM: _wait_for_layer_completion()
        loop every JobConfig.STATUS_POLL_INTERVAL_SECONDS until COMPLETE or timeout
          COM->>OPC: read recoater_layer_complete
          alt assume complete window elapsed
            Note over COM: assume complete after JobConfig.LAYER_COMPLETION_ASSUME_SECONDS
          end
        end
      end

      %% Signal layer completion
      COM->>OPC: set_recoater_layer_complete(True)
      Note over OPC: recoater_layer_complete=True
    end

    %% Cleanup and finalize
    SVC->>COM: signal_layer_complete() (final layer handled in loop)
    SVC->>SVC: current_job.mark_completed()

    SVC->>COM: cleanup_job()
    COM->>OPC: set_job_inactive()
    Note over OPC: job_active=False

    %% Error handling path (whenever any await raises)
    opt On exception
      SVC->>COM: handle_job_error(error)
      COM->>OPC: set_backend_error(True)
      Note over OPC: backend_error=True
      Note over COM,SVC: State → ERROR, job inactive
    end

    %% Operator clear error action
    opt Operator clears errors
      FE->>API: POST /errors/clear
      API->>SVC: LCM.clear_error_flags()
      LCM->>OPC: clear_error_flags()
      Note over OPC: backend_error=False, plc_error=False
    end

```

Legend of OPC UA 7‑variables touched:
- job_active (bool): True when job running; set inactive at cleanup
- total_layers (int): Set at job start
- current_layer (int): Progress indicator, 1‑based
- recoater_ready_to_print (bool): SVC/COM sets True before each print
- recoater_layer_complete (bool): COM resets False at start of layer; True after completion
- backend_error (bool): Set on backend exceptions; cleared by clear_error_flags
- plc_error (bool): Reflects PLC‑side error (observed/cleared externally)



## Operator Setup: Blade Screws (Hopper) Controls

The operator can align/adjust scraping blades before printing. This sequence maps FE HopperControl → backend recoater_controls → RecoaterClient → hardware.

```mermaid
sequenceDiagram
  autonumber
  participant FE as Frontend HopperControl.vue
  participant API as FastAPI Router (recoater_controls/blade.py)
  participant RC as RecoaterClient (BladeControlMixin)

  rect rgba(60,120,200,0.08)
    FE->>API: POST /recoater/drums/{drum_id}/blade/screws/motion
    Note over FE: startCollectiveMotion() -> apiService.setBladeScrewsMotion(drumId, {mode, distance?})
    API->>RC: set_blade_screws_motion(drum_id, mode, distance?)
    RC-->>API: _make_request(POST /drums/{id}/blade/screws/motion)
    API-->>FE: 200 OK (motion started)
  end

  opt Individual screw adjustment
    FE->>API: POST /recoater/drums/{drum_id}/blade/screws/{screw_id}/motion
    Note over FE: startIndividualMotion(screwId) -> apiService.setBladeScrewMotion(...)
    API->>RC: set_blade_screw_motion(drum_id, screw_id, distance)
    RC-->>API: _make_request(POST /drums/{id}/blade/screws/{sid}/motion)
    API-->>FE: 200 OK
  end

  opt Cancel motion
    FE->>API: DELETE /recoater/drums/{drum_id}/blade/screws/motion
    API->>RC: cancel_blade_screws_motion(drum_id)
    RC-->>API: _make_request(DELETE /drums/{id}/blade/screws/motion)
    API-->>FE: 200 OK (cancelled)
  end
```

Notes:
- No OPC UA variables are changed during blade setup; these are hardware prep actions.
- UI source: frontend/src/components/HopperControl.vue (startCollectiveMotion, startIndividualMotion, cancelCollectiveMotion, cancelIndividualMotion).
- Backend: backend/app/api/recoater_controls/blade.py (set_blade_screws_motion, set_blade_screw_motion, cancel_blade_screws_motion).

## Operator Setup: Layer Parameters

The operator configures layer parameters (filling_id, speed, powder_saving, x_offset) pre-print.

```mermaid
sequenceDiagram
  autonumber
  participant FE as Frontend PrintView.vue
  participant API as FastAPI /print/layer/parameters
  participant RC as RecoaterClient (PrintControls)

  rect rgba(60,120,200,0.08)
    FE->>API: PUT /print/layer/parameters {filling_id,speed,powder_saving,x_offset?}
    Note over FE: saveParameters() -> apiService.setLayerParameters(layerParams)
    API->>RC: set_layer_parameters(filling_id,speed,powder_saving,x_offset?)
    RC-->>API: _make_request(PUT /layer/parameters)
    API-->>FE: 200 OK (saved)
  end

  opt Load current parameters
    FE->>API: GET /print/layer/parameters
    Note over FE: loadParameters() -> apiService.getLayerParameters()
    API->>RC: get_layer_parameters()
    RC-->>API: _make_request(GET /layer/parameters)
    API-->>FE: parameters JSON
  end
```

Notes:
- UI source: frontend/src/views/PrintView.vue (saveParameters, loadParameters; layerParams state).
- Backend: backend/app/api/print/layer.py (set_layer_parameters, get_layer_parameters).
- Infrastructure: backend/infrastructure/recoater_client/print_controls.py.

## Per‑Drum CLI Upload (Caching)

```mermaid
sequenceDiagram
  autonumber
  participant FE as Frontend PrintView.vue/FileUploadColumn.vue
  participant API as FastAPI /print/cli/upload/{drum_id}
  participant SVC as MultiMaterialJobService (CliCachingMixin)
  participant CLI as infrastructure.cli_editor.editor.Editor

  FE->>API: POST /print/cli/upload/{drum_id} (multipart .cli)
  Note over FE: handleDrumUpload -> apiService.uploadCliFileToDrum(file, drumId)
  API->>CLI: parse(file_bytes) (async via to_thread)
  API->>SVC: cache_cli_file_for_drum(drum_id, parsed_data, filename)
  API-->>FE: {file_id: "drum_{id}", total_layers, filename}
```

Notes:
- Backend: backend/app/api/print/cli.py (upload_cli_file_to_drum).
- Service call: MultiMaterialJobService.cache_cli_file_for_drum (CliCachingMixin).
- Editor: infrastructure/cli_editor/editor.py (parse, generate_single_layer_ascii_cli).

## Start Job (Frontend → Backend kickoff)

```mermaid
sequenceDiagram
  autonumber
  participant FE as Frontend MultiLayerJobControl.vue
  participant API as FastAPI /print/cli/start-multimaterial-job
  participant SVC as MultiMaterialJobService

  FE->>API: POST /print/cli/start-multimaterial-job
  Note over FE: startJob() -> apiService.startMultiMaterialJob()
  API->>SVC: asyncio.create_task(start_layer_by_layer_job())
  API-->>FE: {success: true, job_id: "layer-by-layer-job"}
  Note over SVC: Execution continues as in main sequence above
```
