<template>
  <div 
    v-if="printJobStore.hasCriticalError" 
    class="critical-error-modal-overlay"
    @click.self="handleOverlayClick"
  >
    <div class="critical-error-modal" role="dialog" aria-modal="true" aria-labelledby="error-modal-title">
      <div class="modal-header">
        <div class="error-icon" aria-hidden="true"></div>
        <h2 id="error-modal-title" class="modal-title">Critical System Error</h2>
        <button 
          @click="closeModal"
          class="close-button"
          aria-label="Close error modal"
          data-testid="close-error-modal-btn"
        >
          ✕
        </button>
      </div>

      <div class="modal-content">
        <div class="error-details">
          <div class="error-type-section">
            <h3 class="section-title">Error Type</h3>
            <div class="error-flags">
              <div 
                v-if="printJobStore.errorFlags.backendError"
                class="error-flag backend-error"
              >
                <div class="flag-icon" aria-hidden="true"></div>
                <div class="flag-content">
                  <div class="flag-title">Backend Error</div>
                  <div class="flag-description">
                    The recoater backend system has encountered an error and requires attention.
                  </div>
                </div>
              </div>

              <div 
                v-if="printJobStore.errorFlags.plcError"
                class="error-flag plc-error"
              >
                <div class="flag-icon" aria-hidden="true"></div>
                <div class="flag-content">
                  <div class="flag-title">PLC Error</div>
                  <div class="flag-description">
                    The PLC system has reported an error condition that requires operator intervention.
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="printJobStore.errorFlags.errorMessage" class="error-message-section">
            <h3 class="section-title">Error Details</h3>
            <div class="error-message-box">
              {{ printJobStore.errorFlags.errorMessage }}
            </div>
          </div>

          <div class="impact-section">
            <h3 class="section-title">Impact</h3>
            <div class="impact-list">
              <div class="impact-item">
                <span class="impact-text">All print operations have been paused</span>
              </div>
              <div class="impact-item">
                <span class="impact-text">New jobs cannot be started until errors are resolved</span>
              </div>
              <div class="impact-item">
                <span class="impact-text">Operator intervention is required</span>
              </div>
            </div>
          </div>

          <div class="action-section">
            <h3 class="section-title">Required Actions</h3>
            <div class="action-steps">
              <div class="action-step">
                <div class="step-number">1</div>
                <div class="step-content">
                  <div class="step-title">Investigate the Error</div>
                  <div class="step-description">
                    Check the system logs and hardware status to identify the root cause of the error.
                  </div>
                </div>
              </div>

              <div class="action-step">
                <div class="step-number">2</div>
                <div class="step-content">
                  <div class="step-title">Resolve the Issue</div>
                  <div class="step-description">
                    Take appropriate corrective action based on the error type and system diagnostics.
                  </div>
                </div>
              </div>

              <div class="action-step">
                <div class="step-number">3</div>
                <div class="step-content">
                  <div class="step-title">Clear Error Flags</div>
                  <div class="step-description">
                    Once the issue is resolved, use the "Clear Error Flags" button to reset the system.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <div class="footer-info">
          <span class="timestamp">
            Error occurred at: {{ formatErrorTime() }}
          </span>
        </div>
        <div class="footer-actions">
          <button
            @click="clearErrors"
            :disabled="printJobStore.isClearingErrors"
            class="btn btn-primary clear-errors-btn"
            data-testid="clear-error-flags-btn"
          >
            <span v-if="printJobStore.isClearingErrors">Clearing...</span>
            <span v-else>Clear Error Flags</span>
          </button>
          <button
            @click="closeModal"
            class="btn btn-secondary"
            data-testid="acknowledge-error-btn"
          >
            Acknowledge
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { usePrintJobStore } from '../stores/printJobStore'

export default {
  name: 'CriticalErrorModal',
  setup() {
    const printJobStore = usePrintJobStore()

    // Methods
    const closeModal = () => {
      printJobStore.closeCriticalModal()
    }

    const handleOverlayClick = (event) => {
      // Prevent closing modal by clicking overlay for critical errors
      // User must explicitly acknowledge or clear errors
      event.preventDefault()
    }

    const clearErrors = async () => {
      try {
        await printJobStore.clearErrorFlagsAPI()
        // Modal will close automatically when error flags are cleared
      } catch (error) {
        console.error('Failed to clear error flags:', error)
        // Error handling is done in the store
      }
    }

    const formatErrorTime = () => {
      // For now, return current time. In a real implementation,
      // this would be the actual error timestamp from the backend
      return new Date().toLocaleString()
    }

    return {
      printJobStore,
      closeModal,
      handleOverlayClick,
      clearErrors,
      formatErrorTime
    }
  }
}
</script>

<style scoped>
.critical-error-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 1rem;
}

.critical-error-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.error-icon {
  font-size: 2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.modal-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  flex: 1;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
  line-height: 1;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.modal-content {
  padding: 2rem;
}

.section-title {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.error-details > div:not(:last-child) {
  margin-bottom: 2rem;
}

.error-flags {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.error-flag {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid;
}

.backend-error {
  background-color: #fff3cd;
  border-left-color: #ffc107;
}

.plc-error {
  background-color: #f8d7da;
  border-left-color: #dc3545;
}

.flag-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.flag-content {
  flex: 1;
}

.flag-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.flag-description {
  color: #6c757d;
  font-size: 0.875rem;
  line-height: 1.4;
}

.error-message-box {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 1rem;
  font-family: monospace;
  font-size: 0.875rem;
  color: #495057;
  white-space: pre-wrap;
  word-break: break-word;
}

.impact-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.impact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.impact-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.impact-text {
  color: #495057;
  font-size: 0.875rem;
}

.action-steps {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.action-step {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.step-number {
  background: #007bff;
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.step-description {
  color: #6c757d;
  font-size: 0.875rem;
  line-height: 1.4;
}

.modal-footer {
  background: #f8f9fa;
  padding: 1.5rem;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-info {
  flex: 1;
}

.timestamp {
  font-size: 0.875rem;
  color: #6c757d;
}

.footer-actions {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #545b62;
}

.clear-errors-btn {
  min-width: 140px;
}

@media (max-width: 768px) {
  .critical-error-modal-overlay {
    padding: 0.5rem;
  }
  
  .modal-header {
    padding: 1rem;
  }
  
  .error-icon {
    font-size: 1.5rem;
  }
  
  .modal-title {
    font-size: 1.25rem;
  }
  
  .modal-content {
    padding: 1.5rem;
  }
  
  .modal-footer {
    padding: 1rem;
    flex-direction: column;
    align-items: stretch;
  }
  
  .footer-actions {
    justify-content: stretch;
  }
  
  .btn {
    flex: 1;
  }
  
  .error-flag {
    flex-direction: column;
    text-align: center;
  }
  
  .action-step {
    flex-direction: column;
    text-align: center;
  }
}

/* Ensure modal stays on top of everything */
.critical-error-modal-overlay {
  z-index: 10000;
}

/* Prevent body scroll when modal is open */
body:has(.critical-error-modal-overlay) {
  overflow: hidden;
}
</style>
