<template>
  <div class="print-view">
    <h2 class="view-title heading-xl">Print Control</h2>
    <!-- Parsing Banner -->
    <div v-if="parsingInProgress" class="parsing-banner" role="status" aria-live="polite">
      <span class="spinner" aria-hidden="true"></span>
      <span>Parsing CLI file… Please wait</span>
    </div>


    <!-- Connection Status -->
    <div class="status-card">
      <div class="status-header">
        <h3 class="status-title heading-lg">Connection Status</h3>
        <div class="status-indicator">
          <div
            :class="[
              'status-dot',
              statusStore.isConnected ? 'status-connected' : 'status-disconnected'
            ]"
          ></div>
          <span class="status-text">
            {{ statusStore.isConnected ? 'Connected' : 'Disconnected' }}
          </span>
        </div>
      </div>
    </div>

    <!-- Critical Error Modal -->
    <CriticalErrorModal />

    <!-- Layer Parameters -->
    <div class="control-card">
      <div class="card-header">
        <h3 class="card-title heading-lg">Layer Parameters</h3>
      </div>
      <div class="card-content">
        <div v-if="!statusStore.isConnected" class="disabled-overlay">
          <p class="disabled-text">Connect to recoater to configure layer parameters</p>
        </div>
        <div v-else-if="isAnyJobActive" class="disabled-overlay">
          <p class="disabled-text">Print job is active - parameters cannot be modified</p>
        </div>

        <div class="parameter-grid">
          <!-- Filling Drum ID -->
          <div class="parameter-group">
            <label for="filling-id" class="parameter-label">Filling Drum ID</label>
            <input
              id="filling-id"
              v-model.number="layerParams.filling_id"
              type="number"
              class="parameter-input"
              :disabled="!statusStore.isConnected || isLoading"
              min="-1"
              step="1"
            />
            <p class="parameter-help">Set to -1 for no filling material</p>
          </div>

          <!-- Patterning Speed -->
          <div class="parameter-group">
            <label for="speed" class="parameter-label">Patterning Speed (mm/s)</label>
            <input
              id="speed"
              v-model.number="layerParams.speed"
              type="number"
              class="parameter-input"
              :disabled="!statusStore.isConnected || isLoading"
              min="0.1"
              step="0.1"
            />
          </div>

          <!-- X Offset -->
          <div class="parameter-group">
            <label for="x-offset" class="parameter-label">X Offset (mm)</label>
            <input
              id="x-offset"
              v-model.number="layerParams.x_offset"
              type="number"
              class="parameter-input"
              :disabled="!statusStore.isConnected || isLoading"
              min="0"
              step="0.1"
            />
          </div>

          <!-- Powder Saving -->
          <div class="parameter-group">
            <label class="parameter-label">
              <input
                v-model="layerParams.powder_saving"
                type="checkbox"
                class="parameter-checkbox"
                :disabled="!statusStore.isConnected || isLoading"
              />
              Enable Powder Saving
            </label>
            <p class="parameter-help">Use powder saving strategies</p>
          </div>
        </div>

        <div class="button-group">
          <button
            @click="saveParameters"
            :disabled="!statusStore.isConnected || isLoading || !isParametersValid"
            class="btn btn-primary"
          >
            <span v-if="isLoading">Saving...</span>
            <span v-else>Save Parameters</span>
          </button>
          <button
            @click="loadParameters"
            :disabled="!statusStore.isConnected || isLoading"
            class="btn btn-secondary"
          >
            <span v-if="isLoading">Loading...</span>
            <span v-else>Load Current</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Layer Preview -->
    <div class="control-card">
      <div class="card-header">
        <h3 class="card-title heading-lg">Layer Preview</h3>
        <div class="card-subtitle">
          Preview shows the current layer configuration based on the parameters above
        </div>
      </div>
      <div class="card-content">
        <div v-if="!statusStore.isConnected" class="disabled-overlay">
          <p class="disabled-text">Connect to recoater to view layer preview</p>
        </div>
        <div v-else-if="isAnyJobActive" class="disabled-overlay">
          <p class="disabled-text">Print job is active - preview not available</p>
        </div>

        <div class="preview-container">
          <div v-if="previewLoading" class="preview-loading">
            <div class="loading-spinner"></div>
            <p>Loading preview...</p>
          </div>

          <div v-else-if="previewImageUrl" class="preview-image-container">
            <img
              :src="previewImageUrl"
              alt="Layer Preview"
              class="preview-image"
              @error="handlePreviewError"
            />
          </div>

          <div v-else class="preview-placeholder">
            <svg class="preview-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <p>No preview available</p>
          </div>
        </div>

        <Legend />

        <div class="preview-controls">
          <div class="form-group">
            <label for="preview-drum-select" class="form-label">Preview Source:</label>
            <select
              id="preview-drum-select"
              v-model="previewSource"
              class="form-select"
              :disabled="!statusStore.isConnected || previewLoading"
            >
              <option value="layer">Current Layer Configuration</option>
              <option v-for="drumId in availableDrums" :key="`drum-${drumId}`" :value="`drum-${drumId}`">
                Drum {{ drumId }} Geometry
              </option>
            </select>
          </div>

          <div class="button-group">
            <button
              @click="loadPreview"
              :disabled="!statusStore.isConnected || previewLoading"
              class="btn btn-primary"
            >
              <span v-if="previewLoading">Loading...</span>
              <span v-else>Load Preview</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- File Management -->
    <div class="control-card">
      <div class="card-header">
        <h3 class="card-title heading-lg">File Management</h3>
        <div class="card-subtitle">
          Upload CLI files to the backend cache for each drum (new workflow). The Delete button clears the cached file for that specific drum.
          <!-- Note: /print/drums/{drum_id}/geometry download fetches from hardware, not cache.
               Prefer reusing your original CLI file; upload/delete here refer to the backend cache. -->
        </div>
      </div>
      <div class="card-content">
        <div v-if="!statusStore.isConnected" class="disabled-overlay">
          <p class="disabled-text">Connect to recoater to manage files</p>
        </div>
        <div v-else-if="isAnyJobActive" class="disabled-overlay">
          <p class="disabled-text">Print job is active - file management not available</p>
        </div>

        <div class="grid-3-equal">
          <FileUploadColumn
            v-for="drumId in availableDrums"
            :key="`drum-${drumId}`"
            :drum-id="drumId"
            :is-connected="statusStore.isConnected"
            :is-loading="isFileOperationLoading"
            @upload="handleDrumUpload"
            @download="handleDrumDownload"
            @delete="handleDrumDelete"
          />
        </div>

        <!-- Legacy file input for test compatibility -->
        <div style="display: none;">
          <input
            id="file-input"
            type="file"
            @change="handleLegacyFileSelect"
            accept=".cli,.png,image/png,application/octet-stream"
          />
        </div>
      </div>
    </div>

    <!-- CLI File Management -->
    <div class="control-card">
      <div class="card-header">
        <h3 class="card-title heading-lg">CLI Layer Preview</h3>
        <div class="card-subtitle">
          Upload multi-layer CLI files and preview individual layers as PNG images.
        </div>
      </div>
      <div class="card-content">
        <div v-if="!statusStore.isConnected" class="disabled-overlay">
          <p class="disabled-text">Connect to recoater to manage CLI files</p>
        </div>
        <div v-else-if="isAnyJobActive" class="disabled-overlay">
          <p class="disabled-text">Print job is active - CLI file operations not available</p>
        </div>

        <div class="cli-management-grid">
          <!-- CLI Upload Section -->
          <div class="cli-upload-section">
            <h4 class="section-title">Upload CLI File</h4>
            <div class="form-group">
              <label for="cli-file-input" class="form-label">Multi-layer CLI File:</label>
              <input
                id="cli-file-input"
                type="file"
                ref="cliFileInput"
                @change="handleCliFileSelect"
                accept=".cli,application/octet-stream"
                class="form-file-input"
                :disabled="!statusStore.isConnected || isCliOperationLoading"
              />
              <div v-if="selectedCliFile" class="file-info">
                Selected: {{ selectedCliFile.name }} ({{ formatFileSize(selectedCliFile.size) }})
              </div>
              <div v-else-if="cliFileInfo" class="file-info success-info">
                ✓ CLI file loaded ({{ cliFileInfo.total_layers }} layers) - Select a new file to upload again
              </div>
            </div>

            <div class="button-group">
              <button
                @click="uploadCliFile"
                :disabled="!statusStore.isConnected || !selectedCliFile || isCliOperationLoading"
                class="btn btn-primary"
              >
                <span v-if="isCliOperationLoading">Uploading...</span>
                <span v-else-if="cliFileInfo && !selectedCliFile">Upload New CLI File</span>
                <span v-else>Upload & Parse CLI</span>
              </button>
              <button
                @click="clearCliFileSelection"
                :disabled="(!selectedCliFile && !cliFileInfo) || isCliOperationLoading"
                class="btn btn-tertiary"
              >
                <span v-if="cliFileInfo && !selectedCliFile">Clear Parsed Data</span>
                <span v-else>Clear</span>
              </button>
            </div>
          </div>

          <!-- Layer Preview Section -->
          <div class="layer-preview-section" v-if="cliFileInfo">
            <h4 class="section-title">Layer Preview</h4>
            <div class="cli-info">
              <p><strong>File ID:</strong> {{ cliFileInfo.file_id }}</p>
              <p><strong>Total Layers:</strong> {{ cliFileInfo.total_layers }}</p>
            </div>

            <div class="form-group">
              <label for="layer-select" class="form-label">Layer Number:</label>
              <input
                id="layer-select"
                type="number"
                v-model.number="selectedLayerNum"
                :min="1"
                :max="cliFileInfo.total_layers"
                class="form-input"
                :disabled="!statusStore.isConnected || isCliPreviewLoading"
              />
              <div class="layer-range-info">
                Range: 1 to {{ cliFileInfo.total_layers }}
              </div>
            </div>

            <div class="form-group">
              <label for="target-drum-select" class="form-label">Target Drum:</label>
              <select
                id="target-drum-select"
                v-model="selectedTargetDrumId"
                class="form-select"
                :disabled="!statusStore.isConnected || isCliSendLoading"
              >
                <option value="">Select drum...</option>
                <option value="0">Drum 0</option>
                <option value="1">Drum 1</option>
                <option value="2">Drum 2</option>
              </select>
            </div>

            <div class="button-group">
              <button
                data-testid="send-layer-button"
                @click="sendCliLayerToDrum"
                :disabled="!statusStore.isConnected || !selectedLayerNum || !selectedTargetDrumId || isCliSendLoading || isAnyJobActive"
                class="btn btn-primary"
              >
                <span v-if="isCliSendLoading">Sending...</span>
                <span v-else>Send Layer to Recoater</span>
              </button>

              <button
                @click="previewCliLayer"
                :disabled="!statusStore.isConnected || !selectedLayerNum || isCliPreviewLoading"
                class="btn btn-secondary"
              >
                <span v-if="isCliPreviewLoading">Loading...</span>
                <span v-else>Preview Layer</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- CLI Layer Selection -->
    <div class="control-card" v-if="cliFileInfo">
      <div class="card-header">
        <h3 class="card-title heading-lg">CLI Layer Selection</h3>
        <div class="card-subtitle">
          Select and send a range of layers from uploaded CLI files to recoater drums for batch processing.
        </div>
      </div>
      <div class="card-content">
        <div v-if="!statusStore.isConnected" class="disabled-overlay">
          <p class="disabled-text">Connect to recoater to select layer ranges</p>
        </div>
        <div v-else-if="isAnyJobActive" class="disabled-overlay">
          <p class="disabled-text">Print job is active - layer range selection not available</p>
        </div>
        <div class="form-group">
          <label for="start-layer-input" class="form-label">Start Layer Number:</label>
          <input
            id="start-layer-input"
            type="number"
            v-model.number="startLayerNum"
            :min="1"
            :max="cliFileInfo.total_layers"
            class="form-input"
            :disabled="!statusStore.isConnected || isRangeOperationLoading"
          />
          <div class="layer-range-info">
            Range: 1 to {{ cliFileInfo.total_layers }}
          </div>
        </div>
        <div class="form-group">
          <label for="end-layer-input" class="form-label">End Layer Number:</label>
          <input
            id="end-layer-input"
            type="number"
            v-model.number="endLayerNum"
            :min="1"
            :max="cliFileInfo.total_layers"
            class="form-input"
            :disabled="!statusStore.isConnected || isRangeOperationLoading"
          />
        </div>
        <div class="form-group">
          <label for="range-target-drum-select" class="form-label">Target Drum:</label>
          <select
            id="range-target-drum-select"
            v-model="rangeTargetDrumId"
            class="form-select"
            :disabled="!statusStore.isConnected || isRangeOperationLoading"
          >
            <option value="">Select drum...</option>
            <option value="0">Drum 0</option>
            <option value="1">Drum 1</option>
            <option value="2">Drum 2</option>
          </select>
        </div>
        <div class="button-group">
          <button
            @click="sendCliLayerRangeToDrum"
            :disabled="!statusStore.isConnected || !startLayerNum || !endLayerNum || startLayerNum > endLayerNum || !rangeTargetDrumId || isRangeOperationLoading || isAnyJobActive"
            class="btn btn-primary"
          >
            <span v-if="isRangeOperationLoading">Sending...</span>
            <span v-else>Send Layer Range</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Enhanced Job Management -->
    <div class="control-card">
      <div class="card-header">
        <h3 class="card-title heading-lg">Print Job Management</h3>
      </div>
      <div class="card-content">
        <div v-if="!statusStore.isConnected" class="disabled-overlay">
          <p class="disabled-text">Connect to recoater to manage print jobs</p>
        </div>

        <div class="job-status-section">
          <div class="status-display">
            <div class="status-item">
              <span class="status-label">Current State:</span>
              <span :class="['status-value', getJobStatusClass()]">
                {{ getJobStatusText() }}
              </span>
            </div>
            <div class="status-item">
              <span class="status-label">Print Active:</span>
              <span :class="['status-value', isPrinting ? 'status-active' : 'status-inactive']">
                {{ isPrinting ? 'Yes' : 'No' }}
              </span>
            </div>

          </div>

          <div class="job-controls">
            <button
              @click="startPrintJob"
              :disabled="!statusStore.isConnected || isAnyJobActive || isJobOperationLoading"
              class="btn btn-primary"
            >
              <span v-if="isJobOperationLoading">Starting...</span>
              <span v-else>Start Print Job</span>
            </button>
            <button
              @click="cancelPrintJob"
              :disabled="!statusStore.isConnected || !isPrinting || isJobOperationLoading"
              class="btn btn-danger"
            >
              <span v-if="isJobOperationLoading">Cancelling...</span>
              <span v-else>Cancel Print Job</span>
            </button>
            <button
              @click="refreshJobStatus"
              :disabled="!statusStore.isConnected || isJobOperationLoading"
              class="btn btn-tertiary"
            >
              <span v-if="isJobOperationLoading">Refreshing...</span>
              <span v-else>Refresh Status</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Job Progress Display -->
    <JobProgressDisplay />

    <!-- Success/Error Messages -->
    <div v-if="successMessage" class="message message-success">
      {{ successMessage }}
    </div>
    <div v-if="errorMessage" class="message message-error">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useStatusStore } from '../stores/status'
import { usePrintJobStore } from '../stores/printJobStore'
import apiService from '../services/api'
import Legend from '@/components/Legend.vue'
import FileUploadColumn from '@/components/FileUploadColumn.vue'
import JobProgressDisplay from '@/components/JobProgressDisplay.vue'
import CriticalErrorModal from '@/components/CriticalErrorModal.vue'

export default {
  name: 'PrintView',
  components: {
    Legend,
    FileUploadColumn,
    JobProgressDisplay,
    CriticalErrorModal
  },
  setup() {
    const statusStore = useStatusStore()
    const printJobStore = usePrintJobStore()

    // Reactive state
    const isLoading = ref(false)
    const previewLoading = ref(false)
    const previewImageUrl = ref(null)
    const previewSource = ref('layer')
    const successMessage = ref('')
    const errorMessage = ref('')

    // Layer parameters
    const layerParams = ref({
      filling_id: 1,
      speed: 30.0,
      powder_saving: true,
      x_offset: 0.0
    })

    // File management state (now handled by FileUploadColumn components)
    const isFileOperationLoading = ref(false)
    const parsingInProgress = ref(false)


    // CLI file management state
    const selectedCliFile = ref(null)
    const cliFileInfo = ref(null) // Stores file_id, total_layers from upload response
    const selectedLayerNum = ref(1)
    const selectedTargetDrumId = ref('')
    const isCliOperationLoading = ref(false)
    const isCliPreviewLoading = ref(false)
    const isCliSendLoading = ref(false)
    const cliFileInput = ref(null)

    // CLI layer range selection state
    const startLayerNum = ref(1)
    const endLayerNum = ref(1)
    const rangeTargetDrumId = ref('')
    const isRangeOperationLoading = ref(false)

    // Job management state
    const isJobOperationLoading = ref(false)

    // Computed properties
    const isParametersValid = computed(() => {
      return layerParams.value.filling_id !== null &&
             layerParams.value.speed > 0
    })

    // Available drums (0, 1, 2 based on hardware limitation)
    const availableDrums = computed(() => [0, 1, 2])

    // Print job status computed properties
    const jobStatus = computed(() => statusStore.printData?.job_status || {})

    // Treat the whole multi-layer run as Printing only while a multi-material job is active
    const isPrinting = computed(() => Boolean(printJobStore.hasActiveJob))
    const hasJobError = computed(() => jobStatus.value.has_error || false)

    // File/preview controls should only be blocked while the multi-material job is active
    const isAnyJobActive = computed(() => Boolean(printJobStore.hasActiveJob))

    const getJobStatusClass = () => {
      if (hasJobError.value) return 'status-error'
      if (printJobStore.hasActiveJob) return 'status-active'
      return 'status-ready'
    }

    const getJobStatusText = () => {
      if (hasJobError.value) return 'Error'
      if (printJobStore.hasActiveJob) return 'Printing'
      return 'Ready'
    }

    // Methods
    const showMessage = (message, isError = false) => {
      if (isError) {
        errorMessage.value = message
        successMessage.value = ''
      } else {
        successMessage.value = message
        errorMessage.value = ''
      }

      // Auto-clear messages after 5 seconds
      setTimeout(() => {
        successMessage.value = ''
        errorMessage.value = ''
      }, 5000)
    }

    const loadParameters = async () => {
      if (!statusStore.isConnected) return

      isLoading.value = true
      try {
        const response = await apiService.getLayerParameters()
        layerParams.value = { ...response.data }
        showMessage('Layer parameters loaded successfully')
      } catch (error) {
        console.error('Failed to load layer parameters:', error)
        showMessage('Failed to load layer parameters: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isLoading.value = false
      }
    }

    const saveParameters = async () => {
      if (!statusStore.isConnected || !isParametersValid.value) return

      isLoading.value = true
      try {
        await apiService.setLayerParameters(layerParams.value)
        showMessage('Layer parameters saved successfully')
      } catch (error) {
        console.error('Failed to save layer parameters:', error)
        showMessage('Failed to save layer parameters: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isLoading.value = false
      }
    }

    const loadPreview = async () => {
      if (!statusStore.isConnected) return

      previewLoading.value = true
      try {
        let response
        let successMessage

        if (previewSource.value === 'layer') {
          // Load current layer configuration preview
          response = await apiService.getLayerPreview()
          successMessage = 'Layer configuration preview loaded successfully'
        } else if (previewSource.value.startsWith('drum-')) {
          // Load drum geometry preview
          const drumId = parseInt(previewSource.value.split('-')[1])
          response = await apiService.getDrumGeometryPreview(drumId)
          successMessage = `Drum ${drumId} geometry preview loaded successfully`
        } else {
          throw new Error('Invalid preview source selected')
        }

        // Create object URL from blob
        const blob = new Blob([response.data], { type: 'image/png' })
        if (previewImageUrl.value) {
          URL.revokeObjectURL(previewImageUrl.value)
        }
        previewImageUrl.value = URL.createObjectURL(blob)

        showMessage(successMessage)
      } catch (error) {
        console.error('Failed to load preview:', error)
        showMessage('Failed to load preview: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        previewLoading.value = false
      }
    }

    const handlePreviewError = () => {
      showMessage('Failed to display preview image', true)
      if (previewImageUrl.value) {
        URL.revokeObjectURL(previewImageUrl.value)
        previewImageUrl.value = null
      }
    }

    // File Management Methods - New drum-specific handlers
    const handleDrumUpload = async ({ drumId, file }) => {
      isFileOperationLoading.value = true
      parsingInProgress.value = true
      try {
        // Upload and cache CLI file for the specific drum
        const response = await apiService.uploadCliFileToDrum(file, drumId)
        const data = response.data
        // Track the uploaded cached file
        printJobStore.setFileUploaded(drumId, {
          fileId: data.file_id,
          fileName: data.filename,
          layerCount: data.total_layers,
          source: 'direct'
        })
        printJobStore.setLastUploadedFile(drumId, data.filename, 'cli')
        showMessage(`CLI file cached for drum ${drumId} (${data.total_layers} layers)`)
      } catch (error) {
        console.error(`Failed to upload CLI file to drum ${drumId}:`, error)
        showMessage('Failed to upload CLI file: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        parsingInProgress.value = false
        isFileOperationLoading.value = false
      }
    }

    const handleDrumDownload = async ({ drumId }) => {
      isFileOperationLoading.value = true
      try {
        const response = await apiService.downloadDrumGeometry(drumId)

        // Create download link
        const blob = new Blob([response.data], { type: 'image/png' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `drum_${drumId}_geometry.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        showMessage(`Geometry file downloaded from drum ${drumId}`)
      } catch (error) {
        console.error('Failed to download file:', error)
        showMessage('Failed to download file: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isFileOperationLoading.value = false
      }
    }

    const handleDrumDelete = async ({ drumId }) => {
      // Clears the backend cache for the specified drum only (not the hardware file)
      isFileOperationLoading.value = true
      try {
        await apiService.clearDrumCache(drumId)
        // Keep UI/store in sync with backend cache clearing for this drum
        printJobStore.clearUploadedFile(drumId)
        printJobStore.clearLastUploadedFile(drumId)
        showMessage(`Cleared cached CLI file for drum ${drumId}`)
      } catch (error) {
        console.error('Failed to clear cached CLI file for drum:', error)
        showMessage('Failed to clear cached CLI file: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isFileOperationLoading.value = false
      }
    }

    // CLI File Management Methods
    const handleCliFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        selectedCliFile.value = file
      }
    }

    const clearCliFileSelection = () => {
      selectedCliFile.value = null
      cliFileInfo.value = null
      selectedLayerNum.value = 1
      if (cliFileInput.value) {
        cliFileInput.value.value = ''
      }
    }

    const uploadCliFile = async () => {
      if (!selectedCliFile.value) {
        showMessage('Please select a CLI file', true)
        return
      }

      isCliOperationLoading.value = true
      parsingInProgress.value = true
      try {
        const response = await apiService.uploadCliFile(selectedCliFile.value)
        cliFileInfo.value = response.data
        selectedLayerNum.value = 1

        // Set default end layer to total number of layers
        endLayerNum.value = response.data.total_layers

        // Keep the selected file available so it can be uploaded to a target drum later
        showMessage(`CLI file uploaded successfully. Found ${response.data.total_layers} layers.`)
      } catch (error) {
        console.error('Failed to upload CLI file:', error)
        showMessage('Failed to upload CLI file: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        parsingInProgress.value = false
        isCliOperationLoading.value = false
      }
    }

    const previewCliLayer = async () => {
      if (!cliFileInfo.value || !selectedLayerNum.value) {
        showMessage('Please select a layer number', true)
        return
      }

      isCliPreviewLoading.value = true
      try {
        const response = await apiService.getCliLayerPreview(cliFileInfo.value.file_id, selectedLayerNum.value)

        // Create blob URL for the image
        const blob = new Blob([response.data], { type: 'image/png' })
        const imageUrl = URL.createObjectURL(blob)

        // Clean up previous preview URL
        if (previewImageUrl.value) {
          URL.revokeObjectURL(previewImageUrl.value)
        }

        previewImageUrl.value = imageUrl
        previewSource.value = 'cli'
        showMessage(`Layer ${selectedLayerNum.value} preview loaded successfully`)
      } catch (error) {
        console.error('Failed to preview CLI layer:', error)
        showMessage('Failed to preview CLI layer: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isCliPreviewLoading.value = false
      }
    }

    const sendCliLayerToDrum = async () => {
      if (!cliFileInfo.value || !selectedLayerNum.value) {
        showMessage('Please select a layer number', true)
        return
      }

      if (!selectedTargetDrumId.value) {
        showMessage('Please select a target drum', true)
        return
      }

      isCliSendLoading.value = true
      parsingInProgress.value = true
      try {
        // New workflow: cache the full CLI file for the selected drum
        const uploadResp = await apiService.uploadCliFileToDrum(
          selectedCliFile.value,
          parseInt(selectedTargetDrumId.value)
        )
        const uploaded = uploadResp.data

        // Track the uploaded file with layer prefix using the original filename for operator clarity
        const originalFilename = (cliFileInfo.value && cliFileInfo.value.filename) || selectedCliFile.value?.name || 'file.cli'
        const layerFileName = `layer_${selectedLayerNum.value}_${originalFilename}`

        // Update job progress store so Job Progress card reflects the upload
        // For single-layer send, layerCount should be 1 and filename should include the layer prefix
        printJobStore.setFileUploaded(parseInt(selectedTargetDrumId.value), {
          fileId: uploaded.file_id,
          fileName: layerFileName,
          layerCount: 1
        })
        printJobStore.setLastUploadedFile(parseInt(selectedTargetDrumId.value), layerFileName, 'cli_layer')

        // Confirmation toast for operator
        showMessage(`Drum ${selectedTargetDrumId.value}: uploaded 1 layer from ${originalFilename}`)
      } catch (error) {
        console.error('Failed to cache CLI for drum:', error)
        showMessage('Failed to cache CLI for drum: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        parsingInProgress.value = false
        isCliSendLoading.value = false
      }
    }

    // CLI Layer Range Selection Method
    const sendCliLayerRangeToDrum = async () => {
      if (!cliFileInfo.value) {
        showMessage('Please upload a CLI file first', true)
        return
      }
      if (!startLayerNum.value || !endLayerNum.value) {
        showMessage('Please select a valid layer range', true)
        return
      }
      if (startLayerNum.value > endLayerNum.value) {
        showMessage('Start layer must be less than or equal to end layer', true)
        return
      }
      if (!rangeTargetDrumId.value) {
        showMessage('Please select a target drum', true)
        return
      }
      isRangeOperationLoading.value = true
      parsingInProgress.value = true
      try {
        // New workflow: cache the full CLI file for the selected drum
        const uploadResp = await apiService.uploadCliFileToDrum(
          selectedCliFile.value,
          parseInt(rangeTargetDrumId.value)
        )
        const uploaded = uploadResp.data

        // Track the uploaded file with layer range prefix using the original filename for operator clarity
        const originalFilename = (cliFileInfo.value && cliFileInfo.value.filename) || selectedCliFile.value?.name || 'file.cli'
        const rangeFileName = `layers_${startLayerNum.value}-${endLayerNum.value}_${originalFilename}`
        const rangeCount = (endLayerNum.value - startLayerNum.value + 1)

        // Update job progress store so Job Progress card reflects the upload
        // For range send, layerCount should equal the selected range count and filename should include the prefix
        printJobStore.setFileUploaded(parseInt(rangeTargetDrumId.value), {
          fileId: uploaded.file_id,
          fileName: rangeFileName,
          layerCount: rangeCount
        })
        printJobStore.setLastUploadedFile(parseInt(rangeTargetDrumId.value), rangeFileName, 'cli_layer')

        // Confirmation toast for operator
        showMessage(`Drum ${rangeTargetDrumId.value}: uploaded ${rangeCount} layers from ${originalFilename}`)
      } catch (error) {
        console.error('Failed to cache CLI for drum (range):', error)
        showMessage('Failed to cache CLI for drum (range): ' + (error.response?.data?.detail || error.message), true)
      } finally {
        parsingInProgress.value = false
        isRangeOperationLoading.value = false
      }
    }

    // Job Management Methods
    const startPrintJob = async () => {
      isJobOperationLoading.value = true
      try {
        // Start via store to set jobId/active state so UI remains in Printing throughout
        await printJobStore.startMultiMaterialJob()
        showMessage('Multi-material print job started successfully')
      } catch (error) {
        console.error('Failed to start multi-material print job:', error)
        showMessage('Failed to start multi-material print job: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isJobOperationLoading.value = false
      }
    }

    const cancelPrintJob = async () => {
      if (!confirm('Are you sure you want to cancel the current print job?')) {
        return
      }

      isJobOperationLoading.value = true
      try {
        await printJobStore.cancelMultiMaterialJob()
        showMessage('Print job cancelled successfully')
      } catch (error) {
        console.error('Failed to cancel print job:', error)
        showMessage('Failed to cancel print job: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isJobOperationLoading.value = false
      }
    }

    const refreshJobStatus = async () => {
      isJobOperationLoading.value = true
      try {
        await printJobStore.fetchJobStatus()
        showMessage('Multi-material job status refreshed')
      } catch (error) {
        console.error('Failed to refresh multi-material job status:', error)
        showMessage('Failed to refresh job status: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isJobOperationLoading.value = false
      }
    }

    // Add missing functions for test compatibility
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // Legacy function names for test compatibility
    const uploadFile = async () => {
      if (!selectedFile.value || !selectedDrumId.value) return
      await handleDrumUpload({ drumId: selectedDrumId.value, file: selectedFile.value })
      selectedFile.value = null // Clear after upload
    }

    const downloadFile = async () => {
      if (!actionDrumId.value) return
      await handleDrumDownload({ drumId: actionDrumId.value })
    }

    const deleteFile = async () => {
      if (!actionDrumId.value) return
      if (!confirm('Are you sure you want to delete this file?')) return
      await handleDrumDelete({ drumId: actionDrumId.value })
    }

    const handleLegacyFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        selectedFile.value = file
      }
    }

    // Add missing state variables for legacy test compatibility
    const selectedFile = ref(null)
    const selectedDrumId = ref('')
    const actionDrumId = ref('')
    const pollTimer = ref(null)

    // Lifecycle hooks
    onMounted(() => {
      // WebSocket connection is now managed globally
      // Load initial data if connected
      if (statusStore.isConnected) {
        loadParameters()
      }
      // Start polling backend job status while a job is active
      if (!pollTimer.value) {
        pollTimer.value = setInterval(async () => {
          try {
            if (printJobStore.hasActiveJob) {
              await printJobStore.fetchJobStatus()
            }
          } catch (e) {
            console.warn('Polling job status failed:', e)
          }
        }, 2000)
      }
    })

    onUnmounted(() => {
      // Clean up object URL
      if (previewImageUrl.value) {
        URL.revokeObjectURL(previewImageUrl.value)
      }
      if (pollTimer.value) {
        clearInterval(pollTimer.value)
        pollTimer.value = null
      }
    })

    return {
      statusStore,
      isLoading,
      previewLoading,
      previewImageUrl,
      previewSource,
      successMessage,
      errorMessage,
      layerParams,
      isParametersValid,

      // File management - New drum-specific handlers
      isFileOperationLoading,
      parsingInProgress,
      availableDrums,
      handleDrumUpload,
      handleDrumDownload,
      handleDrumDelete,

      // Legacy file management for test compatibility
      selectedFile,
      selectedDrumId,
      actionDrumId,

      // CLI file management
      selectedCliFile,
      cliFileInfo,
      selectedLayerNum,
      selectedTargetDrumId,
      isCliOperationLoading,
      isCliPreviewLoading,
      isCliSendLoading,
      cliFileInput,
      handleCliFileSelect,
      clearCliFileSelection,
      uploadCliFile,
      previewCliLayer,
      sendCliLayerToDrum,

      // CLI layer range selection
      startLayerNum,
      endLayerNum,
      rangeTargetDrumId,
      isRangeOperationLoading,
      sendCliLayerRangeToDrum,

      // Job management
      isJobOperationLoading,
      jobStatus,
      isPrinting,
      isAnyJobActive,
      hasJobError,
      getJobStatusClass,
      getJobStatusText,
      startPrintJob,
      cancelPrintJob,
      refreshJobStatus,

      // Existing methods
      loadParameters,
      saveParameters,
      loadPreview,
      handlePreviewError,

      // Test compatibility methods
      uploadFile,
      downloadFile,
      deleteFile,
      formatFileSize,
      handleLegacyFileSelect
    }
  }
}
</script>

<style scoped>
.print-view {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-base);
}

.view-title {
  margin: 0 0 2rem 0;
  color: #111827;
  font-size: 2rem;
  font-weight: 650;
}


/* Status Card */
.status-card {
  background: white;
  border-radius: 8px;
  padding: 1rem 1.5rem;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  border: 1px solid #e5e7eb;
  margin-bottom: 2rem;
}
.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.status-title {
  margin: 0;
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
}
.status-indicator { display: flex; align-items: center; gap: 0.5rem; }
.status-dot { width: 10px; height: 10px; border-radius: 50%; }
.status-connected { background-color: #10b981; }
.status-disconnected { background-color: #ef4444; }
.status-text { font-weight: 500; color: #374151; }

/* Control Cards */
.control-card {
  background: white;
  border-radius: 8px;
  margin-bottom: var(--spacing-lg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--color-neutral-300);
  position: relative;
}

.card-header {
  padding: var(--spacing-lg) var(--spacing-lg) 0 var(--spacing-lg);
  border-bottom: 1px solid var(--color-neutral-300);
  margin-bottom: var(--spacing-md);
}

.card-title {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--color-neutral-800);
}

.card-subtitle {
  margin: 0 0 var(--spacing-base) 0;
  color: var(--color-neutral-600);
  font-size: var(--font-size-sm);
  font-style: italic;
  line-height: 1.4;
}

.card-content {
  padding: 0rem var(--spacing-lg) 1.2rem var(--spacing-lg);
  position: relative;
}

.disabled-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 8px;
}

.disabled-text {
  color: var(--color-neutral-600);
  font-weight: 500;
  text-align: center;
}

/* Parameter Grid */
.parameter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

/* Parameter Group Styling */
.parameter-group {
  margin-bottom: 0rem;
}

.parameter-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--color-neutral-700);
  font-size: var(--font-size-sm);
}

.parameter-input, .parameter-checkbox {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--color-neutral-400);
  border-radius: 4px;
  background-color: white;
}

.parameter-checkbox {
  width: auto;
  margin-right: var(--spacing-sm);
}

.parameter-help {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--color-neutral-600);
}

/* Form Elements */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--color-neutral-700);
  font-size: var(--font-size-sm);
}

.form-select, .form-input, .form-file-input {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--color-neutral-400);
  border-radius: 4px;
  background-color: white;
  font-size: var(--font-size-sm);
}

/* Button Groups */
.button-group {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.job-controls {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

/* Preview Styles */
.preview-container {
  position: relative;
  min-height: 200px;
  margin-bottom: var(--spacing-md);
}

.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--color-neutral-600);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-neutral-300);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-sm);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.preview-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--color-neutral-500);
  background-color: var(--color-neutral-100);
  border-radius: 4px;
}

.preview-icon {
  width: 120px; /* Constrained to max 120px */
  height: 120px;
  max-width: 20%; /* Limited to 20% of container */
  margin-bottom: var(--spacing-sm);
  opacity: 0.5;
}

.preview-controls {
  margin-top: var(--spacing-md);
}

/* Status Display */
.status-display {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--color-neutral-100);
  border-radius: 4px;
}

.status-label {
  font-weight: 500;
  color: var(--color-neutral-700);
}

.status-value {
  font-weight: 600;
}

.status-active {
  color: var(--color-success);
}

.status-inactive {
  color: var(--color-neutral-600);
}

.status-error {
  color: var(--color-danger);
}

.status-ok {
  color: var(--color-success);
}

.status-ready {
  color: var(--color-neutral-700);
}

/* CLI Management */
.cli-management-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-md);
}

.cli-upload-section, .layer-preview-section {
  padding: var(--spacing-md);
  background-color: var(--color-neutral-100);
  border-radius: 4px;
}

.section-title {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--color-neutral-800);
}

.cli-info {
  padding: var(--spacing-sm);
  background-color: white;
  border-radius: 4px;
  margin-bottom: var(--spacing-md);
}

.file-info {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-sm);
  background-color: var(--color-neutral-100);
  border-radius: 4px;
  font-size: var(--font-size-sm);
  color: var(--color-neutral-700);
}

.file-info.success-info {
  background-color: rgba(46, 204, 113, 0.1);
  border: 1px solid rgba(46, 204, 113, 0.3);
  color: var(--color-success);
}

.layer-range-info {
  font-size: var(--font-size-xs);
  color: var(--color-neutral-600);
  margin-top: var(--spacing-xs);
}

/* Messages */
.message {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
  max-width: 400px;
}

.message-success {
  background-color: rgba(39, 174, 96, 0.1);
  border: 1px solid var(--color-success);
  color: var(--color-success-hover);
}

.message-error {
  background-color: rgba(231, 76, 60, 0.1);
  border: 1px solid var(--color-danger);
  color: var(--color-danger-hover);
}

/* Responsive Design */
@media (max-width: 768px) {
  .print-view {
    padding: var(--spacing-sm);
  }

  .parameter-grid {
    grid-template-columns: 1fr;
  }

  .cli-management-grid {
    grid-template-columns: 1fr;
  }

  /* Parsing banner */
  .parsing-banner {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #fff8e1; /* light amber */
    border: 1px solid #f1c40f; /* amber border */
    color: #7d6608; /* dark amber text */
    padding: 8px 12px;
    border-radius: 6px;
    margin: 8px 0 16px;
  }
  .parsing-banner .spinner {
    width: 14px;
    height: 14px;
    border: 2px solid #f1c40f;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 0.9s linear infinite;
  }
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }


  .status-header {
    flex-direction: column;
    gap: var(--spacing-base);
    align-items: flex-start;
  }

  .status-display {
    grid-template-columns: 1fr;
  }

  .button-group,
  .job-controls {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .preview-icon {
    width: 80px;
    height: 80px;
  }
}
</style>