import pytest
import struct
from infrastructure.cli_editor.editor import Editor, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Point
from infrastructure.cli_editor.cli_exceptions import CliGenerationError

class TestBinaryCliGeneration:
    """Test binary CLI generation methods."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

        # Create test layer with sample data
        self.test_layer = CliLayer(
            z_height=16.0,
            polylines=[
                Polyline(
                    part_id=1,
                    direction=1,
                    points=[
                        Point(x=100.0, y=200.0),
                        Point(x=150.0, y=250.0),
                        Point(x=200.0, y=200.0)
                    ]
                )
            ],
            hatches=[
                Hatch(
                    group_id=1,
                    lines=[
                        (Point(x=50.0, y=50.0), Point(x=100.0, y=100.0)),
                        (Point(x=60.0, y=60.0), Point(x=110.0, y=110.0))
                    ]
                )
            ]
        )

    def test_generate_single_layer_binary_cli_basic(self):
        """Test basic binary CLI generation for a single layer."""
        cli_data = self.parser.generate_single_layer_cli(self.test_layer)

        assert b"$$HEADEREND" in cli_data

        # Layer command (127)
        assert struct.pack("<H", 127) in cli_data
        assert struct.pack("<f", 16.0) in cli_data

        # Polyline command (130)
        assert struct.pack("<H", 130) in cli_data
        assert struct.pack("<iii", 1, 1, 3) in cli_data
        assert struct.pack("<ff", 100.0, 200.0) in cli_data

        # Hatch command (132)
        assert struct.pack("<H", 132) in cli_data
        assert struct.pack("<ii", 1, 2) in cli_data
        assert struct.pack("<ffff", 50.0, 50.0, 100.0, 100.0) in cli_data

    def test_generate_single_layer_binary_cli_custom_header(self):
        """Test binary CLI generation with custom header."""
        custom_header = [
            "$$CUSTOM/test",
            "$$HEADEREND"
        ]

        cli_data = self.parser.generate_single_layer_cli(self.test_layer, custom_header)

        assert b"$$CUSTOM/test" in cli_data
        assert struct.pack("<f", 16.0) in cli_data

    def test_generate_single_layer_binary_cli_aligned(self):
        """Test binary CLI generation with alignment."""
        cli_data = self.parser.generate_single_layer_cli(self.test_layer, is_aligned=True)

        # Check for alignment bytes after command codes
        assert struct.pack("<H", 127) + b'\x00\x00' in cli_data
        assert struct.pack("<H", 130) + b'\x00\x00' in cli_data
        assert struct.pack("<H", 132) + b'\x00\x00' in cli_data

    def test_generate_binary_cli_from_layer_range(self):
        """Test binary CLI generation for multiple layers."""
        layer2 = CliLayer(
            z_height=32.0,
            polylines=[Polyline(part_id=2, direction=0, points=[Point(x=300.0, y=400.0)])],
            hatches=[]
        )

        layers = [self.test_layer, layer2]
        cli_data = self.parser.generate_cli_from_layer_range(layers)

        assert b"$$HEADEREND" in cli_data

        # Check for both layers' data
        assert struct.pack("<f", 16.0) in cli_data
        assert struct.pack("<f", 32.0) in cli_data

        # Check for both polylines' data
        assert struct.pack("<iii", 1, 1, 3) in cli_data
        assert struct.pack("<iii", 2, 0, 1) in cli_data

    def test_generate_binary_cli_empty_layer_range_error(self):
        """Test that empty layer range raises error."""
        with pytest.raises(CliGenerationError) as exc_info:
            self.parser.generate_cli_from_layer_range([])

        assert "empty layer range" in str(exc_info.value).lower()

    def test_generate_binary_cli_from_layer_range_aligned(self):
        """Test binary CLI generation for multiple layers with alignment."""
        layer2 = CliLayer(z_height=32.0, polylines=[], hatches=[])
        layers = [self.test_layer, layer2]
        cli_data = self.parser.generate_cli_from_layer_range(layers, is_aligned=True)

        # Check for alignment bytes after command codes
        assert (struct.pack("<H", 127) + b'\x00\x00') in cli_data
        assert (struct.pack("<H", 130) + b'\x00\x00') in cli_data
        assert (struct.pack("<H", 132) + b'\x00\x00') in cli_data
