"""
OPC UA Server Simulator for Learning (Tests Folder)

This version is placed under tests/ and resolves config.yaml relative to the repo root.
"""

import os
import asyncio
import random
import math
from asyncua import Server, ua
import logging

class OPCUAMachineSimulator:
    def __init__(self, machine_type, port, namespace_index=4):
        self.machine_type = machine_type
        self.port = port
        self.namespace_index = namespace_index
        self.server = None
        self.nodes = {}
        self.running = False
        print(f"Initializing {machine_type} simulator on port {port}")

    async def start_server(self):
        self.server = Server()
        await self.server.init()
        self.server.set_endpoint(f"opc.tcp://localhost:{self.port}")
        self.server.set_server_name(f"Simulated {self.machine_type}")
        namespace_uri = f"http://simulated.{self.machine_type}.com"
        namespace_idx = await self.server.register_namespace(namespace_uri)
        await self._create_machine_nodes(namespace_idx)
        await self.server.start()
        self.running = True
        print(f"✅ {self.machine_type} OPC UA server started on port {self.port}")
        asyncio.create_task(self._simulate_machine_data())

    async def _create_machine_nodes(self, namespace_idx):
        objects = self.server.nodes.objects
        machine_folder = await objects.add_folder(namespace_idx, f"{self.machine_type}_Machine")
        if self.machine_type == "3d_printer":
            await self._create_3d_printer_nodes(machine_folder, namespace_idx)
        elif self.machine_type == "cnc_machine":
            await self._create_cnc_machine_nodes(machine_folder, namespace_idx)
        elif self.machine_type == "power_meter":
            await self._create_power_meter_nodes(machine_folder, namespace_idx)

    async def _create_3d_printer_nodes(self, parent_folder, namespace_idx):
        env_folder = await parent_folder.add_folder(namespace_idx, "Environment")
        self.nodes['temperature'] = await env_folder.add_variable(namespace_idx, "Temperature", 245.0, ua.VariantType.Double)
        await self.nodes['temperature'].set_writable()
        self.nodes['pressure'] = await env_folder.add_variable(namespace_idx, "Pressure", 2.3, ua.VariantType.Double)
        await self.nodes['pressure'].set_writable()
        self.nodes['humidity'] = await env_folder.add_variable(namespace_idx, "Humidity", 45.2, ua.VariantType.Double)
        await self.nodes['humidity'].set_writable()
        status_folder = await parent_folder.add_folder(namespace_idx, "Status")
        self.nodes['status'] = await status_folder.add_variable(namespace_idx, "Current", "Running", ua.VariantType.String)
        await self.nodes['status'].set_writable()
        process_folder = await parent_folder.add_folder(namespace_idx, "Process")
        self.nodes['build_progress'] = await process_folder.add_variable(namespace_idx, "BuildProgress", 0.0, ua.VariantType.Double)
        await self.nodes['build_progress'].set_writable()

    async def _create_cnc_machine_nodes(self, parent_folder, namespace_idx):
        spindle_folder = await parent_folder.add_folder(namespace_idx, "spindle")
        self.nodes['spindle_speed'] = await spindle_folder.add_variable(namespace_idx, "SpindleSpeed", 1200.0, ua.VariantType.Double)
        await self.nodes['spindle_speed'].set_writable()
        sensors_folder = await parent_folder.add_folder(namespace_idx, "sensors")
        self.nodes['vibration'] = await sensors_folder.add_variable(namespace_idx, "Vibration", 0.1, ua.VariantType.Double)
        await self.nodes['vibration'].set_writable()
        tools_folder = await parent_folder.add_folder(namespace_idx, "tools")
        self.nodes['tool_wear'] = await tools_folder.add_variable(namespace_idx, "ToolWear", 0.0, ua.VariantType.Double)
        await self.nodes['tool_wear'].set_writable()
        status_folder = await parent_folder.add_folder(namespace_idx, "status")
        self.nodes['status'] = await status_folder.add_variable(namespace_idx, "Status", "Idle", ua.VariantType.String)
        await self.nodes['status'].set_writable()

    async def _create_power_meter_nodes(self, parent_folder, namespace_idx):
        power_folder = await parent_folder.add_folder(namespace_idx, "Power")
        voltage_folder = await power_folder.add_folder(namespace_idx, "Voltage")
        self.nodes['voltage_l1'] = await voltage_folder.add_variable(namespace_idx, "L1", 230.1, ua.VariantType.Double)
        self.nodes['voltage_l2'] = await voltage_folder.add_variable(namespace_idx, "L2", 230.5, ua.VariantType.Double)
        self.nodes['voltage_l3'] = await voltage_folder.add_variable(namespace_idx, "L3", 229.8, ua.VariantType.Double)
        current_folder = await power_folder.add_folder(namespace_idx, "Current")
        self.nodes['current_total'] = await current_folder.add_variable(namespace_idx, "Total", 15.5, ua.VariantType.Double)
        active_power_folder = await power_folder.add_folder(namespace_idx, "Active")
        self.nodes['power_active'] = await active_power_folder.add_variable(namespace_idx, "Total", 3.5, ua.VariantType.Double)
        for k in ['voltage_l1', 'voltage_l2', 'voltage_l3', 'current_total', 'power_active']:
            await self.nodes[k].set_writable()

    async def _simulate_machine_data(self):
        print(f"Starting data simulation for {self.machine_type}")
        t = 0
        while self.running:
            try:
                if self.machine_type == "3d_printer":
                    base_temp = 245.0
                    temp_variation = 5.0 * math.sin(t * 0.1) + random.uniform(-1, 1)
                    await self.nodes['temperature'].write_value(base_temp + temp_variation)
                    pressure_variation = random.uniform(-0.05, 0.05)
                    await self.nodes['pressure'].write_value(2.3 + pressure_variation)
                    prog = await self.nodes['build_progress'].read_value()
                    if prog < 100.0:
                        await self.nodes['build_progress'].write_value(min(100.0, prog + random.uniform(0.05, 0.1)))
                        await self.nodes['status'].write_value("Running")
                    else:
                        await self.nodes['status'].write_value("Finished")
                elif self.machine_type == "cnc_machine":
                    current_speed = await self.nodes['spindle_speed'].read_value()
                    target_speed = random.uniform(500, 8000) if random.random() < 0.1 else current_speed + random.uniform(-50, 50)
                    new_speed = max(0, min(10000, target_speed))
                    await self.nodes['spindle_speed'].write_value(new_speed)
                    await self.nodes['status'].write_value("Running" if new_speed > 10 else "Idle")
                    base_vibration = new_speed / 2000
                    await self.nodes['vibration'].write_value(max(0, base_vibration + random.uniform(-0.1, 0.1)))
                    if (await self.nodes['status'].read_value()) == "Running":
                        wear = await self.nodes['tool_wear'].read_value()
                        if wear < 100.0:
                            await self.nodes['tool_wear'].write_value(min(100.0, wear + 0.01))
                elif self.machine_type == "power_meter":
                    for phase in ['voltage_l1', 'voltage_l2', 'voltage_l3']:
                        await self.nodes[phase].write_value(230.0 + random.uniform(-0.2, 0.2))
                    day_cycle = math.sin((t % 86400) / 86400 * 2 * math.pi - (math.pi/2)) + 1
                    base_power = 5 * day_cycle + random.uniform(0, 2)
                    base_current = 15 * day_cycle + random.uniform(0, 5)
                    await self.nodes['power_active'].write_value(max(0, base_power))
                    await self.nodes['current_total'].write_value(max(0, base_current))
                t += 1
                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error in data simulation: {e}")
                await asyncio.sleep(5)

    async def stop_server(self):
        print(f"Stopping {self.machine_type} OPC UA server...")
        self.running = False
        if self.server:
            await self.server.stop()
        print(f"✅ {self.machine_type} server stopped")

async def start_multiple_simulators(config):
    simulators = []
    for server_config in config['development']['opcua_simulation']['servers']:
        simulators.append(OPCUAMachineSimulator(server_config['machine_type'], server_config['port']))
    for s in simulators:
        await s.start_server()
    print("\n🔌 All OPC UA simulators running. Press Ctrl+C to stop.")
    print("📊 Connect your client to:")
    for s in simulators:
        print(f"   - {s.machine_type}: opc.tcp://localhost:{s.port}")
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        print("\nShutdown requested...")
    finally:
        for s in simulators:
            await s.stop_server()

async def main():
    print("=== OPC UA MACHINE SIMULATORS ===")
    import yaml
    # Resolve config.yaml from the repository root
    repo_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    config_path = os.path.join(repo_root, "config.yaml")
    try:
        with open(config_path) as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        print(f"Error: config.yaml not found at {config_path}")
        return
    await start_multiple_simulators(config)

if __name__ == "__main__":
    logging.basicConfig(level=logging.WARNING)
    asyncio.run(main())

