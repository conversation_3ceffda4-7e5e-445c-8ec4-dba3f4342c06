"""
Tests for CLI Generation and Rendering
======================================

This module contains tests for CLI file generation and layer rendering.
"""

import pytest
from unittest.mock import Mock

from infrastructure.cli_editor.editor import (
    Editor,
    ParsedCliFile,
    CliLayer,
    Polyline,
    Hatch,
    Point,
    CliParsingError
)
from infrastructure.cli_editor.cli_exceptions import CliGenerationError


class TestCliLayerRendering:
    """Test suite for CLI layer rendering functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_render_empty_layer(self):
        """Test rendering an empty layer."""
        layer = CliLayer(z_height=0.0, polylines=[], hatches=[])

        png_bytes = self.parser.render_layer_to_png(layer)

        assert isinstance(png_bytes, bytes)
        assert len(png_bytes) > 0
        # PNG files start with specific bytes
        assert png_bytes.startswith(b'\x89PNG\r\n\x1a\n')

    def test_render_layer_with_geometry(self):
        """Test rendering a layer with polylines and hatches."""
        polyline = Polyline(
            part_id=1,
            direction=0,
            points=[Point(x=0, y=0), Point(x=10, y=10), Point(x=20, y=0)]
        )
        hatch = Hatch(
            group_id=1,
            lines=[(Point(x=5, y=5), Point(x=15, y=15))]
        )
        layer = CliLayer(z_height=0.0, polylines=[polyline], hatches=[hatch])

        png_bytes = self.parser.render_layer_to_png(layer, width=400, height=300)

        assert isinstance(png_bytes, bytes)
        assert len(png_bytes) > 0

    def test_render_custom_size(self):
        """Test rendering with custom image dimensions."""
        layer = CliLayer(z_height=0.0, polylines=[], hatches=[])

        png_bytes = self.parser.render_layer_to_png(layer, width=1024, height=768)

        assert isinstance(png_bytes, bytes)
        assert len(png_bytes) > 0


class TestCliGeneration:
    """Test suite for CLI file generation functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_generate_single_layer_cli(self):
        """Test generating CLI data for a single layer."""
        polyline = Polyline(
            part_id=1,
            direction=0,
            points=[Point(x=0, y=0), Point(x=10, y=10)]
        )
        layer = CliLayer(z_height=16.0, polylines=[polyline], hatches=[])

        cli_bytes = self.parser.generate_single_layer_cli(layer)

        assert isinstance(cli_bytes, bytes)
        assert len(cli_bytes) > 0

        # Parse the generated data to verify it's valid
        parsed = self.parser.parse(cli_bytes)
        assert len(parsed.layers) == 1
        assert parsed.layers[0].z_height == 16.0
        assert len(parsed.layers[0].polylines) == 1

    def test_generate_multi_layer_cli(self):
        """Test generating CLI data for multiple layers."""
        layers = [
            CliLayer(z_height=0.0, polylines=[], hatches=[]),
            CliLayer(z_height=16.0, polylines=[], hatches=[]),
            CliLayer(z_height=32.0, polylines=[], hatches=[])
        ]

        cli_bytes = self.parser.generate_cli_from_layer_range(layers)

        assert isinstance(cli_bytes, bytes)
        assert len(cli_bytes) > 0

        # Parse the generated data to verify it's valid
        parsed = self.parser.parse(cli_bytes)
        assert len(parsed.layers) == 3
        assert parsed.layers[0].z_height == 0.0
        assert parsed.layers[1].z_height == 16.0
        assert parsed.layers[2].z_height == 32.0

    def test_generate_with_custom_header(self):
        """Test generating CLI data with custom header."""
        layer = CliLayer(z_height=0.0, polylines=[], hatches=[])
        header_lines = ["$$BINARY", "$$VERSION 200", "$$HEADEREND"]

        cli_bytes = self.parser.generate_single_layer_cli(layer, header_lines=header_lines)

        parsed = self.parser.parse(cli_bytes)
        assert "$$VERSION 200" in parsed.header_lines

    def test_generate_empty_layer_range_error(self):
        """Test error handling for empty layer range."""
        with pytest.raises(CliGenerationError, match="empty layer range"):
            self.parser.generate_cli_from_layer_range([])
