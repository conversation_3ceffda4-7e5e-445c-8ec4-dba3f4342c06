"""
Test Configuration
==================

This module provides test fixtures and configuration for the test suite.
"""

import pytest
import sys
import os
import shutil
from pathlib import Path
from unittest.mock import Mock
from fastapi.testclient import TestClient

# Add the backend directory and project root to the Python path
backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
project_root = os.path.dirname(backend_dir)
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)
# Also include project root so 'backend.*' absolute imports work
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Ensure binary test file is accessible from backend working directory
try:
    import shutil
    src_binary = os.path.join(project_root, 'tests', 'Tube_Binary.cli')
    dst_dir = os.path.join(backend_dir, 'tests')
    dst_binary = os.path.join(dst_dir, 'Tube_Binary.cli')
    if os.path.exists(src_binary):
        os.makedirs(dst_dir, exist_ok=True)
        if not os.path.exists(dst_binary):
            shutil.copyfile(src_binary, dst_binary)
except Exception:
    pass

# Import the app and dependencies
from app.main import app
import app.dependencies as deps


@pytest.fixture(autouse=True)
def setup_test_dependencies():
    """
    Automatically setup mock dependencies for all tests.
    This fixture runs before each test and ensures we have a mock client.
    """
    # Create a mock recoater client
    mock_client = Mock()

    # Set up default return values for common methods
    mock_client.get_state.return_value = {"status": "idle", "timestamp": "2024-01-01T00:00:00Z"}
    mock_client.get_config.return_value = {
        "build_space_diameter": 250.0,
        "build_space_dimensions": {
            "length": 250.0,
            "width": 96.0
        },
        "ejection_matrix_size": 192,
        "gaps": [130.0, 130.0],
        "resolution": 500
    }
    mock_client.set_config.return_value = {"success": True}
    mock_client.get_drums.return_value = [{"id": 0, "status": "ready"}]
    mock_client.health_check.return_value = True


    # Drum control methods
    mock_client.get_drum_motion.return_value = {"mode": "idle", "speed": 0.0}
    mock_client.set_drum_motion.return_value = {"command_id": "drum_motion_123"}
    mock_client.cancel_drum_motion.return_value = {"success": True}
    mock_client.get_drum_ejection.return_value = {"target": 0.0, "unit": "pascal"}
    mock_client.set_drum_ejection.return_value = {"success": True}
    mock_client.get_drum_suction.return_value = {"target": 0.0, "unit": "pascal"}
    mock_client.set_drum_suction.return_value = {"success": True}

    # Leveler control methods
    mock_client.get_leveler_pressure.return_value = {"maximum": 10.0, "target": 5.0, "value": 4.8}
    mock_client.set_leveler_pressure.return_value = {"success": True, "target_pressure": 5.0, "unit": "Pa"}
    mock_client.get_leveler_sensor.return_value = {"state": True}

    # Print control methods
    mock_client.get_layer_parameters.return_value = {
        "filling_id": 1,
        "speed": 30.0,
        "powder_saving": True,
        "x_offset": 0.0,
        "max_x_offset": 100.0
    }
    mock_client.set_layer_parameters.return_value = {"success": True}
    mock_client.get_layer_preview.return_value = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
    mock_client.start_print_job.return_value = {"success": True, "job_id": "job_12345", "status": "started"}
    mock_client.cancel_print_job.return_value = {"success": True, "status": "cancelled"}
    mock_client.get_print_job_status.return_value = {"state": "ready", "is_printing": False, "has_error": False}

    # File management methods
    mock_client.upload_drum_geometry.return_value = {"success": True, "drum_id": 1, "file_size": 1024, "content_type": "image/png"}
    mock_client.download_drum_geometry.return_value = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89'
    mock_client.delete_drum_geometry.return_value = {"success": True, "drum_id": 1, "message": "Geometry file deleted successfully"}

    # Replace the global client in the dependencies module
    original_client = deps._recoater_client
    deps._recoater_client = mock_client

    # Override the dependency function to return our mock
    app.dependency_overrides[deps.get_recoater_client] = lambda: mock_client

    # Initialize the multilayer job manager for tests that need it
    try:
        deps.initialize_multilayer_job_manager()
    except Exception:
        # If initialization fails, create a mock multilayer job manager with required methods
        mock_job_manager = Mock()
        # Set up basic CLI cache functionality for tests
        mock_job_manager.cli_cache = {}
        mock_job_manager.get_cli_file_with_metadata = Mock(return_value=None)
        mock_job_manager.cli_file_exists = Mock(return_value=False)
        mock_job_manager.add_cli_file = Mock()
        mock_job_manager.create_job = Mock()
        mock_job_manager.start_job = Mock()
        
        deps._multilayer_job_manager = mock_job_manager
        app.dependency_overrides[deps.get_multilayer_job_manager] = lambda: mock_job_manager

    yield mock_client

    # Restore the original client after the test
    deps._recoater_client = original_client
    app.dependency_overrides.clear()


@pytest.fixture
def client():
    """
    Provide a test client for making HTTP requests.
    """
    return TestClient(app)


@pytest.fixture
def mock_recoater_client(setup_test_dependencies):
    """
    Provide access to the mock recoater client for test customization.
    """
    return setup_test_dependencies


@pytest.fixture(scope="session", autouse=True)
def cleanup_temp_cli_files():
    """
    Session-level fixture to ensure temp CLI files are cleaned up after all tests.
    
    This fixture automatically runs after all tests in the session finish,
    ensuring that the temp_cli_files directory is always cleaned up regardless
    of individual test outcomes.
    """
    yield  # Let all tests run first
    
    # Cleanup after all tests are done
    try:
        # Import here to avoid circular imports
        from app.api.print import TEMP_CLI_DIR
        
        if TEMP_CLI_DIR.exists():
            shutil.rmtree(TEMP_CLI_DIR)
            print(f"\n✓ Cleaned up temporary CLI files directory: {TEMP_CLI_DIR}")
        else:
            print(f"\n✓ No temporary CLI files to clean up")
    except Exception as e:
        print(f"\n⚠ Warning: Failed to cleanup temp CLI files: {e}")
