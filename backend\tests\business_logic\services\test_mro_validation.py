"""
Method Resolution Order (MRO) Validation Tests
==============================================

These tests ensure that mixin-based classes have correct method resolution order
and that method overrides work as intended. This prevents issues where the wrong
method is called due to inheritance order problems.

Background: The OPCUAService had an issue where ServerMixin.write_variable() was
being called instead of CoordinationMixin.write_variable(), preventing event
handlers from being triggered. This test suite prevents similar issues in the future.
"""

import pytest
import inspect
from typing import Dict, Any

from app.services.opcua.opcua_service import OPCUAService
from app.services.opcua.mixins import ServerMixin, CoordinationMixin, MonitoringMixin

from app.services.job_management.multimaterial_job_service import MultiMaterialJobService
from app.services.job_management.mixins.layer_operations_mixin import LayerProcessingMixin
from app.services.job_management.mixins.coordination_mixin import OPCUACoordinationMixin
from app.services.job_management.mixins.cli_caching_mixin import CliCachingMixin


class TestOPCUAServiceMRO:
    """Test Method Resolution Order for OPCUAService"""

    def test_mro_order_is_correct(self):
        """Test that the MRO puts CoordinationMixin before ServerMixin"""
        mro = [cls.__name__ for cls in OPCUAService.__mro__]
        
        # CoordinationMixin should come before ServerMixin to ensure
        # CoordinationMixin.write_variable() is called instead of ServerMixin.write_variable()
        coordination_idx = mro.index('CoordinationMixin')
        server_idx = mro.index('ServerMixin')
        monitoring_idx = mro.index('MonitoringMixin')
        
        assert coordination_idx < server_idx, f"CoordinationMixin should come before ServerMixin in MRO: {mro}"
        assert server_idx < monitoring_idx, f"ServerMixin should come before MonitoringMixin in MRO: {mro}"

    def test_write_variable_method_resolution(self):
        """Test that write_variable resolves to CoordinationMixin, not ServerMixin"""
        service = OPCUAService()
        
        # The write_variable method should come from CoordinationMixin
        method_class = service.write_variable.__qualname__.split('.')[0]
        assert method_class == 'CoordinationMixin', \
            f"write_variable should resolve to CoordinationMixin, got {method_class}"

    def test_method_delegation_works(self):
        """Test that CoordinationMixin.write_variable can call super().write_variable"""
        service = OPCUAService()
        
        # Verify that CoordinationMixin has access to ServerMixin methods via super()
        coordination_mixin = CoordinationMixin()
        coordination_mixin._server_running = True
        coordination_mixin._variable_store = {}
        coordination_mixin._logger = service._logger
        
        # This should work without errors (tests the super() chain)
        assert hasattr(coordination_mixin, 'write_variable')

    def test_event_handling_capability(self):
        """Test that the MRO enables event handling functionality"""
        service = OPCUAService()
        
        # The service should have event handling methods from CoordinationMixin
        assert hasattr(service, 'subscribe_to_changes')
        assert hasattr(service, '_trigger_event_handlers')
        assert hasattr(service, '_event_handlers')

    @pytest.mark.asyncio
    async def test_event_handling_integration(self):
        """Integration test to ensure event handlers work with current MRO"""
        service = OPCUAService()
        await service.connect()
        
        try:
            # Test event subscription and triggering
            events_received = []
            
            async def test_handler(name, value):
                events_received.append((name, value))
            
            await service.subscribe_to_changes(['current_layer'], test_handler)
            
            # This should trigger the event handler due to correct MRO
            success = await service.write_variable('current_layer', 42)
            assert success
            assert ('current_layer', 42) in events_received
            
        finally:
            await service.disconnect()


class TestMultiMaterialJobServiceMRO:
    """Test Method Resolution Order for MultiMaterialJobService"""

    def test_mro_order_is_logical(self):
        """Test that the MRO has a logical order for job management"""
        mro = [cls.__name__ for cls in MultiMaterialJobService.__mro__]

        # Expected order should be: LayerProcessingMixin, OPCUACoordinationMixin, CliCachingMixin
        processing_idx = mro.index('LayerProcessingMixin')
        coordination_idx = mro.index('OPCUACoordinationMixin')
        caching_idx = mro.index('CliCachingMixin')

        assert processing_idx < coordination_idx, \
            f"LayerProcessingMixin should come before OPCUACoordinationMixin: {mro}"
        assert coordination_idx < caching_idx, \
            f"OPCUACoordinationMixin should come before CliCachingMixin: {mro}"

    def test_no_method_conflicts(self):
        """Test that there are no unintended method conflicts between mixins"""
        from infrastructure.recoater_client import RecoaterClient
        
        # Mock recoater client for testing
        class MockRecoaterClient:
            pass
        
        service = MultiMaterialJobService(MockRecoaterClient())
        
        # Check that modern workflow methods exist and come from expected classes  
        assert hasattr(service, 'start_layer_by_layer_job')  # From MultiMaterialJobService (modern workflow)
        assert hasattr(service, 'cancel_job')  # From LayerProcessingMixin
        assert hasattr(service, 'setup_opcua_job')  # From OPCUACoordinationMixin
        
        # Verify legacy methods have been removed in favor of modern workflow
        assert not hasattr(service, 'create_job')  # Removed - replaced by start_layer_by_layer_job
        assert not hasattr(service, 'start_job')  # Removed - replaced by start_layer_by_layer_job  
        assert not hasattr(service, 'upload_layer_to_all_drums')  # Removed - internal to modern workflow

    def test_error_handling_methods_distinction(self):
        """Test that error handling methods from different mixins are distinct"""
        from infrastructure.recoater_client import RecoaterClient
        
        class MockRecoaterClient:
            pass
        
        service = MultiMaterialJobService(MockRecoaterClient())
        
        # Different mixins have error clearing methods - ensure they're different
        assert hasattr(service, 'clear_error_flags')  # From OPCUACoordinationMixin
        assert hasattr(service, 'clear_job_error_flags')  # From LayerProcessingMixin
        assert hasattr(service, 'clear_all_error_flags')  # From main service

        # They should be different methods
        assert service.clear_error_flags != service.clear_job_error_flags
        assert service.clear_error_flags != service.clear_all_error_flags


class TestMixinMethodValidation:
    """General tests for mixin method validation"""

    def test_opcua_mixins_have_distinct_responsibilities(self):
        """Test that OPC UA mixins have distinct method responsibilities"""
        server_methods = {name for name, _ in inspect.getmembers(ServerMixin, predicate=inspect.isfunction)
                         if not name.startswith('__')}
        coordination_methods = {name for name, _ in inspect.getmembers(CoordinationMixin, predicate=inspect.isfunction)
                               if not name.startswith('__')}
        monitoring_methods = {name for name, _ in inspect.getmembers(MonitoringMixin, predicate=inspect.isfunction)
                             if not name.startswith('__')}
        
        # Check for overlaps (excluding private methods and __init__)
        server_coord_overlap = server_methods & coordination_methods
        # write_variable and read_variable are intentionally overridden, so exclude them
        server_coord_overlap.discard('write_variable')
        server_coord_overlap.discard('read_variable')
        
        assert not server_coord_overlap, f"Unexpected method overlap between ServerMixin and CoordinationMixin: {server_coord_overlap}"
        
        coord_monitor_overlap = coordination_methods & monitoring_methods
        assert not coord_monitor_overlap, f"Unexpected method overlap between CoordinationMixin and MonitoringMixin: {coord_monitor_overlap}"
        
        server_monitor_overlap = server_methods & monitoring_methods
        assert not server_monitor_overlap, f"Unexpected method overlap between ServerMixin and MonitoringMixin: {server_monitor_overlap}"

    def test_job_management_mixins_have_distinct_responsibilities(self):
        """Test that consolidated job management mixins have distinct method responsibilities"""
        processing_methods = {name for name, _ in inspect.getmembers(LayerProcessingMixin, predicate=inspect.isfunction)
                            if not name.startswith('__')}
        coordination_methods = {name for name, _ in inspect.getmembers(OPCUACoordinationMixin, predicate=inspect.isfunction)
                               if not name.startswith('__')}
        caching_methods = {name for name, _ in inspect.getmembers(CliCachingMixin, predicate=inspect.isfunction)
                          if not name.startswith('__')}

        # Check for overlaps between processing and coordination
        processing_coord_overlap = processing_methods & coordination_methods
        # clear_error_flags vs clear_job_error_flags are intentionally different methods
        processing_coord_overlap.discard('clear_error_flags')
        processing_coord_overlap.discard('clear_job_error_flags')

        assert not processing_coord_overlap, f"Unexpected method overlap between LayerProcessingMixin and OPCUACoordinationMixin: {processing_coord_overlap}"

        # Check for overlaps between processing and caching
        processing_caching_overlap = processing_methods & caching_methods
        assert not processing_caching_overlap, f"Unexpected method overlap between LayerProcessingMixin and CliCachingMixin: {processing_caching_overlap}"

        # Check for overlaps between coordination and caching
        coord_caching_overlap = coordination_methods & caching_methods
        assert not coord_caching_overlap, f"Unexpected method overlap between OPCUACoordinationMixin and CliCachingMixin: {coord_caching_overlap}"


class TestMRORegressionPrevention:
    """Tests specifically designed to prevent MRO regressions"""

    @pytest.mark.asyncio
    async def test_opcua_event_handling_regression(self):
        """Regression test for OPC UA event handling MRO issue"""
        service = OPCUAService()
        await service.connect()
        
        try:
            # This is the exact test case that was failing before the MRO fix
            calls = []
            
            async def handler(name, value):
                calls.append((name, value))
            
            await service.subscribe_to_changes(["current_layer"], handler)
            await service.update_layer_progress(5)
            
            # Allow event loop to process
            import asyncio
            await asyncio.sleep(0)
            
            # This should work due to correct MRO
            assert ("current_layer", 5) in calls, \
                "Event handler should be called due to correct MRO (CoordinationMixin before ServerMixin)"
        
        finally:
            await service.disconnect()

    def test_method_resolution_documentation(self):
        """Test that classes with complex MRO have proper documentation"""
        # Check that OPCUAService has MRO documentation
        opcua_docstring = OPCUAService.__doc__ or ""
        assert "Inheritance Order" in opcua_docstring or "MRO" in opcua_docstring, \
            "OPCUAService should document its inheritance order reasoning"

    def test_critical_method_overrides_are_intentional(self):
        """Test that method overrides in complex inheritance are intentional"""
        service = OPCUAService()
        
        # write_variable should be overridden by CoordinationMixin
        write_var_method = service.write_variable
        assert 'CoordinationMixin' in write_var_method.__qualname__, \
            "write_variable should be intentionally overridden by CoordinationMixin"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
