"""
ServerMixin
===========

Real OPC UA server infrastructure for hosting coordination variables.

Clean Architecture Role: Infrastructure Layer (Adapters/External Interfaces)
Purpose:
- Provide a real OPC UA server implementation using asyncua library
- Manage server lifecycle (start/stop) and real OPC UA variable nodes
- Expose async read/write APIs for OPC UA protocol communication
- Handle type coercion and validation for variable operations

Architecture Notes:
- Uses asyncua library to provide a real OPC UA server implementation
- Maintains protocol-agnostic interface to allow different OPC UA implementations
- Uses configuration-driven variable definitions for type safety and validation
- Provides thread-safe access to OPC UA variables

Key Responsibilities:
- Server Lifecycle: start_server(), stop_server(), is_server_running property
- Variable Management: write_variable(), read_variable(), get_variable_names()
- Type Safety: Data type conversion based on variable definitions from config
- State Management: Maintains real OPC UA server with proper variable nodes

Dependencies:
- asyncua: For real OPC UA server implementation
- app.config.opcua_config: For COORDINATION_VARIABLES and server configuration

Public Methods:
- start_server(): Initialize real OPC UA server and create variables
- stop_server(): Cleanup server resources and stop OPC UA server
- is_server_running: Property to check if server is operational
- get_variable_names(): Get list of all available variable names
- write_variable(name, value): Write value to real OPC UA variable
- read_variable(name): Read current value from real OPC UA variable

Private Methods:
- _get_ua_variant_type(data_type): Convert config data type to asyncua VariantType
- _create_server_variables(): Create all OPC UA variables from configuration
- _get_server_config(): Get server configuration from opcua_config

Implementation Notes:
- Uses real asyncua Server for OPC UA communication
- Creates variables in server's Objects node with proper node IDs
- Handles proper async context management for server lifecycle
- Maintains consistent interface with previous in-memory implementation
"""
from __future__ import annotations

import asyncio
import logging
from typing import Any, Optional, Dict, List

from asyncua import Server, ua
from asyncua.common.node import Node
from app.config.opcua_config import COORDINATION_VARIABLES


class ServerMixin:
    _logger: logging.Logger
    _server_running: bool = False
    _opcua_server: Optional[Server] = None
    _variable_nodes: Dict[str, Node] = {}
    _variable_cache: Dict[str, Any] = {}  # Cache for synchronous access

    def _get_server_config(self):
        """Get server configuration from opcua_config."""
        try:
            from app.config.opcua_config import get_opcua_config
            return get_opcua_config()
        except Exception as e:
            self._logger.error(f"Failed to load OPC UA config: {e}")
            # Return default config if loading fails
            from app.config.opcua_config import OPCUAServerConfig
            return OPCUAServerConfig()

    def _get_ua_variant_type(self, data_type: str) -> ua.VariantType:
        """Convert config data type string to asyncua VariantType."""
        type_mapping = {
            "Boolean": ua.VariantType.Boolean,
            "Int32": ua.VariantType.Int32,
            "String": ua.VariantType.String,
            "Float": ua.VariantType.Float,
            "DateTime": ua.VariantType.DateTime,
        }
        return type_mapping.get(data_type, ua.VariantType.String)

    async def _create_server_variables(self) -> bool:
        """Create all OPC UA variables from configuration."""
        try:
            if not self._opcua_server:
                return False

            # Get the Objects node where we'll add our variables
            objects_node = self._opcua_server.get_objects_node()

            # Clear any existing variable nodes and cache
            self._variable_nodes.clear()
            self._variable_cache.clear()

            # Create each coordination variable
            for var_config in COORDINATION_VARIABLES:
                try:
                    # Create NodeId from the configuration
                    node_id = ua.NodeId.from_string(var_config.node_id)

                    # Get the appropriate variant type
                    variant_type = self._get_ua_variant_type(var_config.data_type)

                    # Add the variable to the server
                    var_node = await objects_node.add_variable(
                        node_id,
                        var_config.name,
                        var_config.initial_value,
                        variant_type
                    )

                    # Make the variable writable if specified in config
                    if var_config.writable:
                        await var_node.set_writable()

                    # Store the node reference for quick access
                    self._variable_nodes[var_config.name] = var_node

                    # Initialize cache with initial value
                    self._variable_cache[var_config.name] = var_config.initial_value

                    self._logger.debug(f"Created OPC UA variable: {var_config.name} -> {var_config.node_id}")

                except Exception as e:
                    self._logger.error(f"Failed to create variable {var_config.name}: {e}")
                    return False

            self._logger.info(f"Created {len(self._variable_nodes)} OPC UA variables")
            return True

        except Exception as e:
            self._logger.error(f"Failed to create server variables: {e}")
            return False

    async def start_server(self) -> bool:
        try:
            if self._server_running:
                self._logger.info("OPC UA server already running")
                return True

            # Get server configuration
            config = self._get_server_config()

            # Create and configure the OPC UA server
            self._opcua_server = Server()
            await self._opcua_server.init()

            # Configure server endpoint and name
            self._opcua_server.set_endpoint(config.endpoint)
            self._opcua_server.set_server_name(config.server_name)

            # Register our custom namespace
            namespace_idx = await self._opcua_server.register_namespace(config.namespace_uri)
            self._logger.info(f"Registered OPC UA namespace: {config.namespace_uri} (idx={namespace_idx})")

            # Create all coordination variables
            if not await self._create_server_variables():
                await self._opcua_server.stop()
                self._opcua_server = None
                return False

            # Start the server
            await self._opcua_server.start()
            self._server_running = True

            self._logger.info(f"OPC UA server started at {config.endpoint}")
            return True

        except Exception as e:
            self._logger.error(f"Failed to start OPC UA server: {e}")
            if self._opcua_server:
                try:
                    await self._opcua_server.stop()
                except:
                    pass
                self._opcua_server = None
            return False

    async def stop_server(self) -> bool:
        try:
            if not self._server_running:
                self._logger.info("OPC UA server already stopped")
                return True

            # Clear variable node references and cache
            self._variable_nodes.clear()
            self._variable_cache.clear()

            # Stop the OPC UA server
            if self._opcua_server:
                await self._opcua_server.stop()
                self._opcua_server = None

            self._server_running = False
            self._logger.info("OPC UA server stopped")
            return True

        except Exception as e:
            self._logger.error(f"Failed to stop OPC UA server: {e}")
            return False

    @property
    def is_server_running(self) -> bool:
        return self._server_running

    def get_variable_names(self) -> List[str]:
        return [v.name for v in COORDINATION_VARIABLES]

    def _convert_value_to_correct_type(self, name: str, value: Any) -> Any:
        """Convert value to the correct type based on variable configuration."""
        try:
            # Find the variable configuration
            var_config = None
            for var in COORDINATION_VARIABLES:
                if var.name == name:
                    var_config = var
                    break

            if not var_config:
                return value

            # Convert based on data type using asyncua types
            if var_config.data_type == "Boolean":
                return bool(value)
            elif var_config.data_type == "Int32":
                # Create a proper Int32 variant to avoid type mismatch
                from asyncua import ua
                return ua.Variant(int(value), ua.VariantType.Int32)
            elif var_config.data_type == "String":
                return str(value)
            elif var_config.data_type == "Float":
                return float(value)
            else:
                return value

        except Exception as e:
            self._logger.warning(f"Failed to convert value for {name}: {e}")
            return value

    async def write_variable(self, name: str, value: Any) -> bool:
        if not self._server_running:
            self._logger.warning(f"Cannot write {name}: server not running")
            return False

        if name not in self._variable_nodes:
            self._logger.warning(f"Variable {name} not found in server")
            return False

        try:
            # Convert value to correct type
            converted_value = self._convert_value_to_correct_type(name, value)

            # Get the variable node and write the value
            var_node = self._variable_nodes[name]
            await var_node.set_value(converted_value)

            # Update cache for synchronous access (store the original value, not the Variant)
            self._variable_cache[name] = value

            return True

        except Exception as e:
            self._logger.error(f"Failed to write variable {name}: {e}")
            return False

    async def read_variable(self, name: str) -> Any:
        if not self._server_running:
            self._logger.warning(f"Cannot read {name}: server not running")
            return None

        if name not in self._variable_nodes:
            self._logger.warning(f"Variable {name} not found in server")
            return None

        try:
            # Get the variable node and read the value
            var_node = self._variable_nodes[name]
            value = await var_node.read_value()
            return value

        except Exception as e:
            self._logger.error(f"Failed to read variable {name}: {e}")
            return None

