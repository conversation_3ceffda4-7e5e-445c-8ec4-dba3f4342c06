"""
Mock Blade Control Methods
==========================

This module contains mock blade control methods for the MockRecoaterClient.
These methods simulate the blade control endpoints from the hardware API.
"""

import random
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class MockBladeControlMixin:
    """Mixin class providing mock blade control methods for MockRecoaterClient."""

    def get_blade_screws_info(self, drum_id: int) -> Dict[str, Any]:
        """
        Mock implementation for getting blade screws info.

        Args:
            drum_id: The drum's ID

        Returns:
            Mock blade screws information
        """
        logger.info(f"Mock blade screws info for drum {drum_id}")

        return [
            {
                "id": 0,
                "position": random.uniform(0, 10000),  # µm
                "running": random.choice([True, False])
            },
            {
                "id": 1,
                "position": random.uniform(0, 10000),  # µm
                "running": random.choice([True, False])
            }
        ]

    def get_blade_screws_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Mock implementation for getting blade screws motion.

        Args:
            drum_id: The drum's ID

        Returns:
            Mock motion information or empty dict
        """
        logger.info(f"Mock blade screws motion for drum {drum_id}")

        # Randomly return motion or no motion
        if random.choice([True, False]):
            return {
                "mode": random.choice(["absolute", "relative", "homing"]),
                "distance": random.uniform(100, 5000)  # µm
            }
        else:
            return {}  # No current motion

    def set_blade_screws_motion(self, drum_id: int, mode: str, distance: float = None) -> Dict[str, Any]:
        """
        Mock implementation for setting blade screws motion.

        Args:
            drum_id: The drum's ID
            mode: Motion mode
            distance: Distance in µm

        Returns:
            Mock motion response
        """
        logger.info(f"Mock blade screws motion for drum {drum_id}: mode={mode}, distance={distance}")

        return {
            "success": True,
            "drum_id": drum_id,
            "mode": mode,
            "distance": distance,
            "estimated_time": random.uniform(1.0, 10.0)
        }

    def cancel_blade_screws_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Mock implementation for cancelling blade screws motion.

        Args:
            drum_id: The drum's ID

        Returns:
            Mock cancellation response
        """
        logger.info(f"Mock cancel blade screws motion for drum {drum_id}")

        return {
            "success": True,
            "drum_id": drum_id,
            "action": "motion_cancelled"
        }

    def get_blade_screw_info(self, drum_id: int, screw_id: int) -> Dict[str, Any]:
        """
        Mock implementation for getting individual blade screw info.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID

        Returns:
            Mock blade screw information
        """
        logger.info(f"Mock blade screw info for drum {drum_id}, screw {screw_id}")

        return {
            "id": screw_id,
            "position": random.uniform(0, 10000),  # µm
            "running": random.choice([True, False])
        }

    def get_blade_screw_motion(self, drum_id: int, screw_id: int) -> Dict[str, Any]:
        """
        Mock implementation for getting individual blade screw motion.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID

        Returns:
            Mock motion information or empty dict
        """
        logger.info(f"Mock blade screw motion for drum {drum_id}, screw {screw_id}")

        # Randomly return motion or no motion
        if random.choice([True, False]):
            return {
                "distance": random.uniform(100, 5000)  # µm
            }
        else:
            return {}  # No current motion

    def set_blade_screw_motion(self, drum_id: int, screw_id: int, distance: float) -> Dict[str, Any]:
        """
        Mock implementation for setting individual blade screw motion.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID
            distance: Distance in µm

        Returns:
            Mock motion response
        """
        logger.info(f"Mock blade screw motion for drum {drum_id}, screw {screw_id}: distance={distance}")

        return {
            "success": True,
            "drum_id": drum_id,
            "screw_id": screw_id,
            "distance": distance,
            "estimated_time": random.uniform(1.0, 5.0)
        }

    def cancel_blade_screw_motion(self, drum_id: int, screw_id: int) -> Dict[str, Any]:
        """
        Mock implementation for cancelling individual blade screw motion.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID

        Returns:
            Mock cancellation response
        """
        logger.info(f"Mock cancel blade screw motion for drum {drum_id}, screw {screw_id}")

        return {
            "success": True,
            "drum_id": drum_id,
            "screw_id": screw_id,
            "action": "motion_cancelled"
        }
