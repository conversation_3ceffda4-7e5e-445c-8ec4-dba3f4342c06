"""
Integration Tests for CLI Parser
================================

This module contains integration tests for the CLI parser,
including tests with real CLI files.
"""

import pytest
import struct
from unittest.mock import Mock

from infrastructure.cli_editor.editor import (
    Editor,
    ParsedCliFile,
    CliLayer,
    Polyline,
    Hatch,
    Point,
    CliParsingError
)
from infrastructure.cli_editor.cli_exceptions import CliGenerationError


class TestRealBinaryFileIntegration:
    """Integration tests using the real Tube_Binary.cli file."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()
        self.binary_file_path = "tests/Tube_Binary.cli"

    def test_parse_real_binary_cli_file(self):
        """Test parsing the real Tube_Binary.cli file."""
        with open(self.binary_file_path, 'rb') as f:
            binary_data = f.read()

        result = self.parser.parse(binary_data)

        # Validate basic structure
        assert isinstance(result, ParsedCliFile)
        assert len(result.header_lines) > 0
        assert len(result.layers) > 0

        # Validate header contains expected markers
        header_text = ' '.join(result.header_lines)
        assert '$$HEADEREND' in header_text

        # Validate layers have reasonable data
        for layer in result.layers:
            assert isinstance(layer.z_height, float)
            assert layer.z_height >= 0.0  # Assuming positive Z heights

            # Check that we have some geometry (polylines or hatches)
            total_geometry = len(layer.polylines) + len(layer.hatches)
            # Not all layers need geometry, but at least some should have it

        # Overall file should have substantial content
        total_polylines = sum(len(layer.polylines) for layer in result.layers)
        total_hatches = sum(len(layer.hatches) for layer in result.layers)
        assert total_polylines > 0 or total_hatches > 0, "File should contain some geometry"

    def test_parse_real_binary_cli_file_performance(self):
        """Test performance of parsing the real binary CLI file."""
        import time

        with open(self.binary_file_path, 'rb') as f:
            binary_data = f.read()

        start_time = time.time()
        result = self.parser.parse(binary_data)
        parse_time = time.time() - start_time

        # Should parse reasonably quickly (less than 5 seconds for large files)
        assert parse_time < 5.0, f"Parsing took {parse_time:.2f} seconds, which seems too slow"

        # Log some statistics
        print(f"\nParsing statistics for Tube_Binary.cli:")
        print(f"  Parse time: {parse_time:.3f} seconds")
        print(f"  File size: {len(binary_data):,} bytes")
        print(f"  Header lines: {len(result.header_lines)}")
        print(f"  Total layers: {len(result.layers)}")
        print(f"  Total polylines: {sum(len(layer.polylines) for layer in result.layers)}")
        print(f"  Total hatches: {sum(len(layer.hatches) for layer in result.layers)}")
        print(f"  Alignment detected: {result.is_aligned}")

    def test_parse_real_binary_cli_round_trip(self):
        """Test that parsing and regenerating produces consistent results."""
        with open(self.binary_file_path, 'rb') as f:
            original_data = f.read()

        # Parse the original file
        parsed = self.parser.parse(original_data)

        # Generate CLI from the parsed data
        regenerated_data = self.parser.generate_cli_from_layer_range(parsed.layers)

        # Parse the regenerated data
        reparsed = self.parser.parse(regenerated_data)

        # Compare structural consistency
        assert len(reparsed.layers) == len(parsed.layers)

        for i, (original_layer, reparsed_layer) in enumerate(zip(parsed.layers, reparsed.layers)):
            assert abs(original_layer.z_height - reparsed_layer.z_height) < 1e-6, f"Layer {i} Z height mismatch"
            assert len(original_layer.polylines) == len(reparsed_layer.polylines), f"Layer {i} polyline count mismatch"
            assert len(original_layer.hatches) == len(reparsed_layer.hatches), f"Layer {i} hatch count mismatch"


class TestFormatDetection:
    """Test suite for format detection functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_ascii_space_detection(self):
        """Test detection of space-delimited ASCII format."""
        cli_content = """$$HEADEREND
$$LAYER 0.0"""

        result = self.parser.parse(cli_content.encode('ascii'))

        # Should be parsed as ASCII, not binary
        assert isinstance(result, ParsedCliFile)
        assert len(result.layers) == 1

    def test_ascii_slash_detection(self):
        """Test detection of slash-delimited ASCII format."""
        cli_content = """$$HEADEREND
$$LAYER/0.0"""

        result = self.parser.parse(cli_content.encode('ascii'))

        # Should be parsed as ASCII, not binary
        assert isinstance(result, ParsedCliFile)
        assert len(result.layers) == 1

    def test_binary_detection(self):
        """Test detection of binary format."""
        # Create a minimal binary CLI file
        binary_data = b"$$HEADEREND" + struct.pack("<H", 127) + struct.pack("<f", 0.0)

        result = self.parser.parse(binary_data)

        # Should be parsed as binary
        assert isinstance(result, ParsedCliFile)
        assert len(result.layers) == 1
