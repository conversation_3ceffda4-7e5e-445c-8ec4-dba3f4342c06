import asyncio
from unittest.mock import AsyncMock, <PERSON>ck

import pytest
import pytest_asyncio

from app.services.job_management.mixins.coordination_mixin import OPCUACoordinationMixin
from app.services.opcua import OPCUAService


class DummyCoordinator(OPCUACoordinationMixin):
    def __init__(self, recoater_client, opcua: OPCUAService, poll_interval: float = 0.0):
        self.recoater_client = recoater_client
        self.opcua = opcua
        self.status_poll_interval = poll_interval
        self.current_job = Mock()


@pytest_asyncio.fixture
async def opcua_service():
    svc = OPCUAService()
    await svc.initialize()
    yield svc
    await svc.shutdown()


@pytest.mark.asyncio
async def test_wait_for_layer_completion_ready(opcua_service: OPCUAService):
    rc = Mock()
    rc.get_state = Mock(return_value={"state": "ready"})
    d = DummyCoordinator(rc, opcua_service, poll_interval=0.0)
    ok = await d.wait_for_layer_completion()
    assert ok is True


@pytest.mark.asyncio
async def test_wait_for_layer_completion_error(opcua_service: OPCUAService):
    rc = Mock()
    rc.get_state = Mock(return_value={"state": "error"})
    d = DummyCoordinator(rc, opcua_service, poll_interval=0.0)
    ok = await d.wait_for_layer_completion()
    assert ok is False


@pytest.mark.asyncio
async def test_wait_for_layer_completion_backend_error(opcua_service: OPCUAService):
    # Set backend_error before entering loop
    await opcua_service.connect()
    await opcua_service.set_backend_error(True)

    rc = Mock()
    rc.get_state = Mock(return_value={"state": "ready"})
    d = DummyCoordinator(rc, opcua_service, poll_interval=0.0)
    ok = await d.wait_for_layer_completion()
    assert ok is False


@pytest.mark.asyncio
async def test_wait_for_layer_completion_plc_error(opcua_service: OPCUAService):
    await opcua_service.connect()
    await opcua_service.set_plc_error(True)

    rc = Mock()
    rc.get_state = Mock(return_value={"state": "ready"})
    d = DummyCoordinator(rc, opcua_service, poll_interval=0.0)
    ok = await d.wait_for_layer_completion()
    assert ok is False


@pytest.mark.asyncio
async def test_wait_for_layer_completion_handles_exception_then_ready(opcua_service: OPCUAService, monkeypatch):
    rc = Mock()
    calls = {"count": 0}

    def flaky_state():
        calls["count"] += 1
        if calls["count"] == 1:
            raise RuntimeError("transient")
        return {"state": "ready"}

    rc.get_state = Mock(side_effect=flaky_state)
    d = DummyCoordinator(rc, opcua_service, poll_interval=0.0)

    # Should retry after exception and return True
    ok = await d.wait_for_layer_completion()
    assert ok is True
    assert calls["count"] >= 2


@pytest.mark.asyncio
async def test_setup_reset_signal_cleanup(opcua_service: OPCUAService):
    rc = Mock()
    d = DummyCoordinator(rc, opcua_service, poll_interval=0.0)

    await d.setup_opcua_job(5)
    assert await opcua_service.read_variable("job_active") is True
    assert await opcua_service.read_variable("total_layers") == 5
    assert await opcua_service.read_variable("current_layer") == 1

    await d.reset_opcua_layer_flags()
    assert await opcua_service.read_variable("recoater_ready_to_print") is False
    assert await opcua_service.read_variable("recoater_layer_complete") is False

    await d.signal_opcua_ready_to_print()
    assert await opcua_service.read_variable("recoater_ready_to_print") is True

    await d.signal_opcua_layer_complete()
    assert await opcua_service.read_variable("recoater_layer_complete") is True

    await d.update_opcua_layer_progress(3)
    assert await opcua_service.read_variable("current_layer") == 3

    await d.cleanup_opcua_job()
    assert await opcua_service.read_variable("job_active") is False

