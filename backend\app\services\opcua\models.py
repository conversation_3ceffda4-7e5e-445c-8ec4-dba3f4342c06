"""
OPC UA Domain Models - Core Business Entities
===========================================

Purpose: Defines the core domain models and value objects for OPC UA operations.
This file contains pure business logic with no external dependencies.

Learning Path for Beginners:
1. Read class docstrings to understand each domain concept
2. Understand relationships and dependencies between models
3. See how these models are used in business logic mixins
4. Explore the main service to see complete integration

Clean Architecture Layer: Domain (Entities and Value Objects)
Dependencies: None (pure domain logic)

Key Domain Concepts:
- ServerConfig: OPC UA server configuration and connection parameters
- CoordinationState: Service connection state (domain-level)
- VariableDefinition: OPC UA variable metadata and type information
"""
from __future__ import annotations

from dataclasses import dataclass

from typing import Any



@dataclass(frozen=True)
class ServerConfig:
    """Immutable server configuration used by the OPC UA service.

    This mirrors values coming from app.config.opcua_config but remains a pure
    domain model (no external imports). The main service converts from the
    external config object at runtime.
    """
    endpoint: str
    server_name: str
    namespace_uri: str
    namespace_idx: int = 2
    security_policy: str = "None"
    security_mode: str = "None"
    certificate_path: str = ""
    private_key_path: str = ""
    connection_timeout: float = 5.0
    session_timeout: float = 60.0
    auto_restart: bool = True
    restart_delay: float = 5.0
    max_restart_attempts: int = 3


@dataclass(frozen=True)
class VariableDefinition:
    """Definition of an OPC UA variable hosted by the server.

    This is a pure description used by business logic to reason about variables.
    """
    name: str
    node_id: str
    data_type: str  # e.g., "Boolean", "Int32", "String", "Float", "Double"
    initial_value: Any
    writable: bool = True
    description: str = ""

