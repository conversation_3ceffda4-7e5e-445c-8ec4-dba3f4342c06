"""
Tests for ASCII CLI Parsing
===========================

This module contains tests for ASCII CLI file parsing, including:
- Space and slash delimited formats
- Inline coordinate format parsing
- Error handling and edge cases related to ASCII parsing
"""

import pytest
from unittest.mock import Mock, patch

from infrastructure.cli_editor.editor import (
    Editor,
    ParsedCliFile,
    CliLayer,
    Polyline,
    Hatch,
    Point,
    CliParsingError
)
from infrastructure.cli_editor.cli_exceptions import CliGenerationError


class TestASCIICliParsing:
    """Test suite for ASCII CLI file parsing."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_space_delimited_basic(self):
        """Test basic space-delimited ASCII CLI parsing."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$POLYLINE 1 0 3
10.0 5.0
15.0 5.0
10.0 10.0
$$LAYER 16.0
$$HATCHES 1 1
0.0 0.0 5.0 5.0"""

        result = self.parser.parse(cli_content.encode('ascii'))

        assert isinstance(result, ParsedCliFile)
        assert len(result.layers) == 2
        assert result.layers[0].z_height == 0.0
        assert result.layers[1].z_height == 16.0
        assert len(result.layers[0].polylines) == 1
        assert len(result.layers[1].hatches) == 1

        # Check polyline details
        polyline = result.layers[0].polylines[0]
        assert polyline.part_id == 1
        assert polyline.direction == 0
        assert len(polyline.points) == 3
        assert polyline.points[0].x == 10.0
        assert polyline.points[0].y == 5.0

    def test_slash_delimited_basic(self):
        """Test basic slash-delimited ASCII CLI parsing."""
        cli_content = """$$HEADERSTART
$$ASCII
$$UNITS/00000000.005000
$$VERSION/200
$$LABEL/1,default
$$DATE/060623
$$HEADEREND
$$GEOMETRYSTART
$$LAYER/0.0
$$LAYER/16.0
$$LAYER/32.0"""

        result = self.parser.parse(cli_content.encode('ascii'))

        assert isinstance(result, ParsedCliFile)
        assert len(result.layers) == 3
        assert result.layers[0].z_height == 0.0
        assert result.layers[1].z_height == 16.0
        assert result.layers[2].z_height == 32.0
        assert len(result.header_lines) == 7  # Updated expected count

    def test_inline_polyline_coordinates(self):
        """Test parsing polylines with inline coordinates."""
        cli_content = """$$HEADEREND
$$LAYER/0.0
$$POLYLINE/1,1,4,10.0,5.0,15.0,5.0,15.0,10.0,10.0,10.0"""

        result = self.parser.parse(cli_content.encode('ascii'))

        assert len(result.layers) == 1
        layer = result.layers[0]
        assert len(layer.polylines) == 1

        polyline = layer.polylines[0]
        assert polyline.part_id == 1
        assert polyline.direction == 1
        assert len(polyline.points) == 4

        expected_points = [(10.0, 5.0), (15.0, 5.0), (15.0, 10.0), (10.0, 10.0)]
        for i, (exp_x, exp_y) in enumerate(expected_points):
            assert polyline.points[i].x == exp_x
            assert polyline.points[i].y == exp_y

    def test_inline_hatch_coordinates(self):
        """Test parsing hatches with inline coordinates."""
        cli_content = """$$HEADEREND
$$LAYER/0.0
$$HATCHES/1,2,0.0,0.0,5.0,5.0,5.0,0.0,0.0,5.0"""

        result = self.parser.parse(cli_content.encode('ascii'))

        assert len(result.layers) == 1
        layer = result.layers[0]
        assert len(layer.hatches) == 1

        hatch = layer.hatches[0]
        assert hatch.group_id == 1
        assert len(hatch.lines) == 2

        # Check first hatch line
        line1 = hatch.lines[0]
        assert line1[0].x == 0.0 and line1[0].y == 0.0
        assert line1[1].x == 5.0 and line1[1].y == 5.0

        # Check second hatch line
        line2 = hatch.lines[1]
        assert line2[0].x == 5.0 and line2[0].y == 0.0
        assert line2[1].x == 0.0 and line2[1].y == 5.0

    def test_large_polyline_inline(self):
        """Test parsing a large polyline with many inline coordinates."""
        # Create a polyline with 50 points
        coords = []
        expected_points = []
        for i in range(50):
            x, y = i * 10.0, i * 5.0
            coords.extend([str(x), str(y)])
            expected_points.append((x, y))

        coord_str = ",".join(coords)
        cli_content = f"""$$HEADEREND
$$LAYER/0.0
$$POLYLINE/1,1,50,{coord_str}"""

        result = self.parser.parse(cli_content.encode('ascii'))

        layer = result.layers[0]
        polyline = layer.polylines[0]

        assert len(polyline.points) == 50
        for i, (exp_x, exp_y) in enumerate(expected_points):
            assert polyline.points[i].x == exp_x
            assert polyline.points[i].y == exp_y

    def test_mixed_coordinate_separators(self):
        """Test parsing with mixed comma and space separators."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$POLYLINE 1 0 2
10.0,5.0
15.0 10.0"""

        result = self.parser.parse(cli_content.encode('ascii'))

        polyline = result.layers[0].polylines[0]
        assert len(polyline.points) == 2
        assert polyline.points[0].x == 10.0
        assert polyline.points[0].y == 5.0
        assert polyline.points[1].x == 15.0
        assert polyline.points[1].y == 10.0

    def test_geometry_start_marker(self):
        """Test that $$GEOMETRYSTART marker is properly handled."""
        cli_content = """$$HEADEREND
$$GEOMETRYSTART
$$LAYER/0.0"""

        result = self.parser.parse(cli_content.encode('ascii'))

        assert len(result.layers) == 1
        assert result.layers[0].z_height == 0.0

    def test_alignment_detection(self):
        """Test detection of alignment in header."""
        cli_content = """$$ALIGN
$$HEADEREND
$$LAYER 0.0"""

        result = self.parser.parse(cli_content.encode('ascii'))

        assert result.is_aligned is True

    def test_empty_lines_ignored(self):
        """Test that empty lines are properly ignored."""
        cli_content = """$$HEADEREND

$$LAYER 0.0

$$POLYLINE 1 0 1

10.0 5.0

"""

        result = self.parser.parse(cli_content.encode('ascii'))

        assert len(result.layers) == 1
        assert len(result.layers[0].polylines) == 1
        assert len(result.layers[0].polylines[0].points) == 1


class TestASCIICliParsingErrors:
    """Test suite for ASCII CLI parsing error cases."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_missing_layer_height(self):
        """Test error handling for missing layer height."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$LAYER"""

        # Add a valid layer first so it's detected as ASCII, then the invalid one
        with pytest.raises(CliParsingError, match="missing Z height"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_polyline_before_layer(self):
        """Test error handling for polyline before layer."""
        cli_content = """$$HEADEREND
$$POLYLINE 1 0 1"""

        with pytest.raises(CliParsingError, match="before any \\$\\$LAYER"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_insufficient_polyline_args(self):
        """Test error handling for insufficient polyline arguments."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$POLYLINE 1 0"""

        with pytest.raises(CliParsingError, match="expected 3 args"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_invalid_polyline_parameters(self):
        """Test error handling for invalid polyline parameters."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$POLYLINE abc def ghi"""

        with pytest.raises(CliParsingError, match="Invalid \\$\\$POLYLINE parameters"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_insufficient_polyline_points(self):
        """Test error handling for insufficient polyline points."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$POLYLINE 1 0 3
10.0 5.0
15.0 10.0"""

        with pytest.raises(CliParsingError, match="Expected 3 points, but only found 2"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_invalid_coordinate_format(self):
        """Test error handling for invalid coordinate format."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$POLYLINE 1 0 1
invalid_coord"""

        with pytest.raises(CliParsingError, match="expected 2 coordinates"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_insufficient_inline_coordinates(self):
        """Test error handling for insufficient inline coordinates."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$POLYLINE/1,1,3,10.0,5.0,15.0"""

        with pytest.raises(CliParsingError, match="Insufficient coordinates in polyline"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_hatches_before_layer(self):
        """Test error handling for hatches before layer."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$HATCHES 1 1"""

        # Add a layer first to trigger ASCII detection, then move hatches before it will cause error during parsing
        cli_content = """$$HEADEREND
$$HATCHES 1 1
$$LAYER 0.0"""

        with pytest.raises(CliParsingError, match="before any \\$\\$LAYER"):
            self.parser.parse(cli_content.encode('ascii'))


class TestHeaderGeometrySplitting:
    """Test suite for the _split_header_geometry method."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_split_basic_header_geometry(self):
        """Test basic header/geometry splitting."""
        text = """$$VERSION 200
$$UNITS 1.0
$$HEADEREND
$$LAYER 0.0
$$POLYLINE 1 0 2
10.0 5.0
15.0 10.0"""

        header_lines, geom_lines = self.parser._split_header_geometry(text)

        assert len(header_lines) == 3
        assert header_lines == ["$$VERSION 200", "$$UNITS 1.0", "$$HEADEREND"]

        assert len(geom_lines) == 4
        assert geom_lines == ["$$LAYER 0.0", "$$POLYLINE 1 0 2", "10.0 5.0", "15.0 10.0"]

    def test_split_with_geometry_start_marker(self):
        """Test splitting when $$GEOMETRYSTART marker is present."""
        text = """$$VERSION 200
$$HEADEREND
$$GEOMETRYSTART
$$LAYER 0.0
$$POLYLINE 1 0 1
10.0 5.0"""

        header_lines, geom_lines = self.parser._split_header_geometry(text)

        assert len(header_lines) == 2
        assert header_lines == ["$$VERSION 200", "$$HEADEREND"]

        # $$GEOMETRYSTART should be filtered out
        assert len(geom_lines) == 3
        assert geom_lines == ["$$LAYER 0.0", "$$POLYLINE 1 0 1", "10.0 5.0"]

    def test_split_empty_lines_filtered(self):
        """Test that empty lines are properly filtered out."""
        text = """$$VERSION 200

$$HEADEREND

$$LAYER 0.0

$$POLYLINE 1 0 1

10.0 5.0

"""

        header_lines, geom_lines = self.parser._split_header_geometry(text)

        assert len(header_lines) == 2
        assert header_lines == ["$$VERSION 200", "$$HEADEREND"]

        assert len(geom_lines) == 3
        assert geom_lines == ["$$LAYER 0.0", "$$POLYLINE 1 0 1", "10.0 5.0"]

    def test_split_only_header(self):
        """Test splitting when only header is present."""
        text = """$$VERSION 200
$$UNITS 1.0
$$HEADEREND"""

        header_lines, geom_lines = self.parser._split_header_geometry(text)

        assert len(header_lines) == 3
        assert len(geom_lines) == 0

    def test_split_whitespace_handling(self):
        """Test proper whitespace handling in splitting."""
        text = """  $$VERSION 200
  $$HEADEREND
  $$LAYER 0.0
  10.0 5.0  """

        header_lines, geom_lines = self.parser._split_header_geometry(text)

        assert header_lines == ["$$VERSION 200", "$$HEADEREND"]
        assert geom_lines == ["$$LAYER 0.0", "10.0 5.0"]


class TestCommandLineParsing:
    """Test suite for the _parse_command_line method."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_parse_space_delimited_commands(self):
        """Test parsing space-delimited commands."""
        # Layer command
        result = self.parser._parse_command_line("$$LAYER 16.0")
        assert result == ["$$LAYER", "16.0"]

        # Polyline command
        result = self.parser._parse_command_line("$$POLYLINE 1 0 3")
        assert result == ["$$POLYLINE", "1", "0", "3"]

        # Hatches command
        result = self.parser._parse_command_line("$$HATCHES 1 2")
        assert result == ["$$HATCHES", "1", "2"]

    def test_parse_slash_delimited_commands(self):
        """Test parsing slash-delimited commands."""
        # Layer command
        result = self.parser._parse_command_line("$$LAYER/16.0")
        assert result == ["$$LAYER", "16.0"]

        # Polyline command with comma-separated parameters
        result = self.parser._parse_command_line("$$POLYLINE/1,0,3")
        assert result == ["$$POLYLINE", "1", "0", "3"]

        # Hatches command with space-separated parameters
        result = self.parser._parse_command_line("$$HATCHES/1 2")
        assert result == ["$$HATCHES", "1", "2"]

    def test_parse_inline_coordinate_commands(self):
        """Test parsing commands with inline coordinates."""
        # Polyline with inline coordinates
        result = self.parser._parse_command_line("$$POLYLINE/1,0,2,10.0,5.0,15.0,10.0")
        expected = ["$$POLYLINE", "1", "0", "2", "10.0", "5.0", "15.0", "10.0"]
        assert result == expected

        # Hatches with inline coordinates
        result = self.parser._parse_command_line("$$HATCHES/1,2,0.0,0.0,5.0,5.0,10.0,10.0,15.0,15.0")
        expected = ["$$HATCHES", "1", "2", "0.0", "0.0", "5.0", "5.0", "10.0", "10.0", "15.0", "15.0"]
        assert result == expected

    def test_parse_mixed_separators(self):
        """Test parsing commands with mixed comma/space separators."""
        result = self.parser._parse_command_line("$$POLYLINE/1,0 3")
        assert result == ["$$POLYLINE", "1", "0", "3"]

        result = self.parser._parse_command_line("$$HATCHES/1 2,0.0,0.0")
        assert result == ["$$HATCHES", "1", "2", "0.0", "0.0"]

    def test_parse_commands_without_slash(self):
        """Test parsing commands that don't use slash format."""
        result = self.parser._parse_command_line("$$GEOMETRYEND")
        assert result == ["$$GEOMETRYEND"]

        result = self.parser._parse_command_line("$$UNKNOWN_COMMAND")
        assert result == ["$$UNKNOWN_COMMAND"]

    def test_parse_case_insensitive_commands(self):
        """Test that command parsing handles case correctly for slash format."""
        result = self.parser._parse_command_line("$$layer/16.0")
        assert result == ["$$LAYER", "16.0"]

        # For space-delimited format, case is preserved
        result = self.parser._parse_command_line("$$Layer 16.0")
        assert result == ["$$Layer", "16.0"]


class TestGeometryCommandsParsing:
    """Test suite for the _parse_geometry_commands method."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_parse_single_layer_with_polyline(self):
        """Test parsing a single layer with one polyline."""
        geom_lines = [
            "$$LAYER 0.0",
            "$$POLYLINE 1 0 2",
            "10.0 5.0",
            "15.0 10.0"
        ]

        layers = self.parser._parse_geometry_commands(geom_lines)

        assert len(layers) == 1
        assert layers[0].z_height == 0.0
        assert len(layers[0].polylines) == 1
        assert layers[0].polylines[0].part_id == 1
        assert layers[0].polylines[0].direction == 0
        assert len(layers[0].polylines[0].points) == 2

    def test_parse_multiple_layers(self):
        """Test parsing multiple layers."""
        geom_lines = [
            "$$LAYER 0.0",
            "$$POLYLINE 1 0 1",
            "10.0 5.0",
            "$$LAYER 16.0",
            "$$HATCHES 1 1",
            "0.0 0.0 5.0 5.0",
            "$$LAYER 32.0"
        ]

        layers = self.parser._parse_geometry_commands(geom_lines)

        assert len(layers) == 3
        assert layers[0].z_height == 0.0
        assert layers[1].z_height == 16.0
        assert layers[2].z_height == 32.0

        assert len(layers[0].polylines) == 1
        assert len(layers[1].hatches) == 1
        assert len(layers[2].polylines) == 0
        assert len(layers[2].hatches) == 0

    def test_parse_inline_format_commands(self):
        """Test parsing inline format commands."""
        geom_lines = [
            "$$LAYER/0.0",
            "$$POLYLINE/1,0,2,10.0,5.0,15.0,10.0",
            "$$HATCHES/1,1,0.0,0.0,5.0,5.0"
        ]

        layers = self.parser._parse_geometry_commands(geom_lines)

        assert len(layers) == 1
        assert len(layers[0].polylines) == 1
        assert len(layers[0].hatches) == 1

        polyline = layers[0].polylines[0]
        assert len(polyline.points) == 2
        assert polyline.points[0].x == 10.0
        assert polyline.points[0].y == 5.0

    def test_parse_geometry_end_command(self):
        """Test that $$GEOMETRYEND command is handled properly."""
        geom_lines = [
            "$$LAYER 0.0",
            "$$POLYLINE 1 0 1",
            "10.0 5.0",
            "$$GEOMETRYEND"
        ]

        layers = self.parser._parse_geometry_commands(geom_lines)

        assert len(layers) == 1
        assert len(layers[0].polylines) == 1

    def test_parse_unrecognized_commands_logged(self):
        """Test that unrecognized commands are logged but don't break parsing."""
        geom_lines = [
            "$$LAYER 0.0",
            "$$UNKNOWN_COMMAND some data",
            "$$POLYLINE 1 0 1",
            "10.0 5.0"
        ]

        with patch.object(self.parser.logger, 'warning') as mock_warning:
            layers = self.parser._parse_geometry_commands(geom_lines)

            # Should log warning for unknown command
            mock_warning.assert_called_once()
            assert "Unrecognized directive" in mock_warning.call_args[0][0]

        # Should still parse valid commands
        assert len(layers) == 1
        assert len(layers[0].polylines) == 1

    def test_parse_empty_geometry_lines(self):
        """Test parsing empty geometry lines."""
        geom_lines = []
        layers = self.parser._parse_geometry_commands(geom_lines)
        assert len(layers) == 0

    def test_parse_with_empty_lines_interspersed(self):
        """Test parsing with empty lines interspersed - should skip empty lines."""
        geom_lines = [
            "$$LAYER 0.0",
            "",
            "$$POLYLINE 1 0 1",
            "10.0 5.0"  # Remove empty line that would cause parsing error
        ]

        layers = self.parser._parse_geometry_commands(geom_lines)

        assert len(layers) == 1
        assert len(layers[0].polylines) == 1


class TestLayerCommandParsing:
    """Test suite for the _parse_layer_command method."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_parse_valid_layer_command(self):
        """Test parsing valid layer commands."""
        # Basic layer
        parts = ["$$LAYER", "16.0"]
        layer = self.parser._parse_layer_command(parts, 0, "$$LAYER 16.0")

        assert layer.z_height == 16.0
        assert len(layer.polylines) == 0
        assert len(layer.hatches) == 0

    def test_parse_layer_with_decimal_height(self):
        """Test parsing layer with decimal height."""
        parts = ["$$LAYER", "25.005"]
        layer = self.parser._parse_layer_command(parts, 0, "$$LAYER 25.005")

        assert layer.z_height == 25.005

    def test_parse_layer_with_negative_height(self):
        """Test parsing layer with negative height."""
        parts = ["$$LAYER", "-10.5"]
        layer = self.parser._parse_layer_command(parts, 0, "$$LAYER -10.5")

        assert layer.z_height == -10.5

    def test_parse_layer_missing_height_error(self):
        """Test error when layer height is missing."""
        parts = ["$$LAYER"]

        with pytest.raises(CliParsingError, match="missing Z height"):
            self.parser._parse_layer_command(parts, 5, "$$LAYER")

    def test_parse_layer_invalid_height_error(self):
        """Test error when layer height is invalid."""
        parts = ["$$LAYER", "invalid_height"]

        with pytest.raises(CliParsingError, match="Invalid Z height"):
            self.parser._parse_layer_command(parts, 10, "$$LAYER invalid_height")


class TestPolylineCommandParsing:
    """Test suite for the _parse_polyline_command method."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_parse_multiline_polyline_command(self):
        """Test parsing multiline polyline command."""
        parts = ["$$POLYLINE", "1", "0", "2"]
        geom_lines = [
            "$$POLYLINE 1 0 2",
            "10.0 5.0",
            "15.0 10.0"
        ]
        current_layer = CliLayer(z_height=0.0, polylines=[], hatches=[])

        next_line = self.parser._parse_polyline_command(parts, 0, "$$POLYLINE 1 0 2", geom_lines, current_layer)

        assert next_line == 3  # Should skip to line after coordinates
        assert len(current_layer.polylines) == 1

        polyline = current_layer.polylines[0]
        assert polyline.part_id == 1
        assert polyline.direction == 0
        assert len(polyline.points) == 2
        assert polyline.points[0].x == 10.0
        assert polyline.points[0].y == 5.0

    def test_parse_inline_polyline_command(self):
        """Test parsing inline polyline command."""
        parts = ["$$POLYLINE", "1", "0", "2", "10.0", "5.0", "15.0", "10.0"]
        geom_lines = ["$$POLYLINE/1,0,2,10.0,5.0,15.0,10.0"]
        current_layer = CliLayer(z_height=0.0, polylines=[], hatches=[])

        next_line = self.parser._parse_polyline_command(parts, 0, geom_lines[0], geom_lines, current_layer)

        assert next_line == 1  # Should move to next line
        assert len(current_layer.polylines) == 1

        polyline = current_layer.polylines[0]
        assert len(polyline.points) == 2
        assert polyline.points[1].x == 15.0
        assert polyline.points[1].y == 10.0

    def test_parse_polyline_before_layer_error(self):
        """Test error when polyline comes before layer."""
        parts = ["$$POLYLINE", "1", "0", "2"]
        geom_lines = ["$$POLYLINE 1 0 2"]

        with pytest.raises(CliParsingError, match="before any \\$\\$LAYER"):
            self.parser._parse_polyline_command(parts, 0, "$$POLYLINE 1 0 2", geom_lines, None)

    def test_parse_polyline_insufficient_args_error(self):
        """Test error when polyline has insufficient arguments."""
        parts = ["$$POLYLINE", "1", "0"]
        geom_lines = ["$$POLYLINE 1 0"]
        current_layer = CliLayer(z_height=0.0, polylines=[], hatches=[])

        with pytest.raises(CliParsingError, match="expected 3 args"):
            self.parser._parse_polyline_command(parts, 0, "$$POLYLINE 1 0", geom_lines, current_layer)

    def test_parse_polyline_invalid_parameters_error(self):
        """Test error when polyline has invalid parameters."""
        parts = ["$$POLYLINE", "abc", "def", "ghi"]
        geom_lines = ["$$POLYLINE abc def ghi"]
        current_layer = CliLayer(z_height=0.0, polylines=[], hatches=[])

        with pytest.raises(CliParsingError, match="Invalid \\$\\$POLYLINE parameters"):
            self.parser._parse_polyline_command(parts, 0, "$$POLYLINE abc def ghi", geom_lines, current_layer)


class TestHatchesCommandParsing:
    """Test suite for the _parse_hatches_command method."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_parse_multiline_hatches_command(self):
        """Test parsing multiline hatches command."""
        parts = ["$$HATCHES", "1", "2"]
        geom_lines = [
            "$$HATCHES 1 2",
            "0.0 0.0 5.0 5.0",
            "10.0 10.0 15.0 15.0"
        ]
        current_layer = CliLayer(z_height=0.0, polylines=[], hatches=[])

        next_line = self.parser._parse_hatches_command(parts, 0, "$$HATCHES 1 2", geom_lines, current_layer)

        assert next_line == 3  # Should skip to line after hatch lines
        assert len(current_layer.hatches) == 1

        hatch = current_layer.hatches[0]
        assert hatch.group_id == 1
        assert len(hatch.lines) == 2

    def test_parse_inline_hatches_command(self):
        """Test parsing inline hatches command."""
        parts = ["$$HATCHES", "1", "1", "0.0", "0.0", "5.0", "5.0"]
        geom_lines = ["$$HATCHES/1,1,0.0,0.0,5.0,5.0"]
        current_layer = CliLayer(z_height=0.0, polylines=[], hatches=[])

        next_line = self.parser._parse_hatches_command(parts, 0, geom_lines[0], geom_lines, current_layer)

        assert next_line == 1  # Should move to next line
        assert len(current_layer.hatches) == 1

        hatch = current_layer.hatches[0]
        assert len(hatch.lines) == 1
        assert hatch.lines[0][0].x == 0.0
        assert hatch.lines[0][1].x == 5.0

    def test_parse_hatches_before_layer_error(self):
        """Test error when hatches comes before layer."""
        parts = ["$$HATCHES", "1", "1"]
        geom_lines = ["$$HATCHES 1 1"]

        with pytest.raises(CliParsingError, match="before any \\$\\$LAYER"):
            self.parser._parse_hatches_command(parts, 0, "$$HATCHES 1 1", geom_lines, None)

    def test_parse_hatches_insufficient_args_error(self):
        """Test error when hatches has insufficient arguments."""
        parts = ["$$HATCHES", "1"]
        geom_lines = ["$$HATCHES 1"]
        current_layer = CliLayer(z_height=0.0, polylines=[], hatches=[])

        with pytest.raises(CliParsingError, match="expected 2 args"):
            self.parser._parse_hatches_command(parts, 0, "$$HATCHES 1", geom_lines, current_layer)
