"""
ASCII CLI Parser Mixin
======================

This module provides the AsciiCliParser mixin for parsing ASCII CLI (Common Layer Interface) files
with support for both space-delimited and slash-delimited formats.

Key Features:
- Parse space-delimited ASCII CLI files ($$LAYER 16.0)
- Parse slash-delimited ASCII CLI files ($$LAYER/16.0)
- Handle inline and multi-line coordinate formats
- Extract header information and geometry data
- Robust error handling with detailed error messages

Dependencies:
- Relies on cli_models for data structures and cli_exceptions for error handling.

Usage:
    class MyParser(AsciiCliParser):
        pass
    parser = MyParser()
    parsed = parser.parse_ascii(ascii_text)
"""
import logging
from typing import List, Optional, Tuple

from .cli_models import Point, Polyline, Hatch, CliLayer, ParsedCliFile
from .cli_exceptions import CliParsingError


class AsciiCliParser:
    """Mixin for ASCII CLI files with support for space and slash delimited formats."""

    @property
    def logger(self):
        """Get logger from parent class or create a default one."""
        if hasattr(self, '_logger') and self._logger:
            return self._logger
        return logging.getLogger(__name__)

    def parse_ascii(self, text: str) -> ParsedCliFile:
        """Parse ASCII CLI file with support for both space-delimited and slash-delimited formats.
        
        The parsing is split into two phases:
        1. Split header from geometry based on $$HEADEREND marker
        2. Parse geometry commands ($$LAYER, $$POLYLINE, $$HATCHES)
        """
        header_lines, geom_lines = self._split_header_geometry(text)
        is_aligned = any("$$ALIGN" in h.upper() for h in header_lines)
        
        layers = self._parse_geometry_commands(geom_lines)
        
        return ParsedCliFile(header_lines=header_lines, is_aligned=is_aligned, layers=layers)

    def _split_header_geometry(self, text: str) -> Tuple[List[str], List[str]]:
        """Splits text into header and geometry segments. Switches appending when HEADEREND detected"""
        lines = [l.strip() for l in text.splitlines() if l.strip()]
        header_lines: List[str] = []
        geom_lines: List[str] = []
        seen_header_end = False

        # Split lines into header vs geometry segments. Switch over when HEADEREND detected
        for l in lines:
            if not seen_header_end:
                header_lines.append(l)
                if l.upper() == "$$HEADEREND":
                    seen_header_end = True
                continue
            # Skip $$GEOMETRYSTART if present
            if l.upper() == "$$GEOMETRYSTART":
                continue
            geom_lines.append(l)

        return header_lines, geom_lines
    
    def _parse_geometry_commands(self, geom_lines: List[str]) -> List[CliLayer]:
        """Parse geometry commands from the geometry section of a CLI file."""
        layers: List[CliLayer] = []
        current_layer: Optional[CliLayer] = None
        i = 0

        while i < len(geom_lines):
            line = geom_lines[i]
            parts = self._parse_command_line(line)
            
            if not parts:  # Skip empty lines
                i += 1
                continue
                
            cmd = parts[0].upper()
            
            try:
                if cmd == "$$LAYER":
                    current_layer = self._parse_layer_command(parts, i, line)
                    layers.append(current_layer)
                    i += 1

                elif cmd == "$$POLYLINE":
                    i = self._parse_polyline_command(parts, i, line, geom_lines, current_layer)

                elif cmd == "$$HATCHES":
                    i = self._parse_hatches_command(parts, i, line, geom_lines, current_layer)

                elif cmd == "$$GEOMETRYEND":
                    # End of geometry section, ignore
                    i += 1

                else:
                    # Unrecognized directive, skip with warning
                    self.logger.warning(f"Unrecognized directive at line {i}: '{line}' - skipping")
                    i += 1
                    
            except CliParsingError:
                raise  # Re-raise CLI parsing errors
            except Exception as e:
                raise CliParsingError(f"Unexpected error parsing line {i}: '{line}' - {e}")

        return layers
    
    def _parse_command_line(self, line: str) -> List[str]:
        """Parse a command line that can be either space or slash delimited."""
        if '/' in line and line.startswith('$$'):
            # Slash-delimited format: $$LAYER/16.0
            parts = line.split('/', 1)
            if len(parts) == 2:
                cmd = parts[0].upper()
                param_str = parts[1]
                # For commands that need multiple parameters, split by comma or space
                if cmd in ["$$POLYLINE", "$$HATCHES"]:
                    params = param_str.replace(',', ' ').split()
                    return [cmd] + params
                else:
                    return [cmd, param_str]
            else:
                return [line.upper()]
        else:
            # Space-delimited format: $$LAYER 16.0
            return line.split()
    
    def _parse_layer_command(self, parts: List[str], line_idx: int, line: str) -> CliLayer:
        """Parse a $$LAYER command and return a new CliLayer."""
        if len(parts) < 2:
            raise CliParsingError(f"Invalid $$LAYER directive at line {line_idx}: '{line}' - missing Z height")
        
        try:
            z = float(parts[1])
            return CliLayer(z_height=z, polylines=[], hatches=[])
        except ValueError as e:
            raise CliParsingError(f"Invalid Z height in $$LAYER at line {line_idx}: '{line}' - {e}")
    
    def _parse_polyline_command(self, parts: List[str], line_idx: int, line: str, 
                               geom_lines: List[str], current_layer: Optional[CliLayer]) -> int:
        """Parse a $$POLYLINE command and add it to the current layer. Returns next line index."""
        if not current_layer:
            raise CliParsingError(f"Found $$POLYLINE at line {line_idx} before any $$LAYER")
        if len(parts) < 4:
            raise CliParsingError(f"Invalid $$POLYLINE directive at line {line_idx}: '{line}' - expected 3 args")
        
        try:
            pid, dr, cnt = int(parts[1]), int(parts[2]), int(parts[3])
            
            if len(parts) > 4:
                # Inline format: all coordinates on same line
                points = self._parse_inline_coordinates(parts[4:], cnt, line_idx, "polyline")
                current_layer.polylines.append(Polyline(part_id=pid, direction=dr, points=points))
                return line_idx + 1
            else:
                # Multi-line format: coordinates on separate lines
                points = self._parse_multiline_coordinates(geom_lines, line_idx, cnt, 2, "polyline")
                current_layer.polylines.append(Polyline(part_id=pid, direction=dr, points=points))
                return line_idx + 1 + cnt
                
        except ValueError as e:
            raise CliParsingError(f"Invalid $$POLYLINE parameters at line {line_idx}: '{line}' - {e}")
    
    def _parse_hatches_command(self, parts: List[str], line_idx: int, line: str,
                              geom_lines: List[str], current_layer: Optional[CliLayer]) -> int:
        """Parse a $$HATCHES command and add it to the current layer. Returns next line index."""
        if not current_layer:
            raise CliParsingError(f"Found $$HATCHES at line {line_idx} before any $$LAYER")
        if len(parts) < 3:
            raise CliParsingError(f"Invalid $$HATCHES directive at line {line_idx}: '{line}' - expected 2 args")
        
        try:
            grp, num = int(parts[1]), int(parts[2])
            
            if len(parts) > 3:
                # Inline format: all coordinates on same line
                coord_parts = parts[3:]
                if len(coord_parts) < num * 4:
                    raise CliParsingError(f"Insufficient coordinates in hatches at line {line_idx}: expected {num * 4} coordinates, got {len(coord_parts)}")
                
                lines_list: List[Tuple[Point, Point]] = []
                for coord_idx in range(0, num * 4, 4):
                    try:
                        x1, y1, x2, y2 = map(float, coord_parts[coord_idx:coord_idx + 4])
                        lines_list.append((Point(x=x1, y=y1), Point(x=x2, y=y2)))
                    except (ValueError, IndexError) as e:
                        raise CliParsingError(f"Invalid coordinates in hatches at line {line_idx}: {e}")
                
                current_layer.hatches.append(Hatch(group_id=grp, lines=lines_list))
                return line_idx + 1
            else:
                # Multi-line format: coordinates on separate lines
                lines_list: List[Tuple[Point, Point]] = []
                for j in range(1, num + 1):
                    hatch_line_idx = line_idx + j
                    if hatch_line_idx >= len(geom_lines):
                        raise CliParsingError(f"Unexpected EOF in hatches at line {line_idx}. Expected {num} hatch lines, but only found {j-1}.")
                    
                    hatch_line = geom_lines[hatch_line_idx]
                    coords = hatch_line.replace(',', ' ').split()
                    if len(coords) < 4:
                        raise CliParsingError(f"Bad hatch line at line {hatch_line_idx}: '{hatch_line}' - expected X1 Y1 X2 Y2 coordinates")
                    
                    try:
                        x1, y1, x2, y2 = map(float, coords[:4])
                        lines_list.append((Point(x=x1, y=y1), Point(x=x2, y=y2)))
                    except ValueError as e:
                        raise CliParsingError(f"Invalid hatch coordinates at line {hatch_line_idx}: '{hatch_line}' - {e}")
                
                current_layer.hatches.append(Hatch(group_id=grp, lines=lines_list))
                return line_idx + 1 + num
                
        except ValueError as e:
            raise CliParsingError(f"Invalid $$HATCHES parameters at line {line_idx}: '{line}' - {e}")
    
    def _parse_inline_coordinates(self, coord_parts: List[str], count: int, line_idx: int, 
                                 coord_type: str) -> List[Point]:
        """Parse coordinates that are all on the same line as the command."""
        expected_coords = count * 2
        if len(coord_parts) < expected_coords:
            raise CliParsingError(f"Insufficient coordinates in {coord_type} at line {line_idx}: expected {expected_coords} coordinates, got {len(coord_parts)}")
        
        points: List[Point] = []
        for coord_idx in range(0, expected_coords, 2):
            try:
                x = float(coord_parts[coord_idx])
                y = float(coord_parts[coord_idx + 1])
                points.append(Point(x=x, y=y))
            except (ValueError, IndexError) as e:
                raise CliParsingError(f"Invalid coordinates in {coord_type} at line {line_idx}: {e}")
        
        return points
    
    def _parse_multiline_coordinates(self, geom_lines: List[str], start_line_idx: int, 
                                   count: int, coords_per_point: int, coord_type: str) -> List[Point]:
        """Parse coordinates that are on separate lines after the command."""
        points: List[Point] = []
        
        for j in range(1, count + 1):
            point_line_idx = start_line_idx + j
            if point_line_idx >= len(geom_lines):
                missing = count - (j - 1)
                raise CliParsingError(f"Unexpected EOF in {coord_type} points at line {start_line_idx}. Expected {count} points, but only found {j-1} points. Missing {missing} points.")
            
            point_line = geom_lines[point_line_idx]
            xy = point_line.replace(',', ' ').split()
            if len(xy) < coords_per_point:
                raise CliParsingError(f"Bad {coord_type} point at line {point_line_idx}: '{point_line}' - expected {coords_per_point} coordinates")
            
            try:
                x, y = float(xy[0]), float(xy[1])
                points.append(Point(x=x, y=y))
            except ValueError as e:
                raise CliParsingError(f"Invalid coordinates at line {point_line_idx}: '{point_line}' - {e}")
        
        return points