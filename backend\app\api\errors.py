"""
Errors API Endpoints
====================

Provides RESTful endpoints for clearing system error flags (backend_error, plc_error)
following the unified 7-variable OPC UA architecture. This offers an operator-facing
API that is decoupled from print job routes.
"""
import logging
from fastapi import APIRouter, Depends, HTTPException

from app.dependencies import get_multilayer_job_manager
from app.services.job_management import MultiMaterialJobService
from app.api.print.models import MultiMaterialJobResponse

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/errors/clear", response_model=MultiMaterialJobResponse)
async def clear_errors(
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
) -> MultiMaterialJobResponse:
    """Clear all error flags for operator error recovery.

    Uses the unified service method `clear_all_error_flags()` to clear both job-level
    and OPC UA error flags (backend_error, plc_error) in a consistent manner.
    """
    try:
        cleared = await job_manager.clear_all_error_flags()
        if cleared:
            return MultiMaterialJobResponse(success=True, message="All error flags cleared")
        else:
            return MultiMaterialJobResponse(success=False, message="Failed to clear one or more error flags")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error clearing errors: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")

