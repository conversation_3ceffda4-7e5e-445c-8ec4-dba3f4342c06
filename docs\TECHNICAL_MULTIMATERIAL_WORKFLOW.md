# 3‑Drum Multi‑Material Printing: End‑to‑End Technical Workflow

This document describes the complete data/control flow of our 3‑drum Aerosint recoater system, from operator UX to hardware control, including key classes, functions, and OPC UA coordination. It reflects the CURRENT system and supersedes partial legacy behaviors. Use this as the single source of truth for the multi‑material layer‑by‑layer workflow.

Related user‑facing workflow overview: docs/MULTIMATERIAL_PRINT_WORKFLOW.md


## System Overview

High‑level architecture

```
Operator (Frontend Vue)
   │   drum‑specific uploads (.cli)
   ▼
FastAPI REST (backend/app/api/print)
   │   /cli/upload/{drum_id}
   │   /cli/start-multimaterial-job
   │   /multimaterial-job/status | /cancel | /clear-error
   ▼
MultiMaterialJobService (backend/app/services/job_management/multimaterial_job_service.py)
   ├─ Caching per drum (0..2)
   ├─ Layer‑by‑layer upload + print orchestration
   ├─ Uses: CLI Editor (parse/generate ASCII CLI)
   ├─ Uses: RecoaterClient (upload_cli_data, start_print_job)
   └─ Uses: OPCUAService (7‑var coordination)

Infrastructure
   ├─ CLI Editor (infrastructure/cli_editor/editor.py)
   ├─ RecoaterClient (infrastructure/recoater_client.py or mock)
   └─ OPCUAService (backend/app/services/opcua)
```

Core coordination variables (OPC UA) are hosted by our in‑process OPC UA service and consumed by the PLC:
- job_active, total_layers, current_layer
- recoater_ready_to_print, recoater_layer_complete
- backend_error, plc_error


## 1) System Startup

Initialization happens via dependency bootstrapping in backend/app/dependencies.py
- initialize_recoater_client() → sets global RecoaterClient (real or mock)
- initialize_multilayer_job_manager() → creates MultiMaterialJobService and exposes it via get_multilayer_job_manager()
- initialize_opcua_service() → starts/ connects OPCUAService and readies the 7‑var store

Key functions
- backend/app/dependencies.py
  - initialize_recoater_client()
  - async initialize_opcua_service()
  - initialize_multilayer_job_manager() (preferred alias: initialize_multimaterial_job_service())
  - get_multilayer_job_manager(), get_recoater_client(), get_opcua_service()

OPC UA service
- backend/app/services/opcua/opcua_service.py: class OPCUAService
- backend/app/services/opcua/mixins/*: server/coordination/monitoring mixins
- backend/app/config/opcua_config.py: COORDINATION_VARIABLES definitions

ASCII startup relationship
```
App Lifespan → dependencies.initialize_* →
  ├─ RecoaterClient (global)
  ├─ MultiMaterialJobService (global alias: multilayer_job_manager)
  └─ OPCUAService (connect + start in‑proc store)
```


## Configuration (JobConfig)

Job orchestration tunables are centralized in app/config/job_config.py and loaded from backend/.env. These names are explicit and include units for non-developer engineers:
- JOB_DRUM_UPLOAD_DELAY_SECONDS: Delay between uploading the same layer to each drum (seconds)
- JOB_STATUS_POLL_INTERVAL_SECONDS: Interval between status polling cycles during a layer print (seconds)
- JOB_READY_TIMEOUT_SECONDS: Max time to wait for recoater "ready-to-print" per layer (seconds)
- JOB_COMPLETION_TIMEOUT_SECONDS: Max time to wait for layer completion (seconds)
- JOB_EMPTY_LAYER_TEMPLATE_PATH: Absolute path to the blank ASCII CLI template for drums without data

Default template path: c:\Users\<USER>\Downloads\SIMTech_Internship\RecoaterSearch\APIRecoater_Ethernet\backend\app\templates\empty_layer.cli

OPC UA service is injected into MultiMaterialJobService via dependencies.initialize_multilayer_job_manager(), improving testability and decoupling.


## 2) Operator Configuration (pre‑print)

Operator prepares per‑drum CLI files and any UI settings. Blade/offset/speed/etc. are set via dedicated API/UI as applicable (see LayerParameters* models if used). All multi‑material geometry must be ASCII CLI.

Constraints
- 3 drums max (IDs 0, 1, 2). UI and API validate this range.
- Only ASCII .cli files are accepted.


## 3) CLI File Upload Process (per drum)

Endpoint and flow
- POST /api/v1/print/cli/upload/{drum_id}
  - File validation: .cli extension, non‑empty
  - Parse: infrastructure/cli_editor/editor.Editor.parse(bytes) → ParsedCliFile
  - Cache per drum: MultiMaterialJobService.cache_cli_file_for_drum(drum_id, parsed, filename)
  - Response: total_layers, filename, simple file_id "drum_{drum_id}" for UI traceability

Main code paths
- backend/app/api/print/cli.py
  - upload_cli_file_to_drum(drum_id: int, file: UploadFile, job_manager: MultiMaterialJobService)
- backend/app/services/job_management/multimaterial_job_service.py
  - cache_cli_file_for_drum(drum_id: int, parsed_file: ParsedCliFile, filename: str)
  - get_cached_file_for_drum(drum_id: int) → Optional[dict]
  - has_cached_files() → bool
  - get_max_layers() → int

Different layer counts scenario
- Drums may have unequal layer counts. The service computes max_layers across cached drums and automatically uses an empty_layer.cli for missing layers when iterating.


## 4) Job Initiation (Start Print)

Endpoint
- POST /api/v1/print/cli/start-multimaterial-job
  - No request body. Job uses previously cached per‑drum files only.
  - Validates presence of at least one cached drum.
  - Starts the layer‑by‑layer orchestration.

Main code paths
- backend/app/api/print/multimaterial.py
  - start_multimaterial_job(job_manager: MultiMaterialJobService)
- backend/app/services/job_management/multimaterial_job_service.py
  - start_layer_by_layer_job() → bool

Error handling
- 400 if no cached drum files
- 500 if orchestration cannot be started


## 5) MultiMaterial Job Service Execution (deep dive)

Primary class
- backend/app/services/job_management/multimaterial_job_service.py: class MultiMaterialJobService
  - Composition: JobLifecycleMixin, LayerOperationsMixin, CoordinationMixin
  - External deps: RecoaterClient, CLI Editor, OPCUAService

Key methods and data
- cache_cli_file_for_drum(drum_id, parsed_file, filename)
- start_layer_by_layer_job() → bool
  - Prepares MultiMaterialJobState(total_layers=max_layers, file_ids={drum→"drum_{drum}_cache"})
  - OPCUA: set_job_active(total_layers), update_layer_progress(1)
  - Calls _process_all_layers()
- _process_all_layers()
  - for each layer_index in [0..max_layers-1]: _process_layer(layer_index)
  - marks job completed
- _process_layer(layer_index)
  - For drum in 0..2:
    - If drum has layer_index: generate_single_layer_ascii_cli(layer, header)
    - Else: read app/templates/empty_layer.cli
    - Upload via RecoaterClient.upload_cli_data(drum_id, cli_bytes)
    - 2s delay between drums (configurable, default 2.0s)
  - Start Aerosint layer print: RecoaterClient.start_print_job()
  - Wait for completion: _wait_for_layer_completion() (poll, 2s interval; see mixins)

Supporting mixins of note
- backend/app/services/job_management/mixins/layer_operations_mixin.py
  - _wait_for_layer_completion(): polls recoater until printing→ready or timeout
- backend/app/services/job_management/mixins/job_lifecycle_mixin.py
  - MultiMaterialJobState management, cancel, status getters

Error handling outline
- Any exception during processing raises MultiMaterialJobError; job is cleared and OPC UA set inactive in finally block.
- RecoaterClient errors bubble up as job errors; operator must resolve and retry (UI displays persistent error modal per product spec).


## 6) Layer‑by‑Layer Processing and OPC UA Integration

OPC UA service and variables
- backend/app/services/opcua/opcua_service.py: OPCUAService
- backend/app/services/opcua/mixins/coordination_mixin.py: set_job_active(), set_job_inactive(), update_layer_progress(), set_recoater_ready_to_print(), set_recoater_layer_complete(), set_backend_error(), set_plc_error(), clear_error_flags()
- backend/app/config/opcua_config.py: COORDINATION_VARIABLES

7 variables used for coordination
- job_active: bool (backend writes TRUE at start, FALSE at end)
- total_layers: int (backend sets once at job start)
- current_layer: int (backend updates per layer)
- recoater_ready_to_print: bool (backend toggles around ready/print states)
- recoater_layer_complete: bool (backend writes TRUE when deposition complete)
- backend_error: bool (backend sets TRUE on any backend error)
- plc_error: bool (PLC sets TRUE on any PLC error)

Sequence (one layer, authoritative)
- Reset flags: opcua.set_recoater_ready_to_print(False); opcua.set_recoater_layer_complete(False)
- Upload ASCII CLI for each drum (missing → empty_layer.cli), stagger uploads by 2s
- Set ready: opcua.set_recoater_ready_to_print(True)
- Start print: recoater.start_print_job()
- Poll for completion:
  - If opcua.get_backend_error() or opcua.get_plc_error(): set backend error state and return False
  - Else read recoater_client.get_state(): return True if 'ready', return False if 'error'
  - Else sleep for status_poll_interval (default 2s) and retry
- Mark complete: opcua.set_recoater_layer_complete(True)
- Advance progress: opcua.update_layer_progress(layer_index + 2)
- Prepare next layer: opcua.set_recoater_ready_to_print(False); opcua.set_recoater_layer_complete(False)

Job start/finish signals
- Start: opcua.set_job_active(total_layers), opcua.update_layer_progress(1)
- End: opcua.set_job_inactive() (also clears ready/complete/error flags to defaults)

Polling cadence and timeouts
- Upload delay between drums: 2.0s
- Status poll interval: 2.0s
- Ready timeout: 30.0s; Completion timeout: 300.0s (see MultiMaterialJobService defaults)


### OPC UA Variable Updates in MultiMaterialJobService

Purpose and usage of the 7 shared variables:
- job_active (bool): TRUE during job, FALSE when ended/failed.
- total_layers (int): Total layers for the current job.
- current_layer (int): 1-based progress through the job.
- recoater_ready_to_print (bool): TRUE when all uploads complete and recoater is ready to start printing the layer.
- recoater_layer_complete (bool): TRUE when layer deposition is complete.
- backend_error (bool): TRUE when backend encounters an error.
- plc_error (bool): TRUE when PLC reports an error (monitored by backend).

Service behavior (layer-by-layer path):
- On start: set_job_active(total_layers), update_layer_progress(1)
- For each layer k (0-based index):
  - Reset flags: set_recoater_ready_to_print(False), set_recoater_layer_complete(False)
  - Upload CLI to each active drum (staggered by 2s)
  - Set recoater ready: set_recoater_ready_to_print(True)
  - Start print: recoater_client.start_print_job()
  - Wait completion: _wait_for_layer_completion() monitors backend_error/plc_error and timeouts
  - Mark complete: set_recoater_layer_complete(True)
  - Update progress: update_layer_progress(k + 2)
- On error: set_backend_error(True); end-of-job finally: set_job_inactive() clears ready/complete/error flags.

Available API (OPCUAService): set_job_active, set_job_inactive, update_layer_progress, set_recoater_ready_to_print, set_recoater_layer_complete, set_backend_error, set_plc_error, clear_error_flags, write_variable, read_variable.


### Sequence diagram: Layer-by-layer print flow (Mermaid)

```mermaid
sequenceDiagram
    autonumber
    participant UI as Operator (Frontend)
    participant API as Print API (FastAPI)
    participant JM as MultiMaterialJobService (job_manager)
    participant OPC as OPCUAService
    participant HW as RecoaterClient (Aerosint)

    UI->>API: POST /cli/upload/{drum_id} (x up to 3)
    API->>JM: cache_cli_file_for_drum(drum_id, parsed)
    note over API,JM: Repeat per drum 0..2 as files are uploaded

    UI->>API: POST /cli/start-multimaterial-job
    API->>JM: start_layer_by_layer_job() [background]
    JM->>OPC: set_job_active(total_layers)
    JM->>OPC: update_layer_progress(1)

    JM->>JM: _process_all_layers()
    loop for layer_index in range(max_layers)
        JM->>JM: _process_layer(layer_index)
        JM->>OPC: set_recoater_ready_to_print(False)
        JM->>OPC: set_recoater_layer_complete(False)
        par Upload drum 0
            JM->>HW: upload_cli_data(0, bytes)
        and Upload drum 1
            JM->>HW: upload_cli_data(1, bytes)
        and Upload drum 2
            JM->>HW: upload_cli_data(2, bytes)
        end
        JM->>OPC: set_recoater_ready_to_print(True)
        JM->>HW: start_print_job()
        JM->>JM: _wait_for_layer_completion()
        alt No errors
            JM->>OPC: set_recoater_layer_complete(True)
            JM->>OPC: update_layer_progress(next#59; capped to total_layers)
        else Error detected
            break Abort on error
                JM->>OPC: set_backend_error(True)
                JM->>OPC: set_job_inactive()
                API-->>UI: status reflects error (persistent modal)
            end
        end
    end

    JM->>OPC: set_job_inactive()
    API-->>UI: polling/WS shows job completed

```


## Data Flow References (files, methods)

Frontend → Backend
- POST /print/cli/upload/{drum_id} → cli.py: upload_cli_file_to_drum()
  - Editor.parse() → ParsedCliFile → MultiMaterialJobService.cache_cli_file_for_drum()
- POST /print/cli/start-multimaterial-job → multimaterial.py: start_multimaterial_job()
  - MultiMaterialJobService.start_layer_by_layer_job()

Backend → Recoater
- MultiMaterialJobService._process_layer():
  - Editor.generate_single_layer_ascii_cli() or empty_layer.cli
  - RecoaterClient.upload_cli_data(drum_id, bytes)
  - RecoaterClient.start_print_job()

Backend ↔ PLC (via OPC UA)
- OPCUAService (write/read): job_active, total_layers, current_layer, recoater_ready_to_print, recoater_layer_complete, backend_error, plc_error


## Error Handling and Operator Recovery

- Backend errors set backend_error TRUE; PLC errors set plc_error TRUE. UI displays persistent error modal until operator acknowledges and uses /multimaterial-job/clear-error.
- The entire operation pauses during error; after recovery, operator can restart the job.

Endpoints
- POST /api/v1/print/multimaterial-job/clear-error → clears backend_error & plc_error
- GET  /api/v1/print/multimaterial-job/status → aggregated status for dashboard
- GET  /api/v1/print/cli/drum-cache-status → per‑drum cached file info and max_layers


## Diagrams (compact ASCII)

Architecture
```
[Vue UI] → [FastAPI Routers]
              │
              ├─ /cli/upload/{drum}
              ├─ /cli/start-multimaterial-job
              └─ /multimaterial-job/*
              │
          [MultiMaterialJobService]
              ├─ CLI Editor (parse/generate)
              ├─ RecoaterClient (upload/start)
              └─ OPCUAService (7 vars)
```

Class relations (key)
```
MultiMaterialJobService
  ├─ cache_cli_file_for_drum()
  ├─ start_layer_by_layer_job()
  ├─ _process_all_layers() → _process_layer()
  └─ uses: opcua_service, recoater_client, cli_parser
```

Sequence (upload + one layer)
```
UI: POST /cli/upload/{drum} (3 files)
API: parse → cache per drum
UI: POST /cli/start-multimaterial-job

Note on deprecated adapters
- The optional_adapters (JobLifecycle, LayerCoordinator, StatusMonitor, FileHandler) package has been removed. Use MultiMaterialJobService directly.

SVC: set_job_active → loop layers → for each drum upload → start_print_job → wait complete → progress++
SVC: set_job_inactive
```


## Cleanup and Backward Compatibility

Production workflow is strictly drum‑specific. However, for preview/testing workflows and to maintain backward compatibility with existing tests:
- Preview upload endpoint exists: POST /api/v1/print/cli/upload (non‑drum‑specific) — caches a parsed file under a transient file_id for preview/inspection.
- Preview send endpoints exist: POST /api/v1/print/cli/{file_id}/layer/{layer_num}/send/{drum_id} and POST /api/v1/print/cli/{file_id}/layers/send/{drum_id} — send single/range layers from preview cache to a drum.
- Start‑job still uses only cached per‑drum files from POST /api/v1/print/cli/upload/{drum_id}.

Recommendation: Use drum‑specific uploads for production printing. Use preview endpoints only for visualization/testing.


## Test Asset

The 3MSpiral_2.cli test file can be found at either of these paths from repo root:
- tests/3MSpiral_2.cli
- backend/tests/3MSpiral_2.cli

Use it to validate parsing and single‑layer ASCII generation (see existing tests for examples).
