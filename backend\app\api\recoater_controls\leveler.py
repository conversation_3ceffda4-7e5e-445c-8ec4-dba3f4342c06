"""
Leveler control endpoints:
- GET /leveler/pressure: Get leveler pressure.
- PUT /leveler/pressure: Set leveler pressure.
- GET /leveler/sensor: Get leveler sensor state.
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import logging

from infrastructure.recoater_client import RecoaterClient, RecoaterConnectionError, RecoaterAPIError
from app.dependencies import get_recoater_client
from .models import LevelerPressureRequest

logger = logging.getLogger(__name__)

router = APIRouter(tags=["recoater"])

# Leveler Control Endpoints

@router.get("/leveler/pressure")
async def get_leveler_pressure(
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the leveler pressure information.

    Returns:
        Dictionary containing leveler pressure information (maximum, target, value)

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info("Getting leveler pressure")
        pressure_data = client.get_leveler_pressure()

        response = {
            "leveler_pressure": pressure_data,
            "connected": True
        }

        logger.debug(f"Leveler pressure retrieved successfully: {response}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting leveler pressure: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting leveler pressure: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error getting leveler pressure: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/leveler/pressure")
async def set_leveler_pressure(
    pressure_request: LevelerPressureRequest,
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Set the target pressure for the leveler.

    Args:
        pressure_request: Leveler pressure parameters (target)

    Returns:
        Dictionary containing leveler pressure response

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Setting leveler pressure: {pressure_request}")

        response_data = client.set_leveler_pressure(
            target=pressure_request.target
        )

        response = {
            "leveler_command": pressure_request.model_dump(),
            "response": response_data,
            "connected": True
        }

        logger.info("Leveler pressure set successfully")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting leveler pressure: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error setting leveler pressure: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error setting leveler pressure: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/leveler/sensor")
async def get_leveler_sensor(
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the current state of the magnetic sensor on the leveler.

    Returns:
        Dictionary containing leveler sensor state

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info("Getting leveler sensor state")
        sensor_data = client.get_leveler_sensor()

        response = {
            "leveler_sensor": sensor_data,
            "connected": True
        }

        logger.debug(f"Leveler sensor state retrieved successfully: {response}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting leveler sensor: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting leveler sensor: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error getting leveler sensor: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
