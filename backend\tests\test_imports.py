"""
Import Tests
============

Comprehensive tests to ensure all modules can be imported without errors.
This helps catch circular dependencies, missing imports, and other import-related issues.
"""

import pytest
import sys
import os
import importlib
import pkgutil
from pathlib import Path

class TestImports:
    """Test all module imports to catch circular dependencies and import errors."""

    def test_main_application_import(self):
        """Test that the main FastAPI application can be imported."""
        try:
            from app.main import app
            assert app is not None
        except Exception as e:
            pytest.fail(f"Failed to import main application: {e}")

    def test_core_dependencies_import(self):
        """Test that core dependency modules can be imported."""
        modules_to_test = [
            'app.dependencies',
            'app.config.opcua_config',
            'app.models.multilayer_job',
        ]
        
        for module_name in modules_to_test:
            try:
                importlib.import_module(module_name)
            except Exception as e:
                pytest.fail(f"Failed to import {module_name}: {e}")

    def test_services_import(self):
        """Test that all service modules can be imported."""
        services_to_test = [
            'app.services.communication.websocket_manager',
            'app.services.job_management.multimaterial_job_service',
            'app.services.monitoring.data_gatherer',
            'app.services.monitoring.status_poller',
            'app.services.opcua.opcua_service',
        ]

        for service_name in services_to_test:
            try:
                importlib.import_module(service_name)
            except Exception as e:
                pytest.fail(f"Failed to import service {service_name}: {e}")

    def test_api_routers_import(self):
        """Test that all API router modules can be imported."""
        api_modules = [
            'app.api.status',
            'app.api.recoater_controls',
            'app.api.print',
            'app.api.configuration',
        ]
        
        for api_module in api_modules:
            try:
                importlib.import_module(api_module)
            except Exception as e:
                pytest.fail(f"Failed to import API module {api_module}: {e}")

    def test_infrastructure_import(self):
        """Test that infrastructure modules can be imported."""
        infrastructure_modules = [
            'infrastructure.recoater_client',
            'infrastructure.mock_recoater_client',
            'infrastructure.cli_editor.editor',
            'infrastructure.cli_editor.ascii_cli_parser',
            'infrastructure.cli_editor.binary_cli_parser',
            'infrastructure.cli_editor.cli_generator',
        ]
        
        for module_name in infrastructure_modules:
            try:
                importlib.import_module(module_name)
            except Exception as e:
                pytest.fail(f"Failed to import infrastructure module {module_name}: {e}")

    def test_services_package_imports(self):
        """Test that services package exports work correctly."""
        try:
            from app.services import (
                WebSocketConnectionManager,
                RecoaterDataGatherer,
                StatusPollingService,
                OPCUAService,
            )
            # Unified service should be importable from the package
            from app.services.job_management import MultiMaterialJobService

            # Verify all imports are not None
            assert WebSocketConnectionManager is not None
            assert MultiMaterialJobService is not None
            assert RecoaterDataGatherer is not None
            assert StatusPollingService is not None
            assert OPCUAService is not None

        except Exception as e:
            pytest.fail(f"Failed to import from services package: {e}")

    def test_circular_dependency_detection(self):
        """Test for circular dependencies by importing key service combinations."""
        try:
            # Test combinations that could cause circular dependencies
            from app.services.job_management import MultiMaterialJobService
            from app.services.opcua import OPCUAService
            from app.services.monitoring import RecoaterDataGatherer
            from app.services.communication import WebSocketConnectionManager

            # If we get here without exceptions, no circular dependencies detected
            assert True
            
        except Exception as e:
            pytest.fail(f"Circular dependency detected: {e}")

    def test_websocket_handler_import(self):
        """Test that WebSocket handler can be imported and instantiated."""
        try:
            from app.websockets import WebSocketHandler
            from app.services.communication.websocket_manager import WebSocketConnectionManager
            
            # Test instantiation
            manager = WebSocketConnectionManager()
            handler = WebSocketHandler(manager)
            
            assert handler is not None
            assert hasattr(handler, 'websocket_endpoint')
            
        except Exception as e:
            pytest.fail(f"Failed to import or instantiate WebSocket handler: {e}")


class TestModuleDiscovery:
    """Test that all Python modules in the project can be discovered and imported."""

    def get_all_python_modules(self, package_path, package_name=""):
        """Recursively discover all Python modules in a package."""
        modules = []
        
        if not os.path.exists(package_path):
            return modules
            
        for item in os.listdir(package_path):
            item_path = os.path.join(package_path, item)
            
            if item.startswith('__pycache__') or item.startswith('.'):
                continue
                
            if os.path.isfile(item_path) and item.endswith('.py') and item != '__init__.py':
                module_name = item[:-3]  # Remove .py extension
                if package_name:
                    full_module_name = f"{package_name}.{module_name}"
                else:
                    full_module_name = module_name
                modules.append(full_module_name)
                
            elif os.path.isdir(item_path) and os.path.exists(os.path.join(item_path, '__init__.py')):
                if package_name:
                    sub_package_name = f"{package_name}.{item}"
                else:
                    sub_package_name = item
                modules.extend(self.get_all_python_modules(item_path, sub_package_name))
                
        return modules

    def test_all_app_modules_importable(self):
        """Test that all modules in the app package can be imported."""
        app_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app')
        modules = self.get_all_python_modules(app_path, 'app')
        
        failed_imports = []
        
        for module_name in modules:
            try:
                importlib.import_module(module_name)
            except Exception as e:
                failed_imports.append((module_name, str(e)))
        
        if failed_imports:
            error_msg = "Failed to import the following modules:\n"
            for module, error in failed_imports:
                error_msg += f"  - {module}: {error}\n"
            pytest.fail(error_msg)

    def test_all_infrastructure_modules_importable(self):
        """Test that all modules in the infrastructure package can be imported."""
        infra_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'infrastructure')
        modules = self.get_all_python_modules(infra_path, 'infrastructure')
        
        failed_imports = []
        
        for module_name in modules:
            try:
                importlib.import_module(module_name)
            except Exception as e:
                failed_imports.append((module_name, str(e)))
        
        if failed_imports:
            error_msg = "Failed to import the following infrastructure modules:\n"
            for module, error in failed_imports:
                error_msg += f"  - {module}: {error}\n"
            pytest.fail(error_msg)


class TestImportConsistency:
    """Test that imports are consistent across the codebase."""

    def test_no_backend_prefix_imports(self):
        """Test that no modules use 'backend.' prefix in imports."""
        backend_dir = os.path.dirname(os.path.dirname(__file__))
        
        files_with_backend_imports = []
        
        for root, dirs, files in os.walk(backend_dir):
            # Skip __pycache__ and .git directories
            dirs[:] = [d for d in dirs if not d.startswith('__pycache__') and not d.startswith('.')]

            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, backend_dir)

                    # Skip this test file itself and scripts (they contain the search pattern)
                    if rel_path.endswith('test_imports.py') or rel_path.startswith('scripts'):
                        continue

                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if 'from backend.' in content or 'import backend.' in content:
                                files_with_backend_imports.append(rel_path)
                    except Exception:
                        # Skip files that can't be read
                        continue
        
        if files_with_backend_imports:
            error_msg = "Found files with 'backend.' prefix imports (should use relative imports):\n"
            for file_path in files_with_backend_imports:
                error_msg += f"  - {file_path}\n"
            pytest.fail(error_msg)

    def test_import_paths_consistency(self):
        """Test that import paths are consistent and follow project structure."""
        # This test ensures that imports follow the expected patterns
        try:
            # Test that these imports work as expected
            from app.services.job_management import MultiMaterialJobService
            from app.services.opcua import OPCUAService
            from infrastructure.cli_editor import Editor
            from infrastructure.recoater_client import RecoaterClient

            assert True  # If we get here, imports are consistent
            
        except ImportError as e:
            pytest.fail(f"Import path inconsistency detected: {e}")


class TestImportCheckerScript:
    """Test that the standalone import checker script works correctly."""

    def test_import_checker_script_exists(self):
        """Test that the import checker script exists and is executable."""
        script_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'scripts', 'check_imports.py')
        assert os.path.exists(script_path), "Import checker script should exist"

        # Test that the script can be imported
        import sys
        sys.path.insert(0, os.path.dirname(script_path))
        try:
            import importlib.util
            spec = importlib.util.spec_from_file_location("check_imports", script_path)
            check_imports = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(check_imports)
            assert hasattr(check_imports, 'main'), "Script should have a main function"
        except ImportError as e:
            pytest.fail(f"Failed to import check_imports script: {e}")
        finally:
            sys.path.pop(0)
