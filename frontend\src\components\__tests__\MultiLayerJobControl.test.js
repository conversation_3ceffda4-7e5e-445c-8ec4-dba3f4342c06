import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import MultiLayerJobControl from '../MultiLayerJobControl.vue'
import { usePrintJobStore } from '../../stores/printJobStore'
import { useStatusStore } from '../../stores/status'
import apiService from '../../services/api'

// Mock API service
vi.mock('../../services/api', () => ({
  default: {
    uploadCliFileToDrum: vi.fn(),
    startMultiMaterialJob: vi.fn(),
    cancelMultiMaterialJob: vi.fn(),
    getMultiMaterialJobStatus: vi.fn()
  }
}))

// Mock stores
vi.mock('../../stores/status', () => ({
  useStatusStore: vi.fn()
}))

describe('MultiLayerJobControl', () => {
  let wrapper
  let mockPrintJobStore
  let mockStatusStore

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()

    // Mock status store
    mockStatusStore = {
      isConnected: true
    }
    useStatusStore.mockReturnValue(mockStatusStore)

    // Create wrapper
    wrapper = mount(MultiLayerJobControl, {
      attachTo: document.body
    })

    // Get the actual print job store instance
    mockPrintJobStore = usePrintJobStore()
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  describe('Component Rendering', () => {
    it('renders correctly with initial state', () => {
      expect(wrapper.find('.multi-layer-job-control').exists()).toBe(true)
      expect(wrapper.find('.card-title').text()).toBe('Multi-Material Job Control')
      expect(wrapper.find('.file-upload-section').exists()).toBe(true)
      expect(wrapper.find('.job-controls-section').exists()).toBe(true)
    })

    it('renders drum upload sections for all 3 drums', () => {
      const drumItems = wrapper.findAll('.drum-upload-item')
      expect(drumItems).toHaveLength(3)

      drumItems.forEach((item, index) => {
        expect(item.find('.drum-label').text()).toBe(`Drum ${index}`)
        expect(item.find('.file-upload-area').exists()).toBe(true)
      })
    })

    it('renders job control buttons', () => {
      expect(wrapper.find('[data-testid="start-multimaterial-job-btn"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="cancel-multimaterial-job-btn"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="refresh-job-status-btn"]').exists()).toBe(true)
    })

    it('renders requirements check section', () => {
      const requirementItems = wrapper.findAll('.requirement-item')
      expect(requirementItems).toHaveLength(3)
      
      expect(requirementItems[0].text()).toContain('All 3 CLI files uploaded')
      expect(requirementItems[1].text()).toContain('No system errors')
      expect(requirementItems[2].text()).toContain('No active job running')
    })
  })

  describe('File Upload Functionality', () => {
    it('handles file upload for drum 0', async () => {
      const mockFile = new File(['test content'], 'test.cli', { type: 'text/plain' })
      const mockResponse = {
        data: {
          success: true,
          file_id: 'test-file-id',
          total_layers: 100
        }
      }
      apiService.uploadCliFileToDrum.mockResolvedValue(mockResponse)

      const fileInput = wrapper.find('#file-input-0')
      
      // Simulate file selection
      Object.defineProperty(fileInput.element, 'files', {
        value: [mockFile],
        writable: false
      })

      await fileInput.trigger('change')
      await wrapper.vm.$nextTick()

      expect(apiService.uploadCliFileToDrum).toHaveBeenCalled()
      expect(mockPrintJobStore.uploadedFiles[0]).toEqual({
        fileId: 'test-file-id',
        fileName: 'test.cli',
        layerCount: 100
      })
    })

    it('validates file type on upload', async () => {
      const mockFile = new File(['test content'], 'test.txt', { type: 'text/plain' })
      
      const fileInput = wrapper.find('#file-input-0')
      Object.defineProperty(fileInput.element, 'files', {
        value: [mockFile],
        writable: false
      })

      await fileInput.trigger('change')
      await wrapper.vm.$nextTick()

      expect(apiService.uploadCliFileToDrum).not.toHaveBeenCalled()
      expect(wrapper.vm.uploadErrors[0]).toBe('Please select a .cli file')
    })

    it('displays uploaded file information', async () => {
      // Set uploaded file in store
      mockPrintJobStore.setFileUploaded(0, {
        fileId: 'test-file-id',
        fileName: 'test-file.cli',
        layerCount: 50
      })

      await wrapper.vm.$nextTick()

      const drumItem = wrapper.findAll('.drum-upload-item')[0]
      expect(drumItem.find('.file-name').text()).toBe('test-file.cli')
      expect(drumItem.find('.file-details').text()).toBe('50 layers')
    })
  })

  describe('Job Control Actions', () => {
    beforeEach(() => {
      // Set up files for all drums to enable job start
      mockPrintJobStore.setFileUploaded(0, { fileId: 'file1', fileName: 'test1.cli', layerCount: 50 })
      mockPrintJobStore.setFileUploaded(1, { fileId: 'file2', fileName: 'test2.cli', layerCount: 50 })
      mockPrintJobStore.setFileUploaded(2, { fileId: 'file3', fileName: 'test3.cli', layerCount: 50 })
    })

    it('starts multi-material job when start button is clicked', async () => {
      apiService.startMultiMaterialJob.mockResolvedValue({ data: { success: true } })

      const startButton = wrapper.find('[data-testid="start-multimaterial-job-btn"]')
      await startButton.trigger('click')

      expect(apiService.startMultiMaterialJob).toHaveBeenCalled()
    })

    it('cancels job when cancel button is clicked', async () => {
      // Set up active job
      mockPrintJobStore.multiMaterialJob.jobId = 'active-job'
      mockPrintJobStore.multiMaterialJob.isActive = true
      await wrapper.vm.$nextTick()

      const mockCancelJob = vi.spyOn(mockPrintJobStore, 'cancelMultiMaterialJob')
        .mockResolvedValue({ success: true })

      const cancelButton = wrapper.find('[data-testid="cancel-multimaterial-job-btn"]')
      await cancelButton.trigger('click')

      expect(mockCancelJob).toHaveBeenCalled()
    })

    it('refreshes status when refresh button is clicked', async () => {
      const mockFetchStatus = vi.spyOn(mockPrintJobStore, 'fetchJobStatus')
        .mockResolvedValue({})

      const refreshButton = wrapper.find('[data-testid="refresh-job-status-btn"]')
      await refreshButton.trigger('click')

      expect(mockFetchStatus).toHaveBeenCalled()
    })
  })

  describe('Button States', () => {
    it('disables start button when requirements not met', async () => {
      const startButton = wrapper.find('[data-testid="start-multimaterial-job-btn"]')
      expect(startButton.element.disabled).toBe(true)
    })

    it('enables start button when all requirements met', async () => {
      // Upload files for all drums
      mockPrintJobStore.setFileUploaded(0, { fileId: 'file1', fileName: 'test1.cli', layerCount: 50 })
      mockPrintJobStore.setFileUploaded(1, { fileId: 'file2', fileName: 'test2.cli', layerCount: 50 })
      mockPrintJobStore.setFileUploaded(2, { fileId: 'file3', fileName: 'test3.cli', layerCount: 50 })
      
      await wrapper.vm.$nextTick()

      const startButton = wrapper.find('[data-testid="start-multimaterial-job-btn"]')
      expect(startButton.element.disabled).toBe(false)
    })

    it('disables cancel button when no active job', async () => {
      const cancelButton = wrapper.find('[data-testid="cancel-multimaterial-job-btn"]')
      expect(cancelButton.element.disabled).toBe(true)
    })

    it('enables cancel button when job is active', async () => {
      mockPrintJobStore.multiMaterialJob.jobId = 'active-job'
      mockPrintJobStore.multiMaterialJob.isActive = true
      
      await wrapper.vm.$nextTick()

      const cancelButton = wrapper.find('[data-testid="cancel-multimaterial-job-btn"]')
      expect(cancelButton.element.disabled).toBe(false)
    })
  })

  describe('Status Indicators', () => {
    it('shows correct job status', async () => {
      mockPrintJobStore.multiMaterialJob.status = 'printing'
      await wrapper.vm.$nextTick()

      const statusText = wrapper.find('.status-text')
      expect(statusText.text()).toBe('Printing')
    })

    it('shows correct drum status indicators', async () => {
      // Set up file uploads to simulate different states
      mockPrintJobStore.setFileUploaded(0, { fileId: 'test1', fileName: 'test1.cli', layerCount: 10 })
      mockPrintJobStore.updateDrumStatus(0, { status: 'ready' })
      mockPrintJobStore.setFileUploaded(1, { fileId: 'test2', fileName: 'test2.cli', layerCount: 10 })
      mockPrintJobStore.updateDrumStatus(1, { status: 'idle' })
      mockPrintJobStore.updateDrumStatus(2, { errorMessage: 'Test error' })

      await wrapper.vm.$nextTick()

      const drumItems = wrapper.findAll('.drum-upload-item')

      // Check drum status dots - now based on file presence and status
      expect(drumItems[0].find('.drum-status-dot').classes()).toContain('status-ready')
      expect(drumItems[1].find('.drum-status-dot').classes()).toContain('status-uploaded')
      expect(drumItems[2].find('.drum-status-dot').classes()).toContain('status-error')
    })

    it('updates requirements check indicators', async () => {
      const requirementItems = wrapper.findAll('.requirement-item')
      
      // Initially all should be pending/error
      expect(requirementItems[0].find('.requirement-icon').classes()).toContain('pending')
      expect(requirementItems[1].find('.requirement-icon').classes()).toContain('success')
      expect(requirementItems[2].find('.requirement-icon').classes()).toContain('success')

      // Upload all files
      mockPrintJobStore.setFileUploaded(0, { fileId: 'file1', fileName: 'test1.cli', layerCount: 50 })
      mockPrintJobStore.setFileUploaded(1, { fileId: 'file2', fileName: 'test2.cli', layerCount: 50 })
      mockPrintJobStore.setFileUploaded(2, { fileId: 'file3', fileName: 'test3.cli', layerCount: 50 })
      
      await wrapper.vm.$nextTick()

      expect(requirementItems[0].find('.requirement-icon').classes()).toContain('success')
    })
  })

  describe('Error Handling', () => {
    it('displays upload error messages', async () => {
      wrapper.vm.uploadErrors[0] = 'Upload failed'
      await wrapper.vm.$nextTick()

      const drumItem = wrapper.findAll('.drum-upload-item')[0]
      expect(drumItem.find('.error-message').text()).toBe('Upload failed')
    })

    it('displays success and error messages', async () => {
      wrapper.vm.successMessage = 'Operation successful'
      await wrapper.vm.$nextTick()

      expect(wrapper.find('[data-testid="success-message"]').text()).toBe('Operation successful')

      wrapper.vm.successMessage = ''
      wrapper.vm.errorMessage = 'Operation failed'
      await wrapper.vm.$nextTick()

      expect(wrapper.find('[data-testid="error-message"]').text()).toBe('Operation failed')
    })
  })

  describe('Auto-refresh Functionality', () => {
    it('sets up auto-refresh interval on mount', () => {
      // Check that setInterval was called (mocked by vitest)
      expect(wrapper.vm.$data).toBeDefined()
    })

    it('clears interval on unmount', () => {
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval')
      wrapper.unmount()
      
      // Should call clearInterval when component unmounts
      expect(clearIntervalSpy).toHaveBeenCalled()
    })
  })
})
