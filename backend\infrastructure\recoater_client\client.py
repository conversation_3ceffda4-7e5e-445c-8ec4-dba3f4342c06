"""
Recoater Client Core
===================

This module provides the main RecoaterClient class that handles all communication
with the Aerosint SPD Recoater hardware API. It serves as the single point
of contact for hardware operations.

The client implements methods for all endpoints defined in the openapi.json
specification and provides proper error handling and type safety.

Key Features:
- **HTTP Communication**: Uses requests.Session for efficient, stateful API calls.
- **Error Handling**: Custom exceptions (RecoaterConnectionError, RecoaterAPIError) with retry logic.
- **Modular Design**: Inherits from mixins (BladeControlMixin, etc.) for organized functionality.
- **Configuration**: Supports environment variables for base URL, timeouts, and retries.

Usage:
    client = RecoaterClient("http://*************:8080")
    state = client.get_state()

Dependencies:
- requests: For HTTP requests.
- OpenAPI Spec: Defines the API endpoints implemented here.

See method docstrings for detailed usage of individual operations.
"""

import requests
import time
import os
from typing import Dict, Any, Optional, List
import logging
from requests.exceptions import <PERSON>questException, ConnectionError, Timeout

from .exceptions import RecoaterConnectionError, <PERSON>coaterAPIError
from .blade_controls import BladeControlMixin
from .leveler_controls import LevelerControlMixin
from .print_controls import PrintControlMixin
from .file_management import FileManagementMixin
from .async_client import AsyncClientMixin

logger = logging.getLogger(__name__)

# In Python, inheritance is achieved by passing in parent classes using parentheses after the class name. All Python classes inherits from object by default.
# Python allows for multiple inheritance natively
# BaseModel adds methods for parsing JSON, validation without writing them
# Mixins are helper classes designed for multiple inheritance to add specific methods to other classes. 
#   Compared to Java Interfaces, Mixins have actual method implementations
#   Compared to Java Static Classes, Mixins don't have to be instantiated once and used for shared methods. Instead, Mixins are available on all classes that inherit it
class RecoaterClient(
    BladeControlMixin,
    LevelerControlMixin,
    PrintControlMixin,
    FileManagementMixin,
    AsyncClientMixin
):
    """
    Client for communicating with the Aerosint SPD Recoater hardware API.
    
    This class implements all the endpoints defined in the openapi.json
    specification and provides a clean interface for the backend to
    interact with the recoater hardware.
    """
    
    def __init__(self, base_url: str, timeout: float = 5.0):
        """
        Initialize the RecoaterClient.
        
        Args:
            base_url: The base URL of the recoater API (e.g., "http://*************:8080")
            timeout: Request timeout in seconds
        """
        # rstrip removes trailing whitespaces after the specified char
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        # A session maintains state across multiple HTTP requests by persisting certain data btw calls
        # allowing reuse for multiple API calls using the existing connection to the host
        self.session = requests.Session()
        
        logger.info(f"RecoaterClient initialized with base_url: {self.base_url}")
    
    # **kwargs handles variable-length named keyword arguments -> dict
    def _make_request(self, method: str, endpoint: str, return_raw: bool = False, **kwargs):
        """
        Make a request to the recoater API with proper error handling and retry logic.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint (without leading slash)
            return_raw: If True, return raw response object instead of JSON
            **kwargs: Additional arguments for requests

        Returns:
            JSON response as dictionary or raw response object if return_raw=True

        Raises:
            RecoaterConnectionError: If connection fails after all retries
            RecoaterAPIError: If API returns error status
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        max_attempts = int(os.getenv("RECOATER_MAX_ATTEMPTS", 2))
        retry_delay = float(os.getenv("RECOATER_RETRY_DELAY", 0.5))

        for attempt in range(max_attempts):
            try:
                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1}/{max_attempts})")
                # session.request() sends HTTP requests and returns a response object (JSON)
                response = self.session.request(
                    method=method,
                    url=url,
                    timeout=self.timeout,
                    **kwargs
                )

                # Check for HTTP errors
                if response.status_code >= 400:
                    logger.error(f"API error {response.status_code}: {response.text}")
                    raise RecoaterAPIError(f"API returned status {response.status_code}: {response.text}")

                # Return raw response if requested (for binary data like images)
                if return_raw:
                    return response

                # Try to parse JSON response
                try:
                    return response.json()
                except ValueError:
                    # If response is not JSON, return empty dict
                    return {}

            except (ConnectionError, Timeout) as e:
                if attempt < max_attempts - 1:
                    logger.warning(f"Connection error on attempt {attempt + 1}: {e}. Retrying in {retry_delay}s...")
                    time.sleep(retry_delay)
                    continue
                else:
                    logger.error(f"Connection error after {max_attempts} attempts: {e}")
                    raise RecoaterConnectionError(f"Failed to connect to recoater after {max_attempts} attempts: {e}")
            except RequestException as e:
                logger.error(f"Request error: {e}")
                raise RecoaterConnectionError(f"Request failed: {e}")
    
    def get_state(self) -> Dict[str, Any]:
        """
        Get the current state of the recoater.

        Returns:
            Dictionary containing the recoater state information
        """
        return self._make_request("GET", "/state")

    def set_state(self, action: str) -> Dict[str, Any]:
        """
        Set the recoater server state (restart or shutdown).

        Args:
            action: The action to perform ('restart' or 'shutdown')

        Returns:
            Dictionary containing the operation response
        """
        return self._make_request("POST", f"/state?action={action}")
    
    def get_config(self) -> Dict[str, Any]:
        """
        Get the recoater configuration variables.
        
        Returns:
            Dictionary containing the recoater configuration
        """
        return self._make_request("GET", "/config")
    
    def set_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Set the recoater configuration variables.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            Response from the API
        """
        return self._make_request("PUT", "/config", json=config)
    
    def get_drums(self) -> Dict[str, Any]:
        """
        Get information about all drums.
        
        Returns:
            Dictionary containing drums information
        """
        return self._make_request("GET", "/drums")
    
    def get_drum(self, drum_id: int) -> Dict[str, Any]:
        """
        Get information about a specific drum.
        
        Args:
            drum_id: The drum's ID
            
        Returns:
            Dictionary containing drum information
        """
        return self._make_request("GET", f"/drums/{drum_id}")
    
    def health_check(self) -> bool:
        """
        Perform a simple health check to verify connectivity.

        Returns:
            True if recoater is reachable and responding, False otherwise
        """
        try:
            self.get_state()
            return True
        except (RecoaterConnectionError, RecoaterAPIError):
            return False


    # Drum Control Methods
    # These methods implement the drum control endpoints from the openapi.json specification

    def get_drum_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Get the current motion command for a drum.

        Args:
            drum_id: The drum's ID

        Returns:
            Dictionary containing current motion information
        """
        return self._make_request("GET", f"/drums/{drum_id}/motion")

    def set_drum_motion(self, drum_id: int, mode: str, speed: float, distance: float = None, turns: float = None) -> Dict[str, Any]:
        """
        Create a motion command for a drum.

        Args:
            drum_id: The drum's ID
            mode: Motion mode ('absolute', 'relative', 'turns', 'speed', 'homing')
            speed: The speed of the motion [mm/s]
            distance: The distance of the motion [mm] (for absolute/relative modes)
            turns: The number of turns (for turns mode)

        Returns:
            Response from the API
        """
        payload = {
            "mode": mode,
            "speed": speed
        }

        if distance is not None:
            payload["distance"] = distance
        if turns is not None:
            payload["turns"] = turns

        return self._make_request("POST", f"/drums/{drum_id}/motion", json=payload)

    def cancel_drum_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Cancel the current motion command for a drum.

        Args:
            drum_id: The drum's ID

        Returns:
            Response from the API
        """
        return self._make_request("DELETE", f"/drums/{drum_id}/motion")

    def get_drum_ejection(self, drum_id: int, unit: str = "pascal") -> Dict[str, Any]:
        """
        Get the ejection pressure information for a drum.

        Args:
            drum_id: The drum's ID
            unit: Pressure unit ('pascal' or 'bar')

        Returns:
            Dictionary containing ejection pressure information
        """
        params = {"unit": unit}
        return self._make_request("GET", f"/drums/{drum_id}/ejection", params=params)

    def set_drum_ejection(self, drum_id: int, target: float, unit: str = "pascal") -> Dict[str, Any]:
        """
        Set the target ejection pressure for a drum.

        Args:
            drum_id: The drum's ID
            target: Target ejection pressure
            unit: Pressure unit ('pascal' or 'bar')

        Returns:
            Response from the API
        """
        payload = {
            "target": target,
            "unit": unit
        }
        return self._make_request("PUT", f"/drums/{drum_id}/ejection", json=payload)

    def get_drum_suction(self, drum_id: int) -> Dict[str, Any]:
        """
        Get the suction pressure information for a drum.

        Args:
            drum_id: The drum's ID

        Returns:
            Dictionary containing suction pressure information
        """
        return self._make_request("GET", f"/drums/{drum_id}/suction")

    def set_drum_suction(self, drum_id: int, target: float) -> Dict[str, Any]:
        """
        Set the target suction pressure for a drum.

        Args:
            drum_id: The drum's ID
            target: Target suction pressure [Pa]

        Returns:
            Response from the API
        """
        payload = {"target": target}
        return self._make_request("PUT", f"/drums/{drum_id}/suction", json=payload)
