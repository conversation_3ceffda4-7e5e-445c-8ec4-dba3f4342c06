"""
OPC UA Service Mixins
=====================

This package contains mixins that separate concerns for the OPC UA service following
Clean Architecture principles and mixin composition patterns.

Architecture Pattern: Mixin Composition
- ServerMixin: Infrastructure layer - server lifecycle and low-level variable access
- CoordinationMixin: Application layer - business coordination helpers and orchestration
- MonitoringMixin: Cross-cutting concerns - health monitoring and system reliability

Composition Order in OPCUAService:
1. CoordinationMixin (first): Business logic coordination, overrides write_variable for events
2. ServerMixin (second): Base server implementation with write_variable
3. MonitoringMixin (last): Independent monitoring functionality

Key Benefits:
- Separation of Concerns: Each mixin handles one responsibility
- Testability: Mixins can be tested independently
- Flexibility: Different combinations possible for different use cases
- Clean Architecture: Clear boundaries between layers

Exposed Mixins:
- ServerMixin: Server lifecycle management and variable store operations
- CoordinationMixin: Business logic coordination and event handling
- MonitoringMixin: Health monitoring and system reliability features

These mixins are combined by app.services.opcua.opcua_service.OPCUAService to provide
a unified, beginner-friendly API for OPC UA operations.
"""

from .server_mixin import ServerMixin
from .coordination_mixin import CoordinationMixin
from .monitoring_mixin import MonitoringMixin

__all__ = [
    "ServerMixin",
    "CoordinationMixin",
    "MonitoringMixin",
]

