# Aerosint Recoater HMI Developer Guide

## About The Aerosint Recoater HMI
The Aerosint Recoater HMI is a modern, intuitive, and reliable web-based Human-Machine Interface (HMI) for the Aerosint SPD Recoater system. It replaces the default SwaggerUI, providing a robust and responsive user experience tailored for operators in industrial or laboratory settings. The application is built with a clean backend/frontend architecture to ensure high uptime and maintainability.

*   [Acknowledgements](#acknowledgements)
*   [Design & Implementation](#design--implementation)
    *   [Backend: Multimaterial Job Management](#backend-multimaterial-job-management)
    *   [Backend: OPC UA Service](#backend-opc-ua-service)
    *   [Backend: Status Monitoring & WebSockets](#backend-status-monitoring--websockets)
    *   [Backend: CLI File Handling](#backend-cli-file-handling)
    *   [Frontend: Print View](#frontend-print-view)
    *   [Frontend: Job Progress Display](#frontend-job-progress-display)
    *   [Frontend: Error Display Panel](#frontend-error-display-panel)
    *   [Frontend: State Management (Pinia)](#frontend-state-management-pinia)
*   [Product Scope](#product-scope)
*   [User Stories](#user-stories)
*   [Non-Functional Requirements](#non-functional-requirements)
*   [Glossary](#glossary)
*   [Instructions for Manual Testing](#instructions-for-manual-testing)

## Acknowledgements

The Aerosint Recoater HMI is built using a range of modern, open-source technologies. We extend our gratitude to the developers and communities behind these essential tools:

1.  **[Python](https://www.python.org/)**: The core language for our backend services.
2.  **[FastAPI](https://fastapi.tiangolo.com/)**: A high-performance ASGI framework for building APIs with Python.
3.  **[Vue.js](https://vuejs.org/)**: A progressive JavaScript framework used for building the user interface.
4.  **[Pinia](https://pinia.vuejs.org/)**: The official state management library for Vue.js.
5.  **[Axios](https://axios-http.com/)**: A promise-based HTTP client for making API requests from the browser.
6.  **[Vite](https://vitejs.dev/)**: A modern frontend build tool that provides a faster and leaner development experience.
7.  **[Pytest](https://pytest.org/)**: A framework that makes it easy to write small, readable tests, and can scale to support complex functional testing.

## Design & Implementation

The application follows a clean architecture, separating concerns into a backend API and a frontend user interface.

### Backend: Multimaterial Job Management

The Multimaterial Job Management component is the brain of the printing process. It orchestrates the entire layer-by-layer workflow, ensuring that each material is deposited correctly according to the cached CLI files.

**API:** `app/services/job_management/multimaterial_job_service.py`

**How it Works:**

1.  **Initiation**: A print job is started via a `POST` request to the `/print/cli/start-multimaterial-job` endpoint. This action invokes the `MultiMaterialJobService`.
2.  **State Coordination**: The service acts as the central coordinator. Upon starting a job, it sets the `job_active` OPC UA flag to `True` and initializes the `total_layers` and `current_layer` variables.
3.  **Layer Loop**: The service enters a loop, iterating through each layer of the print. For each layer, it communicates with the recoater hardware by toggling the `recoater_ready_to_print` and `recoater_layer_complete` OPC UA flags. This handshake ensures the backend and the PLC remain synchronized.
4.  **Error Handling**: If any exception occurs during the print job, the service catches it, sets the `backend_error` OPC UA flag, and halts the process to prevent damage or material waste.
5.  **Completion**: Once all layers are processed, the service sets `job_active` to `False` and ensures the final progress is capped at `total_layers`.

**Why it's implemented this way:**

*   **Centralized Logic**: By placing all orchestration logic within a single service, we create a single source of truth for the print job's state. This avoids scattered logic and makes the workflow easier to understand, debug, and maintain.
*   **Decoupling**: The service is decoupled from the API layer. The API endpoint is only responsible for initiating the service, not for managing the print job's internal state. This follows the Single Responsibility Principle.
*   **Resilience**: The explicit error handling and state-flagging mechanism make the system more resilient. Operators are immediately notified of issues through the HMI, allowing for quick intervention.

**Alternatives considered:**

*   **Logic in the API layer**: We considered placing the job orchestration logic directly within the FastAPI endpoint handler. This was rejected because it would tightly couple the business logic to the web framework and make it difficult to test the workflow in isolation. It would also lead to a very large and unmanageable endpoint function.

### Backend: OPC UA Service

The OPC UA Service provides a unified interface for all communication with the recoater's PLC. It abstracts the low-level details of the OPC UA protocol behind a clean, high-level API.

**API:** `app/services/opcua/**`

**How it Works:**

The service is designed using a mixin pattern, with `Server`, `Coordination`, and `Monitoring` mixins contributing to a unified `OPCUAService`. This service exposes a simple API focused on exactly seven critical OPC UA variables:
1.  `job_active`
2.  `total_layers`
3.  `current_layer`
4.  `recoater_ready_to_print`
5.  `recoater_layer_complete`
6.  `backend_error`
7.  `plc_error`

The public API of the service provides simple `set` and `get` helpers for these variables, hiding the complexity of node IDs and data types.

**Why it's implemented this way:**

*   **Abstraction**: The service creates a strong abstraction layer between the application's business logic and the hardware communication protocol. This means that if the OPC UA variable structure were to change, we would only need to update the service, not every part of the application that interacts with the hardware.
*   **Simplicity**: By strictly limiting the interface to only the seven necessary variables, we reduce the surface area for errors and make the service's purpose clear and unambiguous.
*   **Testability**: The unified service can be easily mocked or replaced with a dummy implementation during testing, allowing us to develop and verify business logic without needing a physical connection to the recoater.

**Alternatives considered:**

*   **Direct OPC UA calls**: We could have allowed different parts of the application to make direct calls to the OPC UA client library. This was rejected because it would scatter hardware-dependent code throughout the codebase, leading to high coupling, poor maintainability, and a much higher risk of introducing bugs.

### Backend: Status Monitoring & WebSockets

This component is responsible for providing real-time feedback to the frontend. It continuously polls the recoater for its status and broadcasts updates over WebSockets.

**API:** `app/services/monitoring/**`, `app/websockets.py`

**How it Works:**

1.  The `StatusPollingService` runs in the background.
2.  At a regular interval, it uses a `DataGatherer` to collect status information from various parts of the system, including drum data, leveler data, and print data.
3.  This consolidated status is then broadcast as a JSON message over a WebSocket connection to all connected frontend clients. The message format is `{type: 'status_update', data: {...}}`.
4.  The main FastAPI application exposes a `/ws` endpoint to handle WebSocket connections.

**Why it's implemented this way:**

*   **Real-Time Feedback**: Using WebSockets provides a much better user experience than traditional HTTP polling from the frontend. The UI updates are instantaneous, giving the operator a true sense of connection to the machine's state.
*   **Efficiency**: A single, backend-driven polling loop is more efficient than having every connected client poll for status independently. This reduces network traffic and load on the backend and the recoater's controller.
*   **Scalability**: The publish-subscribe model of WebSockets allows multiple clients to receive updates simultaneously without any changes to the backend logic.

**Alternatives considered:**

*   **Frontend Polling**: The frontend could have been designed to poll the backend for status updates at a set interval using HTTP requests. This was rejected because it is less efficient, introduces noticeable latency in the UI, and complicates state management on the client side.

### Backend: CLI File Handling

This component manages the uploading, parsing, and caching of CLI (Common Layer Interface) files, which contain the instructions for printing each layer.

**API:** `app/api/print/cli.py`, `infrastructure/cli_editor/**`

**How it Works:**

1.  **Upload**: The user uploads a CLI file for a specific drum via a `POST` request to `/print/cli/upload/{drum_id}`.
2.  **Asynchronous Parsing**: To prevent blocking the server's main event loop during potentially long parsing operations, the file parsing is offloaded to a separate thread using `asyncio.to_thread`.
3.  **Caching**: Once parsed, the file's content is cached in memory on the backend, associated with the drum it was uploaded for. This allows for quick retrieval when the print job starts.
4.  **Preview**: The backend provides an endpoint (`GET /print/cli/{file_id}/layer/{n}/preview`) that generates a PNG image of a specific layer, allowing the operator to visually verify the file before printing.

![CLI Upload and Preview](images/cli-upload.png)

**Why it's implemented this way:**

*   **Responsiveness**: Offloading the parsing to a background thread is critical for keeping the server responsive. A large CLI file could otherwise block the entire application.
*   **Efficiency**: The per-drum caching workflow is highly efficient for production use. The (slow) parsing step is done once upfront, and when the job starts, the backend can immediately access the ready-to-use layer data from the cache.
*   **User Experience**: The layer preview feature is a crucial part of a safe and effective workflow, allowing operators to catch errors before committing to a print.

**Alternatives considered:**

*   **Synchronous Parsing**: Parsing the file directly in the request handler. This was rejected due to the significant performance risk of blocking the event loop.
*   **No Caching**: Sending the full CLI file with the "start job" request. This was rejected because it would introduce unnecessary network overhead and require re-parsing the file every time a job is run.

### Frontend: Print View

This is the main operator HMI, bringing together all the necessary controls and information for managing a print job.

**API:** `frontend/src/views/PrintView.vue`

**How it Works:**

The `PrintView` is a top-level Vue component composed of several smaller, focused child components, creating a card-based dashboard. These cards include:
*   File Management (for uploading and deleting CLI files per drum)
*   CLI Layer Selection (for previewing layers)
*   Print Job Management (for starting and canceling jobs)
*   Job Progress Display
*   Error Display Panel

![Main HMI Interface](images/main-interface.png)

Each card encapsulates a specific piece of functionality. For example, the "Delete" button on a drum's File Management card triggers a call to the `POST /print/cli/clear-drum-cache/{drum_id}` endpoint and simultaneously clears the corresponding entry in the Pinia store to ensure the UI updates instantly.

**Why it's implemented this way:**

*   **Modularity**: The card-based design, built with Vue components, makes the UI highly modular. It's easy to add, remove, or rearrange functionality without impacting the rest of the view.
*   **Maintainability**: Each component has a clearly defined responsibility, making the codebase easier to understand and maintain. For example, all logic related to displaying errors is contained within `ErrorDisplayPanel.vue`.
*   **Information Density**: The layout provides operators with all the critical information and controls at a glance, reducing the need to navigate between different screens.

**Alternatives considered:**

*   **Monolithic Component**: We could have built the entire UI as a single, large Vue component. This was rejected because it would result in a file that is thousands of lines long, making it nearly impossible to debug or extend.

### Frontend: Job Progress Display

This component provides a real-time view of the active print job's status, including which files are being used and the current layer progress.

**API:** `frontend/src/components/JobProgressDisplay.vue`

**How it Works:**

The component gets its data exclusively from the `printJobStore` (a Pinia store). It displays:
*   The `uploaded` status for each drum.
*   The `fileName` and `layerCount` for the file associated with each drum.
*   The overall job progress (`current_layer` / `total_layers`).
*   A "Simulation" banner that appears when no official job is active but the WebSocket feed indicates the recoater is printing (useful for development and testing).

![Job Progress Display](images/print-job-management.png)

**Why it's implemented this way:**

*   **Single Source of Truth**: By relying entirely on the Pinia store for its state, the component ensures that it is always in sync with the rest of the application. It doesn't need to make its own API calls or manage its own state.
*   **Reactive UI**: Because it's bound to the store, the display updates automatically whenever the job state changes, with no extra code required to manually refresh the view.
*   **Clear Operator Feedback**: The component provides clear, unambiguous feedback about the job's status, including special states like simulation mode.

**Alternatives considered:**

*   **Direct API Calls**: The component could have polled the backend for job status itself. This was rejected because it would violate the principle of having a single source of truth (the Pinia store) and would lead to redundant API calls.

### Frontend: Error Display Panel

This component displays critical backend and PLC errors to the operator and provides a mechanism to clear them.

**API:** `frontend/src/components/ErrorDisplayPanel.vue`

**How it Works:**

The panel displays separate flags for "Backend Error" and "PLC Error," which are sourced from the `printJobStore`. It also shows a list of recent error messages. A "Clear Error Flags" button allows the operator to acknowledge the errors and reset the flags. This button triggers the `clearErrorFlagsAPI` action in the store, which sends a `POST` request to the `/multimaterial-job/clear-error` endpoint.

**Why it's implemented this way:**

*   **Centralized Error Handling**: All error display logic is contained within this single component, making it easy to manage how errors are presented to the user.
*   **Clear Action for Operators**: The component provides a clear, explicit action for operators to take when an error occurs. This is a critical safety and operational feature.
*   **Decoupling**: The component is fully decoupled from the components that might cause errors. It simply reacts to the state managed in the `printJobStore`.

**Alternatives considered:**

*   **Pop-up Modals for Errors**: While modals can be effective for grabbing attention, they can also be disruptive to the operator's workflow. A persistent panel was chosen to provide continuous visibility of the error state without blocking other UI elements.

### Frontend: State Management (Pinia)

Pinia stores are the heart of the frontend application, acting as the single source of truth for all shared state.

**API:** `frontend/src/stores/printJobStore.js`, `frontend/src/stores/status.js`

**How it Works:**

We use two main stores:
1.  **`status.js`**: This store is responsible for managing the WebSocket connection and subscribing to the real-time status updates from the backend. It exposes the live data (e.g., `print_data`) to the rest of the application.
2.  **`printJobStore.js`**: This store manages the state of the print job itself. It holds the status of the multi-material job, the list of uploaded files for each drum, and any error flags. It also contains all the API actions related to jobs, such as `startJob`, `cancelJob`, and `clearErrorFlagsAPI`.

Components do not make API calls directly. Instead, they call actions on the stores, which then handle the API communication and update the state.

**Why it's implemented this way:**

*   **Single Source of Truth**: Centralizing the state in Pinia stores eliminates data synchronization issues between components. When the state in the store updates, every component that uses that state automatically re-renders.
*   **Improved Logic Organization**: Separating state management from component logic makes both parts of the application simpler. Components are only responsible for displaying data and dispatching actions, while stores are responsible for business logic and API communication.
*   **Easier Debugging**: With Vue Devtools, Pinia stores are fully inspectable, allowing developers to see exactly how the state is changing over time, which is invaluable for debugging.

**Alternatives considered:**

*   **Prop Drilling**: Passing state down through many layers of components via props. This was rejected as it becomes extremely cumbersome and error-prone in an application of this complexity.
*   **Direct API calls from components**: This was rejected because it leads to scattered, duplicated logic and makes it very difficult to manage a consistent application state.

## Product Scope

**Target User Profile:**

*   An operator of an Aerosint SPD Recoater system in an industrial or R&D laboratory setting.
*   May not have a deep technical or software background.
*   Requires a clear, reliable, and intuitive interface to manage print jobs, monitor machine status, and respond to errors.
*   Values efficiency and clarity over feature density.

## User Stories

| As a...  | I want to...                                                                   | So that I can...                                                              |
| :------- | :----------------------------------------------------------------------------- | :---------------------------------------------------------------------------- |
| Operator | see a clear, real-time overview of the recoater's status                       | know if the machine is ready, running, or has an error at a glance.           |
| Operator | upload a separate CLI file for each of the three material drums                | prepare a multi-material print job.                                           |
| Operator | preview a specific layer from an uploaded CLI file                             | visually verify that the file is correct before starting a print.             |
| Operator | start a multi-material print job with the currently uploaded files             | execute the manufacturing process.                                            |
| Operator | see the current layer and total layers progressing in real time                | monitor the print job's progress and estimate its completion time.            |
| Operator | be clearly notified of a backend or PLC error                                  | stop the process and diagnose the issue to prevent material waste or damage.  |
| Operator | cancel an in-progress print job                                                | safely stop the machine if I notice a problem.                                |
| Operator | clear error flags after I have resolved an issue                               | reset the system and prepare it for the next job.                             |
| Operator | clear the cached file for a specific drum                                      | prepare the system for a completely new job configuration.                    |

## Non-Functional Requirements

1.  **Reliability**: The HMI must maintain a stable connection to the backend and accurately reflect the machine's state. It should handle connection interruptions gracefully.
2.  **Performance**: All UI interactions should feel instantaneous. Real-time status updates should appear with no more than a 1-second delay. API responses should complete within 200ms for typical operations.
3.  **Usability**: The interface should be intuitive enough for a new operator to learn the basic workflows (upload, print, monitor) within 30 minutes. Error messages must be clear and actionable.
4.  **Maintainability**: The codebase must be well-documented and logically structured to allow new developers to contribute effectively. The separation between frontend, backend, and infrastructure code must be maintained.
5.  **Compatibility**: The HMI should run correctly on the latest versions of major web browsers (Firefox, Chrome, Edge).

## Glossary

*   **HMI**: Human-Machine Interface. The graphical user interface used by an operator to interact with a machine.
*   **Recoater**: The core mechanical system responsible for depositing and smoothing layers of powder material.
*   **CLI File**: Common Layer Interface file. A text-based or binary file format that defines the geometry for one or more layers of a print.
*   **OPC UA**: Open Platform Communications Unified Architecture. A machine-to-machine communication protocol for industrial automation. It's how our backend communicates with the recoater's PLC.
*   **PLC**: Programmable Logic Controller. The industrial computer that controls the recoater's hardware.
*   **Drum**: A cylindrical container holding one of the powder materials to be printed. The system supports up to 3 drums.
*   **Cache**: An in-memory store on the backend used to hold the parsed contents of CLI files for fast access during a print job.

## Instructions for Manual Testing

### System Setup

1.  **Prerequisites**: Ensure you have Node.js (v16+) and Python (v3.9+) installed.
2.  **Install Dependencies**: Run the `install_deps.bat` script in the root directory to install both backend and frontend dependencies.
3.  **Configure Backend**:
    *   Navigate to the `backend` directory.
    *   Copy `.env.example` to a new file named `.env`.
    *   Edit the `.env` file and set `RECOATER_API_ADDRESS` to the IP address of the running recoater system or a mock server.
4.  **Run the Application**: Use the `run.bat` script in the root directory. This will start both the backend `uvicorn` server and the frontend `vite` development server in parallel.
5.  **Access the HMI**: Open a web browser and navigate to the local address provided by the Vite server (usually `http://localhost:5173`).

### Test Cases

#### 1. File Management and Preview
1.  **Action**: In the "File Management" card for Drum 0, click "Choose File" and select a valid `.cli` file.
2.  **Expected**: The file name appears next to the button, and the "Uploaded" status in the "Job Progress" card for Drum 0 turns green.
3.  **Action**: In the "CLI Layer Selection" card, enter the File ID `0` (for Drum 0) and a valid layer number (e.g., `1`). Click "Preview Layer".
4.  **Expected**: A PNG image preview of the selected layer is displayed below the button.
5.  **Action**: In the "File Management" card for Drum 0, click "Delete".
6.  **Expected**: The file name disappears, and the "Uploaded" status for Drum 0 turns gray.

#### 2. Starting and Canceling a Job
1.  **Action**: Upload CLI files for at least one drum as described above.
2.  **Action**: In the "Print Job Management" card, click "Start Print Job".
3.  **Expected**:
    *   The "Start Print Job" button becomes disabled.
    *   The "Cancel Print Job" button becomes enabled.
    *   The "Job Progress" card shows the overall progress updating (e.g., "1 / 100").
4.  **Action**: Click "Cancel Print Job".
5.  **Expected**: The job progress stops, and the "Start" and "Cancel" buttons return to their initial state.

#### 3. Error Handling
1.  **Action**: (This may require simulating an error condition). Start a print job that is known to cause a PLC or backend error.
2.  **Expected**:
    *   The print job stops.
    *   The "Error Display Panel" shows a red indicator for either "Backend Error" or "PLC Error".
    *   An error message appears in the history.
3.  **Action**: Click the "Clear Error Flags" button.
4.  **Expected**: The red error indicators in the panel turn gray. The system is ready to attempt another job.