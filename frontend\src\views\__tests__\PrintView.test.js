/**
 * Tests for PrintView component
 * =============================
 *
 * Comprehensive test suite for the PrintView component including
 * layer parameters management and preview functionality.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import PrintView from '../PrintView.vue'
import { useStatusStore } from '../../stores/status'
import apiService from '../../services/api'

// Mock API service
vi.mock('../../services/api', () => ({
  default: {
    getLayerParameters: vi.fn(),
    setLayerParameters: vi.fn(),
    getLayerPreview: vi.fn(),
    getDrumGeometryPreview: vi.fn(),
    startMultiMaterialJob: vi.fn(),
    cancelMultiMaterialJob: vi.fn(),
    cancelPrintJob: vi.fn(),
    getMultiMaterialJobStatus: vi.fn(),
    uploadCliFileToDrum: vi.fn(),
    downloadDrumGeometry: vi.fn(),
    clearDrumCache: vi.fn(),
    uploadCliFile: vi.fn(),
    getCliLayerPreview: vi.fn(),
    sendCliLayerToDrum: vi.fn()
  }
}))

// Mock status store
vi.mock('../../stores/status', () => ({
  useStatusStore: vi.fn()
}))

describe('PrintView', () => {
  let mockStatusStore

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()

    // Ensure document.body exists and is properly set up
    if (!document.body) {
      document.body = document.createElement('body')
    }

    // Clear any existing content
    document.body.innerHTML = ''

    // Mock URL APIs for blob handling
    global.URL = global.URL || {}
    global.URL.createObjectURL = vi.fn(() => 'blob:mock-url')
    global.URL.revokeObjectURL = vi.fn()

    // Mock confirm function
    global.confirm = vi.fn(() => true)

    // Set up default API mocks
    apiService.getLayerParameters.mockResolvedValue({
      data: {
        layer_height: 0.1,
        exposure_time: 8.0,
        bottom_exposure_time: 60.0,
        light_off_delay: 1.0,
        bottom_light_off_delay: 1.0,
        bottom_layer_count: 5,
        z_lift_distance: 5.0,
        z_lift_speed: 3.0,
        z_retract_speed: 3.0
      }
    })

    apiService.getMultiMaterialJobStatus.mockResolvedValue({
      data: {
        status: 'ready',
        is_printing: false,
        has_error: false
      }
    })

    mockStatusStore = {
      isConnected: true, // Default to connected for most tests
      printData: {
        status: 'ready',
        is_printing: false,
        has_error: false
      },
      connectWebSocket: vi.fn(),
      disconnectWebSocket: vi.fn()
    }

    useStatusStore.mockReturnValue(mockStatusStore)
  })

  describe('Component Rendering', () => {
    it('renders the print view with all sections', () => {
      const wrapper = mount(PrintView)

      expect(wrapper.find('.print-view').exists()).toBe(true)
      expect(wrapper.find('.view-title').text()).toBe('Print Control')
      expect(wrapper.find('.status-card').exists()).toBe(true)
      expect(wrapper.find('.control-card').exists()).toBe(true)
      expect(wrapper.text()).toContain('Layer Parameters')
      expect(wrapper.text()).toContain('Layer Preview')
    })

    it('shows disconnected status when not connected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      expect(wrapper.find('.status-disconnected').exists()).toBe(true)
      expect(wrapper.find('.status-text').text()).toBe('Disconnected')
    })

    it('shows connected status when connected', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('.status-connected').exists()).toBe(true)
      expect(wrapper.find('.status-text').text()).toBe('Connected')
    })

    it('shows disabled overlay when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      const overlays = wrapper.findAll('.disabled-overlay')
      expect(overlays.length).toBeGreaterThan(0)
      expect(wrapper.text()).toContain('Connect to recoater to configure layer parameters')
      expect(wrapper.text()).toContain('Connect to recoater to view layer preview')
    })

    it('shows disabled overlays when print job is active', () => {
      mockStatusStore.isConnected = true
      mockStatusStore.printData = {
        job_status: {
          is_printing: true
        }
      }

      // Mock CLI file info so the CLI Layer Selection section appears
      apiService.uploadCliFile.mockResolvedValue({
        data: {
          file_id: 'test-cli-123',
          total_layers: 10,
          filename: 'test.cli'
        }
      })

      const wrapper = mount(PrintView)

      // Set CLI file info to make the CLI Layer Selection section visible
      wrapper.vm.cliFileInfo = {
        file_id: 'test-cli-123',
        total_layers: 10,
        filename: 'test.cli'
      }

      // Wait for reactivity to update the DOM
      wrapper.vm.$nextTick(() => {
        const overlays = wrapper.findAll('.disabled-overlay')
        expect(overlays.length).toBeGreaterThan(0)
        expect(wrapper.text()).toContain('Print job is active - parameters cannot be modified')
        expect(wrapper.text()).toContain('Print job is active - preview not available')
        expect(wrapper.text()).toContain('Print job is active - file management not available')
        expect(wrapper.text()).toContain('Print job is active - CLI file operations not available')
        expect(wrapper.text()).toContain('Print job is active - layer range selection not available')
      })
    })
  })

  describe('Layer Parameters', () => {
    it('renders all parameter input fields', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('#filling-id').exists()).toBe(true)
      expect(wrapper.find('#speed').exists()).toBe(true)
      expect(wrapper.find('#x-offset').exists()).toBe(true)
      expect(wrapper.find('.parameter-checkbox').exists()).toBe(true)
    })

    it('disables inputs when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      expect(wrapper.find('#filling-id').attributes('disabled')).toBeDefined()
      expect(wrapper.find('#speed').attributes('disabled')).toBeDefined()
      expect(wrapper.find('#x-offset').attributes('disabled')).toBeDefined()
      expect(wrapper.find('.parameter-checkbox').attributes('disabled')).toBeDefined()
    })

    it('enables inputs when connected', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('#filling-id').attributes('disabled')).toBeUndefined()
      expect(wrapper.find('#speed').attributes('disabled')).toBeUndefined()
      expect(wrapper.find('#x-offset').attributes('disabled')).toBeUndefined()
      expect(wrapper.find('.parameter-checkbox').attributes('disabled')).toBeUndefined()
    })

    it('calls loadParameters when Load Current button is clicked', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockResolvedValue({
        data: {
          filling_id: 2,
          speed: 25.0,
          powder_saving: false,
          x_offset: 5.0
        }
      })

      const wrapper = mount(PrintView)
      // Clear the call from onMounted
      apiService.getLayerParameters.mockClear()

      const loadButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Current'))

      await loadButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.getLayerParameters).toHaveBeenCalledOnce()
    })

    it('calls saveParameters when Save Parameters button is clicked', async () => {
      mockStatusStore.isConnected = true
      apiService.setLayerParameters.mockResolvedValue({ data: { success: true } })

      const wrapper = mount(PrintView)
      const saveButton = wrapper.findAll('button').find(btn => btn.text().includes('Save Parameters'))

      await saveButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.setLayerParameters).toHaveBeenCalledOnce()
    })

    it('validates parameters before saving', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      // Set invalid speed
      wrapper.vm.layerParams.speed = 0
      expect(wrapper.vm.isParametersValid).toBe(false)

      // Set valid speed
      wrapper.vm.layerParams.speed = 30
      expect(wrapper.vm.isParametersValid).toBe(true)
    })

    it('disables save button when parameters are invalid', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockResolvedValue({
        data: {
          filling_id: 1,
          speed: 30.0,
          powder_saving: true,
          x_offset: 0.0
        }
      })

      const wrapper = mount(PrintView)
      await wrapper.vm.$nextTick()

      // Set invalid parameters
      wrapper.vm.layerParams.speed = 0
      await wrapper.vm.$nextTick()

      // Find save button by looking for the button that calls saveParameters
      const saveButton = wrapper.find('button[class*="btn-primary"]')
      expect(saveButton.exists()).toBe(true)
      expect(saveButton.attributes('disabled')).toBeDefined()
    })
  })

  describe('Layer Preview', () => {
    it('shows preview placeholder when no image is loaded', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('.preview-placeholder').exists()).toBe(true)
      expect(wrapper.find('.preview-icon').exists()).toBe(true)
      expect(wrapper.text()).toContain('No preview available')
    })

    it('shows loading state when preview is loading', async () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      wrapper.vm.previewLoading = true
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.preview-loading').exists()).toBe(true)
      expect(wrapper.find('.loading-spinner').exists()).toBe(true)
      expect(wrapper.text()).toContain('Loading preview...')
    })

    it('calls loadPreview when Load Preview button is clicked', async () => {
      mockStatusStore.isConnected = true

      // Mock blob response
      const mockBlob = new Blob(['mock image data'], { type: 'image/png' })
      apiService.getLayerPreview.mockResolvedValue({ data: mockBlob })

      const wrapper = mount(PrintView)
      const previewButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Preview'))

      await previewButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.getLayerPreview).toHaveBeenCalledOnce()
    })

    it('disables preview button when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      const previewButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Preview'))
      expect(previewButton.attributes('disabled')).toBeDefined()
    })

    it('has preview source selector with correct options', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      const previewSelect = wrapper.find('#preview-drum-select')
      expect(previewSelect.exists()).toBe(true)

      const options = previewSelect.findAll('option')
      expect(options).toHaveLength(4) // 1 layer option + 3 drum options
      expect(options[0].text()).toBe('Current Layer Configuration')
      expect(options[1].text()).toBe('Drum 0 Geometry')
      expect(options[2].text()).toBe('Drum 1 Geometry')
      expect(options[3].text()).toBe('Drum 2 Geometry')
    })

    it('calls getDrumGeometryPreview when drum geometry is selected', async () => {
      mockStatusStore.isConnected = true

      // Mock blob response
      const mockBlob = new Blob(['mock drum image data'], { type: 'image/png' })
      apiService.getDrumGeometryPreview.mockResolvedValue({ data: mockBlob })

      const wrapper = mount(PrintView)

      // Select drum 1 geometry
      const previewSelect = wrapper.find('#preview-drum-select')
      await previewSelect.setValue('drum-1')

      // Click load preview
      const previewButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Preview'))
      await previewButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.getDrumGeometryPreview).toHaveBeenCalledWith(1)
    })
  })

  describe('Error Handling', () => {
    it('shows error message when parameter loading fails', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockRejectedValue(new Error('Connection failed'))

      const wrapper = mount(PrintView)
      await wrapper.vm.loadParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-error').exists()).toBe(true)
      expect(wrapper.text()).toContain('Failed to load layer parameters')
    })

    it('shows error message when parameter saving fails', async () => {
      mockStatusStore.isConnected = true
      apiService.setLayerParameters.mockRejectedValue(new Error('Save failed'))

      const wrapper = mount(PrintView)
      await wrapper.vm.saveParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-error').exists()).toBe(true)
      expect(wrapper.text()).toContain('Failed to save layer parameters')
    })

    it('shows error message when preview loading fails', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerPreview.mockRejectedValue(new Error('Preview failed'))

      const wrapper = mount(PrintView)
      await wrapper.vm.loadPreview()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-error').exists()).toBe(true)
      expect(wrapper.text()).toContain('Failed to load preview')
    })
  })

  describe('Success Messages', () => {
    it('shows success message when parameters are loaded successfully', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockResolvedValue({
        data: { filling_id: 1, speed: 30.0, powder_saving: true, x_offset: 0.0 }
      })

      const wrapper = mount(PrintView)
      await wrapper.vm.loadParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-success').exists()).toBe(true)
      expect(wrapper.text()).toContain('Layer parameters loaded successfully')
    })

    it('shows success message when parameters are saved successfully', async () => {
      mockStatusStore.isConnected = true
      apiService.setLayerParameters.mockResolvedValue({ data: { success: true } })

      const wrapper = mount(PrintView)
      await wrapper.vm.saveParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-success').exists()).toBe(true)
      expect(wrapper.text()).toContain('Layer parameters saved successfully')
    })
  })

  describe('WebSocket Integration', () => {
    it('does not manually connect to WebSocket on mount (managed globally)', () => {
      mount(PrintView)
      // WebSocket connection is now managed globally, not by individual components
      expect(mockStatusStore.connectWebSocket).not.toHaveBeenCalled()
    })
  })

  describe('Component Lifecycle', () => {
    it('cleans up object URLs on unmount', () => {
      // Mock URL.revokeObjectURL before mounting
      global.URL = global.URL || {}
      global.URL.revokeObjectURL = vi.fn()

      const wrapper = mount(PrintView)

      // Set a preview URL
      wrapper.vm.previewImageUrl = 'blob:mock-url'

      wrapper.unmount()

      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:mock-url')
    })
  })

  describe('File Management - 3-Column Design', () => {
    beforeEach(() => {
      mockStatusStore.isConnected = true
      apiService.uploadCliFileToDrum.mockResolvedValue({ data: { file_id: 'test-id', filename: 'file.cli', total_layers: 10 } })
      apiService.downloadDrumGeometry.mockResolvedValue({
        data: new Blob(['mock data'], { type: 'image/png' })
      })
      apiService.clearDrumCache.mockResolvedValue({ data: { success: true } })
    })

    it('renders three drum columns when connected', () => {
      const wrapper = mount(PrintView)

      // Check for 3-column grid
      const grid = wrapper.find('.grid-3-equal')
      expect(grid.exists()).toBe(true)

      // Check for three FileUploadColumn components (one for each drum)
      const drumColumns = wrapper.findAllComponents({ name: 'FileUploadColumn' })
      expect(drumColumns).toHaveLength(3)

      // Verify each drum has correct ID
      expect(drumColumns[0].props('drumId')).toBe(0)
      expect(drumColumns[1].props('drumId')).toBe(1)
      expect(drumColumns[2].props('drumId')).toBe(2)
    })

    it('shows disabled overlay when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      const controlCards = wrapper.findAll('.control-card')
      const fileManagementCard = controlCards.find(card =>
        card.text().includes('File Management')
      )
      expect(fileManagementCard.find('.disabled-overlay').exists()).toBe(true)
    })

    it('passes correct props to FileUploadColumn components', () => {
      const wrapper = mount(PrintView)

      const drumColumns = wrapper.findAllComponents({ name: 'FileUploadColumn' })

      drumColumns.forEach((column, index) => {
        expect(column.props('drumId')).toBe(index)
        expect(column.props('isConnected')).toBe(true)
        expect(column.props('isLoading')).toBe(false)
      })
    })

    it('handles drum upload events correctly', async () => {
      const wrapper = mount(PrintView)
      const mockFile = new File(['test'], 'test.png', { type: 'image/png' })

      // Emit upload event from first drum column
      const firstColumn = wrapper.findAllComponents({ name: 'FileUploadColumn' })[0]
      await firstColumn.vm.$emit('upload', { drumId: 0, file: mockFile })

      expect(apiService.uploadCliFileToDrum).toHaveBeenCalledWith(mockFile, 0)
    })

    it('handles drum download events correctly', async () => {
      const wrapper = mount(PrintView)

      // Emit download event from second drum column
      const secondColumn = wrapper.findAllComponents({ name: 'FileUploadColumn' })[1]
      await secondColumn.vm.$emit('download', { drumId: 1 })

      expect(apiService.downloadDrumGeometry).toHaveBeenCalledWith(1)
    })

    it('handles drum delete events correctly', async () => {
      const wrapper = mount(PrintView)

      // Emit delete event from third drum column
      const thirdColumn = wrapper.findAllComponents({ name: 'FileUploadColumn' })[2]
      await thirdColumn.vm.$emit('delete', { drumId: 2 })

      expect(apiService.clearDrumCache).toHaveBeenCalledWith(2)
    })

    it('shows loading state across all columns during operations', async () => {
      const wrapper = mount(PrintView)

      // Set loading state
      wrapper.vm.isFileOperationLoading = true
      await wrapper.vm.$nextTick()

      const drumColumns = wrapper.findAllComponents({ name: 'FileUploadColumn' })
      drumColumns.forEach(column => {
        expect(column.props('isLoading')).toBe(true)
      })
    })

    it('operates independently per drum column', async () => {
      const wrapper = mount(PrintView)
      const mockFile1 = new File(['test1'], 'test1.png', { type: 'image/png' })
      const mockFile2 = new File(['test2'], 'test2.png', { type: 'image/png' })

      const columns = wrapper.findAllComponents({ name: 'FileUploadColumn' })

      // Upload to different drums should work independently
      await columns[0].vm.$emit('upload', { drumId: 0, file: mockFile1 })
      await columns[1].vm.$emit('upload', { drumId: 1, file: mockFile2 })

      expect(apiService.uploadCliFileToDrum).toHaveBeenCalledWith(mockFile1, 0)
      expect(apiService.uploadCliFileToDrum).toHaveBeenCalledWith(mockFile2, 1)
      expect(apiService.uploadCliFileToDrum).toHaveBeenCalledTimes(2)
    })

    it('shows success messages for drum operations', async () => {
      const wrapper = mount(PrintView)

      // Test upload success message
      await wrapper.vm.handleDrumUpload({ drumId: 1, file: new File(['test'], 'test.png') })
      expect(wrapper.vm.successMessage).toContain('CLI file cached for drum 1')

      // Test download success message
      await wrapper.vm.handleDrumDownload({ drumId: 2 })
      expect(wrapper.vm.successMessage).toBe('Geometry file downloaded from drum 2')

      // Test delete success message
      await wrapper.vm.handleDrumDelete({ drumId: 0 })
      expect(wrapper.vm.successMessage).toBe('Cleared cached CLI file for drum 0')
    })

    it('handles API errors for drum operations', async () => {
      apiService.uploadCliFileToDrum.mockRejectedValue(new Error('Upload failed'))

      const wrapper = mount(PrintView)
      await wrapper.vm.handleDrumUpload({ drumId: 0, file: new File(['test'], 'test.png') })

      expect(wrapper.vm.errorMessage).toContain('Failed to upload CLI file')
    })
  })

  // FIX: Wrapped the following tests in a 'describe' block.
  describe('Individual File Actions', () => {
    it('handles file selection', async () => {
      const wrapper = mount(PrintView)

      const fileInput = wrapper.find('#file-input')
      expect(fileInput.exists()).toBe(true)

      // Mock file
      const mockFile = new File(['test'], 'test.png', { type: 'image/png' })

      // Simulate file selection
      Object.defineProperty(fileInput.element, 'files', {
        value: [mockFile],
        writable: false
      })

      await fileInput.trigger('change')

      expect(wrapper.vm.selectedFile).toBe(mockFile)
    })

    it('uploads file successfully', async () => {
      apiService.uploadCliFileToDrum.mockResolvedValue({ data: { file_id: 'ok', total_layers: 10 } })

      const wrapper = mount(PrintView)

      // Set up file and drum selection
      const mockFile = new File(['test'], 'test.png', { type: 'image/png' })
      wrapper.vm.selectedFile = mockFile
      wrapper.vm.selectedDrumId = '1'

      await wrapper.vm.uploadFile()

      expect(apiService.uploadCliFileToDrum).toHaveBeenCalledWith(mockFile, '1')
      expect(wrapper.vm.selectedFile).toBe(null)
    })

    it('downloads file successfully', async () => {
      const mockBlob = new Blob(['test'], { type: 'image/png' })
      apiService.downloadDrumGeometry.mockResolvedValue({ data: mockBlob })

      // Mock DOM methods more carefully
      const mockLink = {
        href: '',
        download: '',
        click: vi.fn(),
        setAttribute: vi.fn(),
        style: {}
      }

      // Mock URL.createObjectURL
      const originalCreateObjectURL = global.URL.createObjectURL
      global.URL.createObjectURL = vi.fn(() => 'blob:mock-url')

      // Mock document.createElement to return our mock link
      const originalCreateElement = document.createElement
      document.createElement = vi.fn((tagName) => {
        if (tagName === 'a') {
          return mockLink
        }
        return originalCreateElement.call(document, tagName)
      })

      // Mock appendChild and removeChild to avoid DOM issues
      const originalAppendChild = document.body.appendChild
      const originalRemoveChild = document.body.removeChild
      document.body.appendChild = vi.fn()
      document.body.removeChild = vi.fn()

      const wrapper = mount(PrintView)
      wrapper.vm.actionDrumId = '1'

      await wrapper.vm.downloadFile()

      expect(apiService.downloadDrumGeometry).toHaveBeenCalledWith('1')
      expect(mockLink.click).toHaveBeenCalled()

      // Restore original methods
      document.createElement = originalCreateElement
      document.body.appendChild = originalAppendChild
      document.body.removeChild = originalRemoveChild
      global.URL.createObjectURL = originalCreateObjectURL
    })

    it('deletes file with confirmation', async () => {
      apiService.clearDrumCache.mockResolvedValue({ data: { success: true } })

      // Mock confirm dialog
      global.confirm = vi.fn(() => true)

      const wrapper = mount(PrintView)
      wrapper.vm.actionDrumId = '1'

      await wrapper.vm.deleteFile()

      expect(global.confirm).toHaveBeenCalled()
      expect(apiService.clearDrumCache).toHaveBeenCalledWith('1')
    })

    it('cancels file deletion when not confirmed', async () => {
      // Mock confirm dialog to return false
      global.confirm = vi.fn(() => false)

      const wrapper = mount(PrintView)
      wrapper.vm.actionDrumId = '1'

      await wrapper.vm.deleteFile()

      expect(global.confirm).toHaveBeenCalled()
      expect(apiService.clearDrumCache).not.toHaveBeenCalled()
    })
  })

  describe('Job Management', () => {
    beforeEach(() => {
      // Reset all mocks
      vi.clearAllMocks()

      // Ensure clean DOM state
      if (document.body) {
        document.body.innerHTML = ''
      }

      mockStatusStore.isConnected = true
      mockStatusStore.printData = {
        job_status: {
          state: 'ready',
          is_printing: false,
          has_error: false
        }
      }
    })

    it('renders job management section when connected', () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      expect(wrapper.find('.job-status-section').exists()).toBe(true)
      expect(wrapper.find('.status-display').exists()).toBe(true)
      expect(wrapper.find('.job-controls').exists()).toBe(true)

      wrapper.unmount()
    })

    it('displays job status correctly', () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      expect(wrapper.vm.getJobStatusText()).toBe('Ready')
      expect(wrapper.vm.getJobStatusClass()).toBe('status-ready')
      expect(wrapper.vm.isPrinting).toBe(false)
      expect(wrapper.vm.hasJobError).toBe(false)

      wrapper.unmount()
    })

    it('displays printing status correctly', () => {
      mockStatusStore.printData.job_status = {
        state: 'printing',
        is_printing: true,
        has_error: false
      }

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      expect(wrapper.vm.getJobStatusText()).toBe('Printing')
      expect(wrapper.vm.getJobStatusClass()).toBe('status-active')
      expect(wrapper.vm.isPrinting).toBe(true)

      wrapper.unmount()
    })

    it('displays error status correctly', () => {
      mockStatusStore.printData.job_status = {
        state: 'error',
        is_printing: false,
        has_error: true
      }

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      expect(wrapper.vm.getJobStatusText()).toBe('Error')
      expect(wrapper.vm.getJobStatusClass()).toBe('status-error')
      expect(wrapper.vm.hasJobError).toBe(true)

      wrapper.unmount()
    })

    it('starts print job successfully', async () => {
      apiService.startMultiMaterialJob.mockResolvedValue({ data: { success: true } })

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      await wrapper.vm.startPrintJob()

      expect(apiService.startMultiMaterialJob).toHaveBeenCalled()

      wrapper.unmount()
    })

    it('cancels print job with confirmation', async () => {
      apiService.cancelPrintJob.mockResolvedValue({ data: { success: true } })
      global.confirm = vi.fn(() => true)

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      await wrapper.vm.cancelPrintJob()

      expect(global.confirm).toHaveBeenCalled()
      expect(apiService.cancelPrintJob).toHaveBeenCalled()

      wrapper.unmount()
    })

    it('does not cancel print job when not confirmed', async () => {
      global.confirm = vi.fn(() => false)

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      await wrapper.vm.cancelPrintJob()

      expect(global.confirm).toHaveBeenCalled()
      expect(apiService.cancelPrintJob).not.toHaveBeenCalled()

      wrapper.unmount()
    })

    it('refreshes job status', async () => {
      apiService.getMultiMaterialJobStatus.mockResolvedValue({ data: { state: 'ready' } })

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      await wrapper.vm.refreshJobStatus()

      expect(apiService.getMultiMaterialJobStatus).toHaveBeenCalled()

      wrapper.unmount()
    })

    it('disables start button when printing', () => {
      mockStatusStore.printData.job_status.is_printing = true

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      const buttons = wrapper.findAll('button')
      const startButton = buttons.find(btn =>
        btn.text().includes('Start Print Job')
      )
      expect(startButton.element.disabled).toBe(true)

      wrapper.unmount()
    })

    it('disables cancel button when not printing', () => {
      mockStatusStore.printData.job_status.is_printing = false

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      const buttons = wrapper.findAll('button')
      const cancelButton = buttons.find(btn =>
        btn.text().includes('Cancel Print Job')
      )
      expect(cancelButton.element.disabled).toBe(true)

      wrapper.unmount()
    })
  })

  describe('CLI File Management', () => {
    it('renders CLI upload section', () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      // Check for CLI section title
      expect(wrapper.text()).toContain('CLI Layer Preview')
      expect(wrapper.text()).toContain('Upload multi-layer CLI files')

      // Check for CLI file input
      const cliFileInput = wrapper.find('input[accept=".cli,application/octet-stream"]')
      expect(cliFileInput.exists()).toBe(true)

      wrapper.unmount()
    })

    it('handles CLI file selection', async () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      const cliFileInput = wrapper.find('input[accept=".cli,application/octet-stream"]')

      // Create a mock file
      const mockFile = new File(['test content'], 'test.cli', { type: 'application/octet-stream' })

      // Mock the file input change event
      Object.defineProperty(cliFileInput.element, 'files', {
        value: [mockFile],
        writable: false
      })

      await cliFileInput.trigger('change')
      await wrapper.vm.$nextTick()

      // Check that file is selected
      expect(wrapper.vm.selectedCliFile).toBe(mockFile)
      expect(wrapper.text()).toContain('test.cli')

      wrapper.unmount()
    })

    it('uploads CLI file successfully', async () => {
      // Mock successful API response
      apiService.uploadCliFile.mockResolvedValue({
        data: {
          success: true,
          message: 'CLI file uploaded successfully',
          file_id: 'test-file-id',
          total_layers: 5,
          file_size: 1024
        }
      })

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      // Set a selected file
      const mockFile = new File(['test content'], 'test.cli', { type: 'application/octet-stream' })
      wrapper.vm.selectedCliFile = mockFile

      await wrapper.vm.$nextTick()

      // Find and click upload button
      const uploadButton = wrapper.findAll('button').find(btn =>
        btn.text().includes('Upload & Parse CLI')
      )
      expect(uploadButton.exists()).toBe(true)

      await uploadButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Check that API was called
      expect(apiService.uploadCliFile).toHaveBeenCalledWith(mockFile)

      // Check that CLI info is set
      expect(wrapper.vm.cliFileInfo).toEqual({
        success: true,
        message: 'CLI file uploaded successfully',
        file_id: 'test-file-id',
        total_layers: 5,
        file_size: 1024
      })

      // Check that endLayerNum defaults to total_layers
      expect(wrapper.vm.endLayerNum).toBe(5)
      expect(wrapper.vm.selectedLayerNum).toBe(1)

      wrapper.unmount()
    })

    it('previews CLI layer successfully', async () => {
      // Mock successful API response
      const mockBlob = new Blob(['fake image data'], { type: 'image/png' })
      apiService.getCliLayerPreview.mockResolvedValue({
        data: mockBlob
      })

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      // Set CLI file info (as if file was uploaded)
      wrapper.vm.cliFileInfo = {
        file_id: 'test-file-id',
        total_layers: 5
      }
      wrapper.vm.selectedLayerNum = 3

      await wrapper.vm.$nextTick()

      // Find and click preview button
      const previewButton = wrapper.findAll('button').find(btn =>
        btn.text().includes('Preview Layer')
      )
      expect(previewButton.exists()).toBe(true)

      await previewButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Check that API was called with correct parameters
      expect(apiService.getCliLayerPreview).toHaveBeenCalledWith('test-file-id', 3)

      wrapper.unmount()
    })

    it('clears CLI file selection', async () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      // Set some CLI data
      const mockFile = new File(['test content'], 'test.cli', { type: 'application/octet-stream' })
      wrapper.vm.selectedCliFile = mockFile
      wrapper.vm.cliFileInfo = { file_id: 'test-id', total_layers: 3 }
      wrapper.vm.selectedLayerNum = 2

      await wrapper.vm.$nextTick()

      // Find and click clear button
      const clearButton = wrapper.findAll('button').find(btn =>
        btn.text().includes('Clear') && btn.element.closest('.cli-upload-section')
      )
      expect(clearButton.exists()).toBe(true)

      await clearButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Check that data is cleared
      expect(wrapper.vm.selectedCliFile).toBe(null)
      expect(wrapper.vm.cliFileInfo).toBe(null)
      expect(wrapper.vm.selectedLayerNum).toBe(1)

      wrapper.unmount()
    })
  })

  describe('CLI Layer Range Selection', () => {
    beforeEach(() => {
      apiService.uploadCliFileToDrum.mockResolvedValue({
        data: { file_id: 'range-ok', total_layers: 10 }
      })
    })

    it('renders CLI layer range selection UI when CLI file is uploaded', async () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      // Set CLI file info to show range selection
      wrapper.vm.cliFileInfo = { file_id: 'test-id', total_layers: 10 }
      await wrapper.vm.$nextTick()

      // Check if range selection card is visible
      const rangeCard = wrapper.findAll('.control-card').find(card =>
        card.text().includes('CLI Layer Selection')
      )
      expect(rangeCard.exists()).toBe(true)

      // Check for start and end layer inputs
      const startInput = wrapper.find('#start-layer-input')
      const endInput = wrapper.find('#end-layer-input')
      expect(startInput.exists()).toBe(true)
      expect(endInput.exists()).toBe(true)

      // Check for target drum dropdown
      const drumSelect = wrapper.find('#range-target-drum-select')
      expect(drumSelect.exists()).toBe(true)

      wrapper.unmount()
    })

    it('validates layer range inputs correctly', async () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      wrapper.vm.cliFileInfo = { file_id: 'test-id', total_layers: 10 }
      await wrapper.vm.$nextTick()

      // Test that start layer defaults to 1
      expect(wrapper.vm.startLayerNum).toBe(1)
      expect(wrapper.vm.endLayerNum).toBe(1)

      // Set valid range
      wrapper.vm.startLayerNum = 3
      wrapper.vm.endLayerNum = 7
      await wrapper.vm.$nextTick()

      // Check that send button is enabled with valid range
      const sendButton = wrapper.findAll('button').find(btn =>
        btn.text().includes('Send Layer Range')
      )
      expect(sendButton.exists()).toBe(true)

      wrapper.unmount()
    })

    it('disables send button for invalid ranges', async () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      wrapper.vm.cliFileInfo = { file_id: 'test-id', total_layers: 10 }
      await wrapper.vm.$nextTick()

      // Set invalid range (start > end)
      wrapper.vm.startLayerNum = 7
      wrapper.vm.endLayerNum = 3
      await wrapper.vm.$nextTick()

      const sendButton = wrapper.findAll('button').find(btn =>
        btn.text().includes('Send Layer Range')
      )
      expect(sendButton.attributes('disabled')).toBeDefined()

      wrapper.unmount()
    })

    it('calls sendCliLayerRangeToDrum API with correct parameters', async () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      wrapper.vm.cliFileInfo = { file_id: 'test-file-id', total_layers: 10 }
      wrapper.vm.startLayerNum = 2
      wrapper.vm.endLayerNum = 5
      wrapper.vm.rangeTargetDrumId = '1'
      await wrapper.vm.$nextTick()

      // Call the method directly
      await wrapper.vm.sendCliLayerRangeToDrum()

      expect(apiService.uploadCliFileToDrum).toHaveBeenCalled()

      wrapper.unmount()
    })

    it('shows loading state during range operation', async () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      wrapper.vm.cliFileInfo = { file_id: 'test-id', total_layers: 10 }
      wrapper.vm.isRangeOperationLoading = true
      await wrapper.vm.$nextTick()

      const sendButton = wrapper.findAll('button').find(btn =>
        btn.text().includes('Sending...')
      )
      expect(sendButton.exists()).toBe(true)
      expect(sendButton.attributes('disabled')).toBeDefined()

      wrapper.unmount()
    })

    it('validates layer numbers are within CLI file bounds', async () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      wrapper.vm.cliFileInfo = { file_id: 'test-id', total_layers: 5 }
      await wrapper.vm.$nextTick()

      // Check that input max attributes are set correctly
      const startInput = wrapper.find('#start-layer-input')
      const endInput = wrapper.find('#end-layer-input')

      expect(startInput.attributes('max')).toBe('5')
      expect(endInput.attributes('max')).toBe('5')
      expect(startInput.attributes('min')).toBe('1')
      expect(endInput.attributes('min')).toBe('1')

      wrapper.unmount()
    })

    it('shows success message after successful range send', async () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      wrapper.vm.cliFileInfo = { file_id: 'test-id', total_layers: 10 }
      wrapper.vm.startLayerNum = 2
      wrapper.vm.endLayerNum = 4
      wrapper.vm.rangeTargetDrumId = '0'
      await wrapper.vm.$nextTick()

      await wrapper.vm.sendCliLayerRangeToDrum()

      // Check for success message
      expect(wrapper.vm.successMessage).toContain('Drum 0:')

      wrapper.unmount()
    })

    it('handles API errors gracefully', async () => {
      apiService.uploadCliFileToDrum.mockRejectedValue(new Error('Network error'))

      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      wrapper.vm.cliFileInfo = { file_id: 'test-id', total_layers: 10 }
      wrapper.vm.startLayerNum = 1
      wrapper.vm.endLayerNum = 3
      wrapper.vm.rangeTargetDrumId = '2'
      await wrapper.vm.$nextTick()

      await wrapper.vm.sendCliLayerRangeToDrum()

      // Check for error message
      expect(wrapper.vm.errorMessage).toContain('Failed to cache CLI for drum (range)')

      wrapper.unmount()
    })

    it('requires all fields to be filled before enabling send button', async () => {
      const wrapper = mount(PrintView, {
        attachTo: document.body
      })

      wrapper.vm.cliFileInfo = { file_id: 'test-id', total_layers: 10 }
      await wrapper.vm.$nextTick()

      const sendButton = wrapper.findAll('button').find(btn =>
        btn.text().includes('Send Layer Range')
      )

      // Initially disabled (no drum selected)
      expect(sendButton.attributes('disabled')).toBeDefined()

      // Set start and end layers but no drum
      wrapper.vm.startLayerNum = 1
      wrapper.vm.endLayerNum = 3
      await wrapper.vm.$nextTick()
      expect(sendButton.attributes('disabled')).toBeDefined()

      // Set drum to enable button
      wrapper.vm.rangeTargetDrumId = '1'
      await wrapper.vm.$nextTick()
      expect(sendButton.attributes('disabled')).toBeUndefined()

      wrapper.unmount()
    })
  })
})