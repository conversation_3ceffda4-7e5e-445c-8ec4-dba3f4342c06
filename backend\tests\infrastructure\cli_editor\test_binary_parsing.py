"""
Tests for Binary CLI Parsing
============================

This module contains tests for binary CLI file parsing, including:
- Basic binary parsing
- Aligned binary format
- Binary header and geometry parsing
"""

import pytest
import struct
import io
from typing import List, Tuple
from unittest.mock import Mock, patch

from infrastructure.cli_editor.editor import (
    Editor,
    ParsedCliFile,
    CliLayer,
    Polyline,
    Hatch,
    Point,
    CliParsingError
)
from infrastructure.cli_editor.cli_exceptions import CliGenerationError


class TestBinaryCliParsing:
    """Test suite for binary CLI file parsing."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def create_binary_cli(self, layers_data: List[dict], header_lines: List[str] = None, is_aligned: bool = False) -> bytes:
        """Helper method to create binary CLI data for testing."""
        stream = io.BytesIO()

        # Write header
        if header_lines is None:
            header_lines = ["$$HEADEREND"]

        header_str = "\n".join(header_lines)
        if not header_str.endswith("$$HEADEREND"):
            header_str += "\n$$HEADEREND"

        stream.write(header_str.encode('ascii'))

        # Write geometry data
        for layer_data in layers_data:
            # Layer command (127)
            stream.write(struct.pack("<H", 127))
            if is_aligned:
                stream.write(b'\x00\x00')
            stream.write(struct.pack("<f", layer_data['z_height']))

            # Polylines (130)
            for poly_data in layer_data.get('polylines', []):
                stream.write(struct.pack("<H", 130))
                if is_aligned:
                    stream.write(b'\x00\x00')
                stream.write(struct.pack("<iii", poly_data['part_id'], poly_data['direction'], len(poly_data['points'])))
                for point in poly_data['points']:
                    stream.write(struct.pack("<ff", point[0], point[1]))

            # Hatches (132)
            for hatch_data in layer_data.get('hatches', []):
                stream.write(struct.pack("<H", 132))
                if is_aligned:
                    stream.write(b'\x00\x00')
                stream.write(struct.pack("<ii", hatch_data['group_id'], len(hatch_data['lines'])))
                for line in hatch_data['lines']:
                    stream.write(struct.pack("<ffff", line[0][0], line[0][1], line[1][0], line[1][1]))

        return stream.getvalue()

    def test_binary_basic_parsing(self):
        """Test basic binary CLI parsing."""
        layers_data = [
            {
                'z_height': 0.0,
                'polylines': [
                    {
                        'part_id': 1,
                        'direction': 0,
                        'points': [(10.0, 5.0), (15.0, 5.0), (15.0, 10.0)]
                    }
                ],
                'hatches': [
                    {
                        'group_id': 1,
                        'lines': [((0.0, 0.0), (5.0, 5.0))]
                    }
                ]
            }
        ]

        binary_data = self.create_binary_cli(layers_data)
        result = self.parser.parse(binary_data)

        assert len(result.layers) == 1
        layer = result.layers[0]
        assert layer.z_height == 0.0
        assert len(layer.polylines) == 1
        assert len(layer.hatches) == 1

        polyline = layer.polylines[0]
        assert polyline.part_id == 1
        assert polyline.direction == 0
        assert len(polyline.points) == 3

    def test_binary_aligned_format(self):
        """Test binary CLI parsing with alignment."""
        layers_data = [{'z_height': 0.0}]
        header_lines = ["$$ALIGN", "$$HEADEREND"]

        binary_data = self.create_binary_cli(layers_data, header_lines, is_aligned=True)
        result = self.parser.parse(binary_data)

        assert result.is_aligned is True
        assert len(result.layers) == 1

    def test_binary_multiple_layers(self):
        """Test binary CLI with multiple layers."""
        layers_data = [
            {'z_height': 0.0},
            {'z_height': 16.0},
            {'z_height': 32.0}
        ]

        binary_data = self.create_binary_cli(layers_data)
        result = self.parser.parse(binary_data)

        assert len(result.layers) == 3
        assert result.layers[0].z_height == 0.0
        assert result.layers[1].z_height == 16.0
        assert result.layers[2].z_height == 32.0


class TestBinaryHeaderParsing:
    """Test suite for the _parse_binary_header method."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_parse_basic_binary_header(self):
        """Test parsing basic binary header."""
        header_text = "$$VERSION 200\n$$UNITS 1.0\n$$HEADEREND"
        header_bytes = header_text.encode('ascii')
        geometry_data = struct.pack("<H", 127) + struct.pack("<f", 0.0)  # Layer command

        binary_data = header_bytes + geometry_data
        stream = io.BytesIO(binary_data)

        header_lines, is_aligned = self.parser._parse_binary_header(stream)

        assert len(header_lines) == 3
        assert header_lines == ["$$VERSION 200", "$$UNITS 1.0", "$$HEADEREND"]
        assert is_aligned is False

        # Stream should be positioned after header
        remaining = stream.read()
        assert len(remaining) == 6  # 2 bytes command + 4 bytes float

    def test_parse_binary_header_with_alignment(self):
        """Test parsing binary header with alignment marker."""
        header_text = "$$ALIGN\n$$VERSION 200\n$$HEADEREND"
        header_bytes = header_text.encode('ascii')
        geometry_data = struct.pack("<H", 127) + b'\x00\x00' + struct.pack("<f", 0.0)  # Aligned layer

        binary_data = header_bytes + geometry_data
        stream = io.BytesIO(binary_data)

        header_lines, is_aligned = self.parser._parse_binary_header(stream)

        assert "$$ALIGN" in header_lines
        assert is_aligned is True

    def test_parse_binary_header_eof_error(self):
        """Test error when EOF reached before HEADEREND."""
        header_text = "$$VERSION 200\n$$UNITS 1.0"  # Missing $$HEADEREND
        binary_data = header_text.encode('ascii')
        stream = io.BytesIO(binary_data)

        with pytest.raises(CliParsingError, match="EOF reached before"):
            self.parser._parse_binary_header(stream)

    def test_parse_binary_header_too_large_error(self):
        """Test error when header size exceeds limit."""
        # Create header larger than max_header_size (8192 bytes)
        large_header = "$$VERSION 200\n" + "A" * 10000 + "\n$$HEADEREND"
        binary_data = large_header.encode('ascii')
        stream = io.BytesIO(binary_data)

        with pytest.raises(CliParsingError, match="Header size exceeds"):
            self.parser._parse_binary_header(stream)

    def test_decode_header_bytes_with_unicode_errors(self):
        """Test header decoding with unicode errors."""
        # Create header with some invalid ASCII bytes
        header_bytes = bytearray(b"$$VERSION 200\n$$HEADEREND")
        header_bytes[5] = 0xFF  # Invalid ASCII byte

        with patch.object(self.parser.logger, 'warning') as mock_warning:
            result = self.parser._decode_header_bytes(header_bytes)

            # Should log warning about unicode decode error
            mock_warning.assert_called_once()
            assert "Unicode decode error" in mock_warning.call_args[0][0]

            # Should still return a decoded string with replacement chars
            assert isinstance(result, str)
            # Check that the replacement character is present
            assert "\ufffd" in result or "�" in result


class TestBinaryGeometryParsing:
    """Test suite for the _parse_binary_geometry method."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def create_binary_geometry_data(self, commands: List[dict], is_aligned: bool = False) -> bytes:
        """Helper to create binary geometry data for testing."""
        data = b''

        for cmd in commands:
            if cmd['type'] == 'layer':
                data += struct.pack("<H", 127)
                if is_aligned:
                    data += b'\x00\x00'
                data += struct.pack("<f", cmd['z_height'])

            elif cmd['type'] == 'polyline':
                data += struct.pack("<H", 130)
                if is_aligned:
                    data += b'\x00\x00'
                data += struct.pack("<iii", cmd['part_id'], cmd['direction'], len(cmd['points']))
                for point in cmd['points']:
                    data += struct.pack("<ff", point[0], point[1])

            elif cmd['type'] == 'hatches':
                data += struct.pack("<H", 132)
                if is_aligned:
                    data += b'\x00\x00'
                data += struct.pack("<ii", cmd['group_id'], len(cmd['lines']))
                for line in cmd['lines']:
                    data += struct.pack("<ffff", line[0][0], line[0][1], line[1][0], line[1][1])

        return data

    def test_parse_basic_binary_geometry(self):
        """Test parsing basic binary geometry."""
        commands = [
            {'type': 'layer', 'z_height': 0.0},
            {'type': 'polyline', 'part_id': 1, 'direction': 0, 'points': [(10.0, 5.0), (15.0, 10.0)]}
        ]

        binary_data = self.create_binary_geometry_data(commands)
        stream = io.BytesIO(binary_data)

        layers = self.parser._parse_binary_geometry(stream, is_aligned=False)

        assert len(layers) == 1
        assert layers[0].z_height == 0.0
        assert len(layers[0].polylines) == 1

        polyline = layers[0].polylines[0]
        assert polyline.part_id == 1
        assert polyline.direction == 0
        assert len(polyline.points) == 2
        assert polyline.points[0].x == 10.0
        assert polyline.points[0].y == 5.0

    def test_parse_binary_geometry_with_alignment(self):
        """Test parsing binary geometry with alignment."""
        commands = [
            {'type': 'layer', 'z_height': 16.0},
            {'type': 'hatches', 'group_id': 1, 'lines': [((0.0, 0.0), (5.0, 5.0))]}
        ]

        binary_data = self.create_binary_geometry_data(commands, is_aligned=True)
        stream = io.BytesIO(binary_data)

        layers = self.parser._parse_binary_geometry(stream, is_aligned=True)

        assert len(layers) == 1
        assert layers[0].z_height == 16.0
        assert len(layers[0].hatches) == 1

        hatch = layers[0].hatches[0]
        assert hatch.group_id == 1
        assert len(hatch.lines) == 1

    def test_parse_binary_geometry_multiple_layers(self):
        """Test parsing multiple layers in binary geometry."""
        commands = [
            {'type': 'layer', 'z_height': 0.0},
            {'type': 'polyline', 'part_id': 1, 'direction': 0, 'points': [(10.0, 5.0)]},
            {'type': 'layer', 'z_height': 16.0},
            {'type': 'hatches', 'group_id': 1, 'lines': [((0.0, 0.0), (5.0, 5.0))]},
            {'type': 'layer', 'z_height': 32.0}
        ]

        binary_data = self.create_binary_geometry_data(commands)
        stream = io.BytesIO(binary_data)

        layers = self.parser._parse_binary_geometry(stream, is_aligned=False)

        assert len(layers) == 3
        assert layers[0].z_height == 0.0
        assert layers[1].z_height == 16.0
        assert layers[2].z_height == 32.0

        assert len(layers[0].polylines) == 1
        assert len(layers[1].hatches) == 1
        assert len(layers[2].polylines) == 0
        assert len(layers[2].hatches) == 0

    def test_parse_binary_geometry_unsupported_command(self):
        """Test handling of unsupported command code."""
        # Create binary data with unsupported command code 999
        binary_data = struct.pack("<H", 999) + b'\x00\x00\x00\x00'
        stream = io.BytesIO(binary_data)

        with patch.object(self.parser.logger, 'warning') as mock_warning:
            layers = self.parser._parse_binary_geometry(stream, is_aligned=False)

            # Should log warning about unsupported command
            mock_warning.assert_called_once()
            assert "Unsupported command code 999" in mock_warning.call_args[0][0]

        # Should return empty layers list
        assert len(layers) == 0

    def test_parse_binary_geometry_eof_handling(self):
        """Test proper EOF handling in binary geometry parsing."""
        commands = [
            {'type': 'layer', 'z_height': 0.0},
            {'type': 'polyline', 'part_id': 1, 'direction': 0, 'points': [(10.0, 5.0)]}
        ]

        binary_data = self.create_binary_geometry_data(commands)
        stream = io.BytesIO(binary_data)

        with patch.object(self.parser.logger, 'info') as mock_info:
            layers = self.parser._parse_binary_geometry(stream, is_aligned=False)

            # Should log info about reaching end of data
            mock_info.assert_called_once()
            assert "Successfully reached end" in mock_info.call_args[0][0]

        assert len(layers) == 1


class TestBinaryCommandParsing:
    """Test suite for individual binary command parsing methods."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_read_command_code_without_alignment(self):
        """Test reading command code without alignment."""
        binary_data = struct.pack("<H", 127)  # Layer command
        stream = io.BytesIO(binary_data)

        command_code = self.parser._read_command_code(stream, is_aligned=False)

        assert command_code == 127
        assert stream.tell() == 2  # Should have read 2 bytes

    def test_read_command_code_with_alignment(self):
        """Test reading command code with alignment."""
        binary_data = struct.pack("<H", 127) + b'\\x00\\x00'  # Layer command + alignment
        stream = io.BytesIO(binary_data)

        command_code = self.parser._read_command_code(stream, is_aligned=True)

        assert command_code == 127
        assert stream.tell() == 4  # Should have read 4 bytes (2 + 2 alignment)

    def test_parse_binary_layer_command(self):
        """Test parsing binary layer command."""
        binary_data = struct.pack("<f", 25.5)  # Z height
        stream = io.BytesIO(binary_data)

        layer = self.parser._parse_binary_layer_command(stream)

        assert layer.z_height == 25.5
        assert len(layer.polylines) == 0
        assert len(layer.hatches) == 0

    def test_parse_binary_polyline_command(self):
        """Test parsing binary polyline command."""
        # part_id=1, direction=0, num_points=2, then 2 points
        binary_data = (struct.pack("<iii", 1, 0, 2) +
                      struct.pack("<ff", 10.0, 5.0) +
                      struct.pack("<ff", 15.0, 10.0))
        stream = io.BytesIO(binary_data)

        current_layer = CliLayer(z_height=0.0, polylines=[], hatches=[])
        self.parser._parse_binary_polyline_command(stream, current_layer)

        assert len(current_layer.polylines) == 1
        polyline = current_layer.polylines[0]
        assert polyline.part_id == 1
        assert polyline.direction == 0
        assert len(polyline.points) == 2
        assert polyline.points[0].x == 10.0
        assert polyline.points[1].y == 10.0

    def test_parse_binary_polyline_command_no_layer_error(self):
        """Test error when polyline command comes before layer."""
        binary_data = struct.pack("<iii", 1, 0, 1) + struct.pack("<ff", 10.0, 5.0)
        stream = io.BytesIO(binary_data)

        with pytest.raises(CliParsingError, match="Found Polyline data before a Layer"):
            self.parser._parse_binary_polyline_command(stream, None)

    def test_parse_binary_hatches_command(self):
        """Test parsing binary hatches command."""
        # group_id=1, num_lines=1, then 1 line (x1,y1,x2,y2)
        binary_data = (struct.pack("<ii", 1, 1) +
                      struct.pack("<ffff", 0.0, 0.0, 5.0, 5.0))
        stream = io.BytesIO(binary_data)

        current_layer = CliLayer(z_height=0.0, polylines=[], hatches=[])
        self.parser._parse_binary_hatches_command(stream, current_layer)

        assert len(current_layer.hatches) == 1
        hatch = current_layer.hatches[0]
        assert hatch.group_id == 1
        assert len(hatch.lines) == 1
        assert hatch.lines[0][0].x == 0.0
        assert hatch.lines[0][1].x == 5.0

    def test_parse_binary_hatches_command_no_layer_error(self):
        """Test error when hatches command comes before layer."""
        binary_data = struct.pack("<ii", 1, 1) + struct.pack("<ffff", 0.0, 0.0, 5.0, 5.0)
        stream = io.BytesIO(binary_data)

        with pytest.raises(CliParsingError, match="Found Hatch data before a Layer"):
            self.parser._parse_binary_hatches_command(stream, None)

    def test_read_binary_points(self):
        """Test reading binary points."""
        binary_data = (struct.pack("<ff", 10.0, 5.0) +
                      struct.pack("<ff", 15.0, 10.0) +
                      struct.pack("<ff", 20.0, 15.0))
        stream = io.BytesIO(binary_data)

        points = self.parser._read_binary_points(stream, 3)

        assert len(points) == 3
        assert points[0].x == 10.0
        assert points[0].y == 5.0
        assert points[2].x == 20.0
        assert points[2].y == 15.0

    def test_read_binary_hatch_lines(self):
        """Test reading binary hatch lines."""
        binary_data = (struct.pack("<ffff", 0.0, 0.0, 5.0, 5.0) +
                      struct.pack("<ffff", 10.0, 10.0, 15.0, 15.0))
        stream = io.BytesIO(binary_data)

        hatch_lines = self.parser._read_binary_hatch_lines(stream, 2)

        assert len(hatch_lines) == 2
        assert hatch_lines[0][0].x == 0.0
        assert hatch_lines[0][1].y == 5.0
        assert hatch_lines[1][0].x == 10.0
        assert hatch_lines[1][1].y == 15.0
