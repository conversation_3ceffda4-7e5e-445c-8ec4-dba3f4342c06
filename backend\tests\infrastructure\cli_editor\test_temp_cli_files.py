"""
Test suite for temporary CLI file handling functionality.

This module tests the temporary CLI file creation and cleanup functionality
that allows verification of generated ASCII CLI files.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, Mock

from app.api.print import create_temp_cli_file, cleanup_temp_cli_file, TEMP_CLI_DIR


class TestTempCliFileHandling:
    """Test temporary CLI file creation and cleanup."""

    def setup_method(self):
        """Set up test fixtures."""
        # Ensure clean state for each test
        if TEMP_CLI_DIR.exists():
            shutil.rmtree(TEMP_CLI_DIR)

    def teardown_method(self):
        """Clean up after each test."""
        # Clean up temp directory after each test
        if TEMP_CLI_DIR.exists():
            shutil.rmtree(TEMP_CLI_DIR)

    def test_create_temp_cli_file_basic(self):
        """Test basic temporary CLI file creation."""
        test_data = b"$$HEADERSTART\n$$ASCII\n$$HEADEREND\n$$LAYER/16.0\n"
        
        temp_file_path = create_temp_cli_file(test_data, "test_layer")
        
        # Check that file was created
        assert temp_file_path.exists()
        assert temp_file_path.parent == TEMP_CLI_DIR
        assert temp_file_path.name.startswith("test_layer_")
        assert temp_file_path.suffix == ".cli"
        
        # Check file contents
        with open(temp_file_path, 'rb') as f:
            content = f.read()
        assert content == test_data

    def test_create_temp_cli_file_creates_directory(self):
        """Test that temp directory is created if it doesn't exist."""
        # Ensure directory doesn't exist
        assert not TEMP_CLI_DIR.exists()
        
        test_data = b"test cli data"
        temp_file_path = create_temp_cli_file(test_data)
        
        # Check that directory was created
        assert TEMP_CLI_DIR.exists()
        assert TEMP_CLI_DIR.is_dir()
        assert temp_file_path.exists()

    def test_create_temp_cli_file_unique_names(self):
        """Test that multiple calls create files with unique names."""
        test_data = b"test data"
        
        file1 = create_temp_cli_file(test_data, "test")
        file2 = create_temp_cli_file(test_data, "test")
        
        assert file1 != file2
        assert file1.exists()
        assert file2.exists()
        assert file1.name != file2.name

    def test_cleanup_temp_cli_file_success(self):
        """Test successful cleanup of temporary file."""
        test_data = b"test data"
        temp_file_path = create_temp_cli_file(test_data)
        
        # Verify file exists
        assert temp_file_path.exists()
        
        # Clean up file
        result = cleanup_temp_cli_file(temp_file_path)
        
        # Verify cleanup was successful
        assert result is True
        assert not temp_file_path.exists()

    def test_cleanup_temp_cli_file_nonexistent(self):
        """Test cleanup of non-existent file."""
        nonexistent_path = TEMP_CLI_DIR / "nonexistent.cli"
        
        result = cleanup_temp_cli_file(nonexistent_path)
        
        # Should return False but not raise exception
        assert result is False

    def test_cleanup_temp_cli_file_permission_error(self):
        """Test cleanup when file deletion fails."""
        test_data = b"test data"
        temp_file_path = create_temp_cli_file(test_data)

        # Mock the pathlib.Path.unlink method to raise permission error
        with patch('pathlib.Path.unlink', side_effect=PermissionError("Access denied")):
            result = cleanup_temp_cli_file(temp_file_path)

            # Should return False and log error
            assert result is False

    def test_temp_file_contains_valid_ascii_cli(self):
        """Test that temporary file contains valid ASCII CLI format."""
        # Create realistic ASCII CLI data
        cli_data = (
            "$$HEADERSTART\n"
            "$$ASCII\n"
            "$$UNITS/00000000.005000\n"
            "$$VERSION/200\n"
            "$$LAYERS/000001\n"
            "$$HEADEREND\n"
            "$$GEOMETRYSTART\n"
            "$$LAYER/16.0\n"
            "$$POLYLINE/1,1,3,100.00000,200.00000,150.00000,250.00000,200.00000,200.00000\n"
            "$$HATCHES/1,2,50.00000,50.00000,100.00000,100.00000,60.00000,60.00000,110.00000,110.00000\n"
        ).encode('ascii')
        
        temp_file_path = create_temp_cli_file(cli_data, "ascii_test")
        
        # Read file and verify it's valid ASCII
        with open(temp_file_path, 'r', encoding='ascii') as f:
            content = f.read()
        
        # Check key CLI format elements
        assert "$$HEADERSTART" in content
        assert "$$ASCII" in content
        assert "$$LAYER/16.0" in content
        assert "$$POLYLINE/" in content
        assert "$$HATCHES/" in content

    def test_temp_file_prefix_in_filename(self):
        """Test that file prefix appears in the generated filename."""
        test_data = b"test data"
        prefix = "custom_prefix_test"
        
        temp_file_path = create_temp_cli_file(test_data, prefix)
        
        assert prefix in temp_file_path.name
        assert temp_file_path.name.startswith(prefix)
