import pytest
import pytest_asyncio
from unittest.mock import AsyncMock

from app.services.opcua import OPCUAService


@pytest_asyncio.fixture
async def service():
    svc = OPCUAService()
    yield svc
    try:
        await svc.shutdown()
    except Exception:
        pass


@pytest.mark.asyncio
async def test_write_read_when_server_not_running(service: OPCUAService):
    # Ensure not running
    assert not service.is_server_running

    # write should fail, read should return None
    ok = await service.write_variable("job_active", True)
    assert ok is False
    val = await service.read_variable("job_active")
    assert val is None


@pytest.mark.asyncio
async def test_connect_returns_false_when_start_server_fails(monkeypatch):
    svc = OPCUAService()

    async def fake_start_server():
        return False

    # Ensure server is not running
    assert not svc.is_server_running

    monkeypatch.setattr(svc, "start_server", fake_start_server)
    ok = await svc.connect()
    assert ok is False


@pytest.mark.asyncio
async def test_monitoring_start_and_stop(service: OPCUAService):
    # Connect triggers start_monitoring via CoordinationMixin.connect
    ok = await service.connect()
    assert ok is True
    assert service.monitoring_active is True

    # Explicit stop
    await service.stop_monitoring()
    assert service.monitoring_active is False

    # Restart monitoring explicitly
    await service.start_monitoring(0.01)
    assert service.monitoring_active is True

    # Shutdown should stop monitoring
    await service.shutdown()
    assert service.monitoring_active is False

