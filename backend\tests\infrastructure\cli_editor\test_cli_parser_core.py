"""
Core Tests for CLI Parser
=========================

This module contains core tests for the CLI parser service and
coordinate parsing helper methods.
"""

import pytest
from unittest.mock import Mock

from infrastructure.cli_editor.editor import (
    Editor,
    <PERSON>rsed<PERSON>liFile,
    <PERSON>liLayer,
    <PERSON><PERSON><PERSON>,
    Hatch,
    Point,
    CliParsingError
)
from infrastructure.cli_editor.cli_exceptions import CliGenerationError


class TestCliParserService:
    """Test suite for CliParserService."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_parser_initialization(self):
        """Test that the parser initializes correctly."""
        assert self.parser is not None
        assert self.parser.logger is not None

    def test_parser_with_custom_logger(self):
        """Test parser initialization with custom logger."""
        mock_logger = Mock()
        parser = Editor(logger=mock_logger)
        assert parser.logger == mock_logger


class TestCoordinateParsing:
    """Test suite for coordinate parsing helper methods."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_parse_inline_coordinates_valid(self):
        """Test parsing valid inline coordinates."""
        coord_parts = ["10.0", "5.0", "15.0", "10.0", "20.0", "15.0"]

        points = self.parser._parse_inline_coordinates(coord_parts, 3, 0, "polyline")

        assert len(points) == 3
        assert points[0].x == 10.0
        assert points[0].y == 5.0
        assert points[2].x == 20.0
        assert points[2].y == 15.0

    def test_parse_inline_coordinates_insufficient_error(self):
        """Test error when insufficient inline coordinates."""
        coord_parts = ["10.0", "5.0", "15.0"]  # Only 3 coords for 2 points

        with pytest.raises(CliParsingError, match="Insufficient coordinates"):
            self.parser._parse_inline_coordinates(coord_parts, 2, 5, "polyline")

    def test_parse_inline_coordinates_invalid_values_error(self):
        """Test error when inline coordinates have invalid values."""
        coord_parts = ["10.0", "invalid", "15.0", "10.0"]

        with pytest.raises(CliParsingError, match="Invalid coordinates"):
            self.parser._parse_inline_coordinates(coord_parts, 2, 0, "polyline")

    def test_parse_multiline_coordinates_valid(self):
        """Test parsing valid multiline coordinates."""
        geom_lines = [
            "$$POLYLINE 1 0 2",
            "10.0 5.0",
            "15.0 10.0"
        ]

        points = self.parser._parse_multiline_coordinates(geom_lines, 0, 2, 2, "polyline")

        assert len(points) == 2
        assert points[0].x == 10.0
        assert points[0].y == 5.0
