"""
Mock Leveler Control Methods
============================

This module contains mock leveler control methods for the MockRecoaterClient.
These methods simulate the leveler control endpoints from the hardware API.
"""

import random
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class MockLevelerControlMixin:
    """Mixin class providing mock leveler control methods for MockRecoaterClient."""

    def get_leveler_pressure(self) -> Dict[str, Any]:
        """
        Mock implementation for getting leveler pressure.

        Returns:
            Mock leveler pressure information
        """
        logger.info("Mock get leveler pressure")

        return {
            "maximum": 10.0,  # Maximum pressure in Pa
            "target": random.uniform(2.0, 8.0),  # Target pressure in Pa
            "value": random.uniform(1.8, 8.2)  # Current pressure in Pa
        }

    def set_leveler_pressure(self, target: float) -> Dict[str, Any]:
        """
        Mock implementation for setting leveler pressure.

        Args:
            target: Target pressure in Pa

        Returns:
            Mock pressure response
        """
        logger.info(f"Mock set leveler pressure: target={target}")

        return {
            "success": True,
            "target_pressure": target,
            "unit": "Pa"
        }

    def get_leveler_sensor(self) -> Dict[str, Any]:
        """
        Mock implementation for getting leveler sensor state.

        Returns:
            Mock sensor state
        """
        logger.info("Mock get leveler sensor state")

        return {
            "state": random.choice([True, False])  # Randomly return true or false
        }
