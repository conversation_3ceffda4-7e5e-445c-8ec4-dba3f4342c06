import os
import importlib


def test_get_job_config_loads_from_env(monkeypatch):
    # Arrange: set env vars to unique values for the test
    monkeypatch.setenv("JOB_DRUM_UPLOAD_DELAY_SECONDS", "1.5")
    monkeypatch.setenv("JOB_STATUS_POLL_INTERVAL_SECONDS", "1.0")
    monkeypatch.setenv("JOB_READY_TIMEOUT_SECONDS", "10.0")
    monkeypatch.setenv("JOB_COMPLETION_TIMEOUT_SECONDS", "20.0")
    monkeypatch.setenv("JOB_EMPTY_LAYER_TEMPLATE_PATH", "C:/tmp/empty_layer.cli")

    # Force reload to pick up env
    if "app.config.job_config" in importlib.sys.modules:
        importlib.reload(importlib.import_module("app.config.job_config"))

    from app.config.job_config import get_job_config

    # Act
    cfg = get_job_config()

    # Assert
    assert cfg.DRUM_UPLOAD_DELAY_SECONDS == 1.5
    assert cfg.STATUS_POLL_INTERVAL_SECONDS == 1.0
    assert cfg.READY_TIMEOUT_SECONDS == 10.0
    assert cfg.COMPLETION_TIMEOUT_SECONDS == 20.0
    import os
    expected_path = os.path.abspath("C:/tmp/empty_layer.cli")
    assert os.path.normcase(os.path.normpath(cfg.EMPTY_LAYER_TEMPLATE_PATH)) == os.path.normcase(os.path.normpath(expected_path))

