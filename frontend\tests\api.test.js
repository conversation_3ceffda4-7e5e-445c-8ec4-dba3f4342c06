/**
 * Tests for API Service
 * =====================
 *
 * This module contains tests for the API service structure.
 */

import { describe, it, expect } from 'vitest'
import apiService from '../src/services/api.js'

describe('API Service', () => {
  it('should have all required API methods', () => {
    // Test that the API service has the expected methods
    expect(typeof apiService.getStatus).toBe('function')
    expect(typeof apiService.getHealth).toBe('function')
    expect(typeof apiService.getConfig).toBe('function')
    expect(typeof apiService.setConfig).toBe('function')
    expect(typeof apiService.getDrums).toBe('function')
    expect(typeof apiService.getDrum).toBe('function')
  })

  it('should be an object with API methods', () => {
    // Verify the service is properly structured
    expect(typeof apiService).toBe('object')
    expect(apiService).not.toBeNull()

    // Verify all methods exist
    const expectedMethods = ['getStatus', 'getHealth', 'getConfig', 'setConfig', 'getDrums', 'getDrum']
    expectedMethods.forEach(method => {
      expect(apiService).toHaveProperty(method)
      expect(typeof apiService[method]).toBe('function')
    })
  })

  it('should have proper method signatures', () => {
    // Test method signatures (number of parameters)
    expect(apiService.getStatus.length).toBe(0)
    expect(apiService.getHealth.length).toBe(0)
    expect(apiService.getConfig.length).toBe(0)
    expect(apiService.setConfig.length).toBe(1)
    expect(apiService.getDrums.length).toBe(0)
    expect(apiService.getDrum.length).toBe(1)
  })

  it('should have CLI file management methods', () => {
    // Test CLI-related methods exist
    expect(typeof apiService.uploadCliFile).toBe('function')
    expect(typeof apiService.getCliLayerPreview).toBe('function')
    expect(typeof apiService.uploadCliFileToDrum).toBe('function')

    // Test method signatures
    expect(apiService.uploadCliFile.length).toBe(1)
    expect(apiService.getCliLayerPreview.length).toBe(2)
    expect(apiService.uploadCliFileToDrum.length).toBe(2)
  })
})
