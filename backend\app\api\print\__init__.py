"""
Initialization file for print API module.
No endpoints defined here.
"""

import logging
from fastapi import APIRouter

logger = logging.getLogger(__name__)

# Every module that defines endpoints must create its own router before registering endpoints on it
router = APIRouter(prefix="/print", tags=["print"])

# Import sub-routers (no prefixes inside the modules)
from .job import router as job_router  # noqa: E402
from .layer import router as layer_router  # noqa: E402
from .cli import router as cli_router  # noqa: E402
from .drum import router as drum_router  # noqa: E402
from .multimaterial import router as multimaterial_router  # noqa: E402

# Export utility functions and dependencies for backward compatibility with tests
from .utils import (  # noqa: E402
    create_temp_cli_file,
    cleanup_temp_cli_file,
    cleanup_all_temp_cli_files,
    TEMP_CLI_DIR,
)
# Re-export dependency providers so tests can patch via 'app.api.print.*'
from app.dependencies import (  # noqa: E402
    get_recoater_client,
    get_multilayer_job_manager,
)

# Include routers
router.include_router(layer_router)
router.include_router(drum_router)
router.include_router(job_router)
router.include_router(cli_router)
router.include_router(multimaterial_router)

