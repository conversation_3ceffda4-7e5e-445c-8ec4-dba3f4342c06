"""
OPC UA Configuration
===================

Configuration settings for OPC UA server hosting simplified coordination variables
for backend-PLC communication in multi-material print job system.

Configuration Components:
------------------------

1. OPCUAServerConfig - Server endpoint and connection settings
   - endpoint: OPC UA server endpoint URL (opc.tcp://host:port/path)
   - server_name: Human-readable server name for identification
   - namespace_uri: Unique namespace identifier for variables
   - namespace_idx: Numeric index assigned to namespace (typically 2)
   - security_policy/mode: Security configuration (None for internal networks)
   - certificate/private_key paths: For secure connections
   - connection_timeout: Client connection timeout in seconds
   - session_timeout: OPC UA session timeout in seconds
   - auto_restart: Enable automatic server restart on failure
   - restart_delay: Delay between restart attempts
   - max_restart_attempts: Maximum number of restart attempts

2. CoordinationVariable - Variable definition template
   - name: Human-readable variable name for backend code
   - node_id: OPC UA node identifier (ns=X;s=variable_name format)
   - data_type: OPC UA data type (<PERSON>olean, String, Int32, Float, DateTime)
   - initial_value: Starting value when server creates the variable
   - writable: Whether PLC clients can write to this variable
   - description: Documentation for variable purpose

3. COORDINATION_VARIABLES - Simplified variable set for backend-PLC coordination

   Job Control Variables (Backend managed):
   - job_active: Backend sets TRUE at start, FALSE at end
   - total_layers: Backend sets once at job start
   - current_layer: Backend manages, PLC reads

   Recoater Coordination Variables (Backend managed):
   - recoater_ready_to_print: Backend writes when Aerosint is ready
   - recoater_layer_complete: Backend writes when deposition complete

   Error Handling Variables (Bidirectional):
   - backend_error: Backend writes if any issue arises
   - plc_error: PLC writes if any issues

4. Helper Functions:
   - get_opcua_config(): Load configuration from environment variables
   - get_variable_by_name(): Lookup variable definition by name
   - get_variables_by_type(): Filter variables by OPC UA data type

Environment Variable Configuration:
----------------------------------
All configuration can be overridden via environment variables in .env file:

Server Configuration:
- OPCUA_SERVER_ENDPOINT: Server endpoint URL
- OPCUA_SERVER_NAME: Server display name
- OPCUA_NAMESPACE_URI: Namespace identifier
- OPCUA_NAMESPACE_IDX: Namespace numeric index

Security Configuration:
- OPCUA_SECURITY_POLICY: Security policy (None, Basic256Sha256, etc.)
- OPCUA_SECURITY_MODE: Security mode (None, Sign, SignAndEncrypt)
- OPCUA_CERTIFICATE_PATH: Path to server certificate file
- OPCUA_PRIVATE_KEY_PATH: Path to private key file

Connection Configuration:
- OPCUA_CONNECTION_TIMEOUT: Client connection timeout (seconds)
- OPCUA_SESSION_TIMEOUT: OPC UA session timeout (seconds)

Server Management:
- OPCUA_AUTO_RESTART: Enable automatic restart (true/false)
- OPCUA_RESTART_DELAY: Delay between restart attempts (seconds)
- OPCUA_MAX_RESTART_ATTEMPTS: Maximum restart attempts

Variable Architecture:
----------------------
Variables use namespace-qualified node IDs:
- Format: "ns=2;s=variable_name"
- ns=2: References the custom namespace (namespace_idx=2)
- s=variable_name: String identifier for the variable

Communication Flow:
------------------
Backend (OPC UA Server) ←→ PLC (OPC UA Client)
- Backend hosts variables in its OPC UA server
- PLC connects as client to read/write variables
- Variables serve as shared "mailboxes" for coordination
- Real-time communication without polling delays

Usage Example:
-------------
# Load configuration
config = get_opcua_config()

# Access variable definition
job_var = get_variable_by_name("job_active")
print(f"Variable: {job_var.name} -> {job_var.node_id}")

# Filter by type
boolean_vars = get_variables_by_type("Boolean")
print(f"Found {len(boolean_vars)} boolean variables")
"""

import os
from typing import Dict, Any, List
from dataclasses import dataclass


@dataclass
class OPCUAServerConfig:
    """Configuration for OPC UA server hosting."""
    
    # Server endpoint configuration
    endpoint: str = "opc.tcp://0.0.0.0:4843/recoater/server/"
    server_name: str = "Recoater Multi-Material Coordination Server"
    
    # Namespace configuration
    namespace_uri: str = "http://recoater.backend.server"
    namespace_idx: int = 2
    
    # Security settings
    security_policy: str = "None"  # No security for internal network
    security_mode: str = "None"
    certificate_path: str = ""
    private_key_path: str = ""
    
    # Connection settings
    connection_timeout: float = 5.0
    session_timeout: float = 60.0
    
    # Server management
    auto_restart: bool = True
    restart_delay: float = 5.0
    max_restart_attempts: int = 3

# Coordination variables are shared data points that enable communication between the FastAPI backend and the PLC
# ┌──────────────────────────────────────────────────────────────┐
# │                    Your Backend Process                      │
# │                                                              │
# │  ┌─────────────────┐    ┌──────────────────────────────────┐ │
# │  │   FastAPI       │    │      OPC UA Server               │ │
# │  │   Server        │    │                                  │ │
# │  │                 │    │  ┌─────────────────────────────┐ │ │
# │  │ /api/v1/jobs    │◄───┤  │  Coordination Variables     │ │ │
# │  │ /api/v1/print   │    │  │                             │ │ │
# │  │                 │    │  │ job_active: False           │ │ │
# │  └─────────────────┘    │  │ current_layer: 5            │ │ │
# │                         │  │ backend_error: False         │ │ │
# │                         │  │ plc_error: False             │ │ │
# │                         │  │ ...                         │ │ │
# │                         │  └─────────────────────────────┘ │ │
# │                         └──────────────────────────────────┘ │
# └──────────────────────────────────────────────────────────────┘
#                                         ▲
#                                         │ OPC UA Protocol
#                                         │ opc.tcp://...
#                                         │
#                                 ┌───────────────┐
#                                 │   PLC Client  │
#                                 │   (TwinCAT)   │
#                                 └───────────────┘
@dataclass
class CoordinationVariable:
    """Definition for a coordination variable hosted by OPC UA server."""
    
    name: str
    node_id: str
    data_type: str
    initial_value: Any
    writable: bool = True
    description: str = ""


# Simplified coordination variables for backend-PLC coordination
COORDINATION_VARIABLES: List[CoordinationVariable] = [
    # Job Control
    CoordinationVariable(
        name="job_active",
        node_id="ns=2;s=job_active",
        data_type="Boolean",
        initial_value=False,
        description="Backend sets TRUE at start, FALSE at end"
    ),
    CoordinationVariable(
        name="total_layers",
        node_id="ns=2;s=total_layers",
        data_type="Int32",
        initial_value=0,
        description="Backend sets once at job start"
    ),
    CoordinationVariable(
        name="current_layer",
        node_id="ns=2;s=current_layer",
        data_type="Int32",
        initial_value=0,
        description="Backend manages, PLC reads"
    ),

    # Recoater Coordination
    CoordinationVariable(
        name="recoater_ready_to_print",
        node_id="ns=2;s=recoater_ready_to_print",
        data_type="Boolean",
        initial_value=False,
        description="Backend writes when Aerosint is ready"
    ),
    CoordinationVariable(
        name="recoater_layer_complete",
        node_id="ns=2;s=recoater_layer_complete",
        data_type="Boolean",
        initial_value=False,
        description="Backend writes when deposition complete"
    ),

    # System Status
    CoordinationVariable(
        name="backend_error",
        node_id="ns=2;s=backend_error",
        data_type="Boolean",
        initial_value=False,
        description="Backend writes if any issue arises"
    ),
    CoordinationVariable(
        name="plc_error",
        node_id="ns=2;s=plc_error",
        data_type="Boolean",
        initial_value=False,
        description="PLC writes if any issues"
    )
]


def get_opcua_config() -> OPCUAServerConfig:
    """
    Get OPC UA server configuration from environment variables or defaults.
    
    Returns:
        OPCUAServerConfig: Configuration object with server settings
    """
    return OPCUAServerConfig(
        endpoint=os.getenv("OPCUA_SERVER_ENDPOINT", "opc.tcp://0.0.0.0:4843/recoater/server/"),
        server_name=os.getenv("OPCUA_SERVER_NAME", "Recoater Multi-Material Coordination Server"),
        namespace_uri=os.getenv("OPCUA_NAMESPACE_URI", "http://recoater.backend.server"),
        namespace_idx=int(os.getenv("OPCUA_NAMESPACE_IDX", "2")),
        security_policy=os.getenv("OPCUA_SECURITY_POLICY", "None"),
        security_mode=os.getenv("OPCUA_SECURITY_MODE", "None"),
        certificate_path=os.getenv("OPCUA_CERTIFICATE_PATH", ""),
        private_key_path=os.getenv("OPCUA_PRIVATE_KEY_PATH", ""),
        connection_timeout=float(os.getenv("OPCUA_CONNECTION_TIMEOUT", "5.0")),
        session_timeout=float(os.getenv("OPCUA_SESSION_TIMEOUT", "60.0")),
        auto_restart=os.getenv("OPCUA_AUTO_RESTART", "true").lower() == "true",
        restart_delay=float(os.getenv("OPCUA_RESTART_DELAY", "5.0")),
        max_restart_attempts=int(os.getenv("OPCUA_MAX_RESTART_ATTEMPTS", "3"))
    )


def get_variable_by_name(name: str) -> CoordinationVariable:
    """
    Get coordination variable definition by name.
    
    Args:
        name: Variable name to lookup
        
    Returns:
        CoordinationVariable: Variable definition
        
    Raises:
        ValueError: If variable name not found
    """
    for var in COORDINATION_VARIABLES:
        if var.name == name:
            return var
    raise ValueError(f"Coordination variable '{name}' not found")


def get_variables_by_type(data_type: str) -> List[CoordinationVariable]:
    """
    Get all coordination variables of a specific data type.
    
    Args:
        data_type: OPC UA data type (Boolean, String, Int32, DateTime)
        
    Returns:
        List[CoordinationVariable]: Variables matching the data type
    """
    return [var for var in COORDINATION_VARIABLES if var.data_type == data_type]


# Global configuration instance
opcua_config = get_opcua_config()
