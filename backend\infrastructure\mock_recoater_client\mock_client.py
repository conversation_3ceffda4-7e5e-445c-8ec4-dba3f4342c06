"""
Mock Recoater Client Core
========================

This module provides the main MockRecoaterClient class that simulates the behavior
of the real recoater hardware for development and testing purposes.

The mock client returns realistic sample data and simulates state changes
to allow frontend development without requiring actual hardware.
"""

import time
import os
import random
import logging
from typing import Dict, Any, Optional, List

from .mock_blade_controls import MockBladeControlMixin
from .mock_leveler_controls import MockLevelerControlMixin
from .mock_print_controls import MockPrintControlMixin
from .mock_file_management import MockFileManagementMixin
from .mock_async_client import MockAsyncClientMixin
from .mock_drum_controls import MockDrumControlMixin

logger = logging.getLogger(__name__)


class MockRecoaterClient(
    MockBladeControlMixin,
    MockLevelerControlMixin,
    MockPrintControlMixin,
    MockFileManagementMixin,
    MockAsyncClientMixin,
    MockDrumControlMixin
):
    """
    Mock client that simulates the Aerosint SPD Recoater hardware API.
    
    This class implements the same interface as RecoaterClient but returns
    mock data instead of making actual HTTP requests to hardware.
    """
    
    def __init__(self, base_url: str, timeout: float = 5.0):
        """
        Initialize the MockRecoaterClient.
        
        Args:
            base_url: The base URL (ignored in mock mode)
            timeout: Request timeout (ignored in mock mode)
        """
        self.base_url = base_url
        self.timeout = timeout
        self._start_time = time.time()
        self._state = {
            "status": "idle",
            "current_layer": 0,
            "total_layers": 100,
            "progress": 0.0,
            "temperature": 25.0,
            "errors": []
        }

        # Print job state management
        self._print_job_state = "ready"
        self._print_job_id = None
        self._print_job_start_time = None
        self._printing_until = None

        # Dev-mode timing and failure injection (env-configurable)
        self._layer_time_sec = float(os.getenv("MOCK_TIMING_LAYER_TIME_MS", "4000")) / 1000.0
        self._fail_at_layer = int(os.getenv("MOCK_FAIL_AT_LAYER", "0"))
        self._random_fail_prob = float(os.getenv("MOCK_RANDOM_FAILURE_PROB", "0"))
        self._fail_type = os.getenv("MOCK_FAIL_TYPE", "none").lower()
        seed_value = os.getenv("MOCK_SEED")
        self._rng = random.Random(seed_value) if seed_value is not None else random.Random()
        self._printed_layers = 0
        self._error_reason = ""
        # Ensure an injected failure triggers only once at the configured layer
        self._fail_triggered_once = False

        # Drum geometry storage for persistent mock data
        self._drum_geometries = {}  # {drum_id: {"file_data": bytes, "content_type": str}}

        # Drum state management for coordination engine
        self._drum_states = {}  # {drum_id: state_string}
        self._current_layers = {}  # {drum_id: current_layer_number}

        # Multi-material job state
        self._job_active = False

        logger.info(f"MockRecoaterClient initialized (development mode)")
    
    def get_state(self) -> Dict[str, Any]:
        """
        Get the current mock recoater state in a form compatible with the
        job manager's polling logic. Key: returns a 'state' field that is one of
        'ready', 'printing', or 'error'.
        """
        # Temperature noise
        self._state["temperature"] = 25.0 + random.uniform(-2.0, 2.0)

        now = time.time()
        # Handle printing timer and completion/error injection
        if getattr(self, "_print_job_state", "ready") == "printing":
            if self._printing_until is not None:
                # Progress during layer print
                remaining = max(0.0, self._printing_until - now)
                elapsed = max(0.0, self._layer_time_sec - remaining)
                progress = max(0.0, min(0.999, elapsed / self._layer_time_sec))
                self._state["progress"] = progress
                # When timer elapses, transition to ready or error
                if remaining <= 0.0:
                    self._printed_layers += 1
                    fail = False
                    if self._fail_at_layer > 0 and self._printed_layers == self._fail_at_layer and not getattr(self, "_fail_triggered_once", False):
                        fail = True
                        self._fail_triggered_once = True
                    elif self._random_fail_prob > 0.0 and self._rng.random() < self._random_fail_prob:
                        fail = True
                    if fail:
                        self._print_job_state = "error"
                        self._error_reason = self._fail_type or "mock_failure"
                    else:
                        self._print_job_state = "ready"
                    self._printing_until = None
        else:
            # When not printing, progress is zero
            self._state["progress"] = 0.0

        # Map to expected fields
        current_state = getattr(self, "_print_job_state", "ready")
        self._state["state"] = current_state
        # Keep legacy 'status' but align with state for simplicity
        self._state["status"] = "running" if current_state == "printing" else ("error" if current_state == "error" else "ready")

        logger.debug(f"Mock state: {self._state}")
        return self._state.copy()

    def get_config(self) -> Dict[str, Any]:
        """
        Mock implementation for getting recoater configuration.

        Returns:
            Dictionary containing mock configuration variables matching the hardware API schema
        """
        logger.info("Mock get_config called")
        return {
            "build_space_diameter": 250.0,
            "build_space_dimensions": {
                "length": 250.0,
                "width": 96.0
            },
            "ejection_matrix_size": 192,
            "gaps": [130.0, 130.0],  # Gaps between 3 drums
            "resolution": 500
        }

    def set_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Mock implementation for setting recoater configuration.

        Args:
            config: Configuration dictionary

        Returns:
            Mock response indicating success
        """
        logger.info(f"Mock set_config called with: {config}")
        return {
            "success": True,
            "config_updated": config,
            "timestamp": time.time()
        }

    def get_status(self) -> Dict[str, Any]:
        """
        Get the current status of the recoater.
        
        Returns:
            Dictionary containing status information
        """
        return {
            "status": self._state["status"],
            "uptime": int(time.time() - self._start_time),
            "version": "1.0.0-mock",
            "hardware_id": "MOCK-RECOATER-001"
        }
    
    def start_job(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Start a recoating job.
        
        Args:
            job_data: Job configuration data
            
        Returns:
            Dictionary containing job start response
        """
        if self._state["status"] != "idle":
            return {
                "success": False,
                "error": "Recoater is not idle"
            }
        
        self._state["status"] = "running"
        self._state["current_layer"] = 0
        self._state["progress"] = 0.0
        self._state["total_layers"] = job_data.get("layers", 100)
        
        logger.info(f"Mock job started with {self._state['total_layers']} layers")
        return {
            "success": True,
            "job_id": f"mock-job-{int(time.time())}"
        }
    
    def stop_job(self) -> Dict[str, Any]:
        """
        Stop the current recoating job.
        
        Returns:
            Dictionary containing job stop response
        """
        if self._state["status"] != "running":
            return {
                "success": False,
                "error": "No job is currently running"
            }
        
        self._state["status"] = "idle"
        self._state["current_layer"] = 0
        self._state["progress"] = 0.0
        
        logger.info("Mock job stopped")
        return {
            "success": True
        }
    
    def pause_job(self) -> Dict[str, Any]:
        """
        Pause the current recoating job.
        
        Returns:
            Dictionary containing job pause response
        """
        if self._state["status"] != "running":
            return {
                "success": False,
                "error": "No job is currently running"
            }
        
        self._state["status"] = "paused"
        
        logger.info("Mock job paused")
        return {
            "success": True
        }
    
    def resume_job(self) -> Dict[str, Any]:
        """
        Resume a paused recoating job.
        
        Returns:
            Dictionary containing job resume response
        """
        if self._state["status"] != "paused":
            return {
                "success": False,
                "error": "No job is currently paused"
            }
        
        self._state["status"] = "running"
        
        logger.info("Mock job resumed")
        return {
            "success": True
        }
    


    def get_drums(self) -> list[dict[str, any]]:
        """
        Mock implementation for getting information about all drums.

        Returns:
            List of dictionaries containing drum information
        """
        logger.info("Mock get_drums called")
        return [
            {"id": 0, "name": "Drum 0 (Mock)"},
            {"id": 1, "name": "Drum 1 (Mock)"},
            {"id": 2, "name": "Drum 2 (Mock)"}
        ]

    def get_drum(self, drum_id: int) -> dict[str, any]:
        """
        Mock implementation for getting information about a specific drum.

        Args:
            drum_id: The drum's ID

        Returns:
            Dictionary containing drum information
        """
        logger.info(f"Mock get_drum for id {drum_id} called")

        # Ensure drum state is initialized
        if drum_id not in self._drum_states:
            self._drum_states[drum_id] = "ready"

        return {
            "id": drum_id,
            "state": self._drum_states.get(drum_id, "ready"),
            "circumference": 314.15,
            "position": round(random.uniform(0, 314), 2),
            "running": random.choice([True, False]),
            "current_layer": self._current_layers.get(drum_id, 0)
        }

    def set_state(self, action: str) -> Dict[str, Any]:
        """
        Mock implementation for setting recoater server state.

        Args:
            action: The action to perform ('restart' or 'shutdown')

        Returns:
            Mock response indicating command accepted
        """
        logger.info(f"Mock set_state called with action: {action}")
        return {
            "success": True,
            "action": action,
            "status": "accepted",
            "message": f"Mock server {action} command accepted"
        }

    def health_check(self) -> bool:
        """
        Mock implementation for health check. Always returns True.

        Returns:
            True indicating the mock recoater is always healthy
        """
        logger.info("Mock health_check called")
        return True
