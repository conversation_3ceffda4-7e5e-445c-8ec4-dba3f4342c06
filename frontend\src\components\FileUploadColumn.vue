<template>
  <div class="file-upload-column">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title heading-md">Drum {{ drumId }}</h4>
      </div>
      <div class="card-content">
        <div v-if="!isConnected" class="disabled-overlay">
          <p class="disabled-text">Connect to recoater to manage files</p>
        </div>

        <div class="form-group">
          <label :for="`file-input-${drumId}`" class="form-label label-sm">Geometry File (PNG or CLI):</label>
          <input
            :id="`file-input-${drumId}`"
            type="file"
            ref="fileInput"
            @change="handleFileSelect"
            accept=".png,.cli,image/png,application/octet-stream"
            class="form-file-input"
            :disabled="!isConnected || isLoading"
          />
          <div v-if="selectedFile" class="file-info">
            Selected: {{ selectedFile.name }} ({{ formatFileSize(selectedFile.size) }})
          </div>
        </div>

        <!-- Last Uploaded File Display -->
        <div class="last-uploaded-section">
          <div class="last-uploaded-label">Last Uploaded:</div>
          <div class="last-uploaded-file">
            {{ printJobStore.getLastUploadedFileName(drumId) }}
          </div>
        </div>

        <div class="button-group">
          <button
            @click="uploadFile"
            :disabled="!isConnected || !selectedFile || isLoading"
            class="btn btn-primary"
          >
            <span v-if="isLoading">Uploading...</span>
            <span v-else>Upload</span>
          </button>
          <button
            @click="downloadFile"
            :disabled="!isConnected || isLoading"
            class="btn btn-secondary"
          >
            <span v-if="isLoading">Downloading...</span>
            <span v-else>Download</span>
          </button>
          <button
            @click="deleteFile"
            :disabled="!isConnected || isLoading"
            class="btn btn-tertiary btn-danger"
          >
            <span v-if="isLoading">Deleting...</span>
            <span v-else>Delete</span>
          </button>
        </div>

        <button
          v-if="selectedFile"
          @click="clearFileSelection"
          :disabled="isLoading"
          class="btn btn-tertiary clear-btn"
        >
          Clear Selection
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { usePrintJobStore } from '@/stores/printJobStore'

export default {
  name: 'FileUploadColumn',
  props: {
    drumId: {
      type: Number,
      required: true,
      validator: (value) => [0, 1, 2].includes(value)
    },
    isConnected: {
      type: Boolean,
      required: true
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['upload', 'download', 'delete'],
  setup(props, { emit }) {
    const selectedFile = ref(null)
    const fileInput = ref(null)
    const printJobStore = usePrintJobStore()

    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        // Validate file type (PNG or CLI)
        const allowedTypes = ['image/png', 'application/octet-stream']
        const allowedExtensions = ['.png', '.cli']
        const fileName = file.name.toLowerCase()
        const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext))

        if (!allowedTypes.includes(file.type) && !hasValidExtension) {
          alert('Please select a PNG or CLI file.')
          clearFileSelection()
          return
        }

        selectedFile.value = file
      }
    }

    const clearFileSelection = () => {
      selectedFile.value = null
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const uploadFile = () => {
      if (!selectedFile.value) {
        alert('Please select a file to upload.')
        return
      }
      emit('upload', {
        drumId: props.drumId,
        file: selectedFile.value
      })
      clearFileSelection()
    }

    const downloadFile = () => {
      emit('download', {
        drumId: props.drumId
      })
    }

    const deleteFile = () => {
      if (!confirm('Are you sure you want to delete the geometry file from the cache?')) {
        return
      }
      emit('delete', {
        drumId: props.drumId
      })
    }

    return {
      selectedFile,
      fileInput,
      printJobStore,
      handleFileSelect,
      clearFileSelection,
      formatFileSize,
      uploadFile,
      downloadFile,
      deleteFile
    }
  }
}
</script>

<style scoped>
.file-upload-column {
  height: 90%;
}

.card {
  height: 100%;
  display: flex;
  flex-direction: column;
}


.card-content {
  flex: 1;
  position: relative;
}

.disabled-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
}

.disabled-text {
  color: var(--color-neutral-600);
  font-weight: 500;
  text-align: center;
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--color-neutral-700);
}

.form-file-input {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--color-neutral-400);
  border-radius: 4px;
  background-color: white;
  font-size: var(--font-size-sm);
}

.form-file-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.file-info {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-sm);
  background-color: var(--color-neutral-100);
  border-radius: 4px;
  font-size: var(--font-size-sm);
  color: var(--color-neutral-700);
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.clear-btn {
  width: 100%;
  margin-top: var(--spacing-sm);
}

.btn {
  width: 100%;
  text-align: center;
}

/* Override tertiary danger styling for better visibility */
.btn-tertiary.btn-danger {
  color: var(--color-danger);
  border: 1px solid var(--color-danger);
  background-color: rgba(231, 76, 60, 0.1);
}

.btn-tertiary.btn-danger:hover {
  background-color: var(--color-danger);
  color: white;
}

.btn-tertiary.btn-danger:disabled {
  color: var(--color-neutral-400);
  border-color: var(--color-neutral-400);
  background-color: var(--color-neutral-100);
}

@media (max-width: 768px) {
  .button-group {
    gap: var(--spacing-sm);
  }
}

.last-uploaded-section {
  margin-top: var(--spacing-md);
  padding: var(--spacing-sm);
  background-color: var(--color-neutral-100);
  border-radius: 4px;
  border: 1px solid var(--color-neutral-300);
}

.last-uploaded-label {
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--color-neutral-600);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.last-uploaded-file {
  font-size: var(--font-size-sm);
  color: var(--color-neutral-700);
  word-break: break-word;
  line-height: 1.4;
}
</style>
