"""
WebSocket Connection Manager
===========================

Manages WebSocket connections for real-time updates in the Recoater HMI.
Handles connection lifecycle, subscription management, and message broadcasting.
"""

import logging
from typing import Dict, Any, List, Set
from fastapi import WebSocket

logger = logging.getLogger(__name__)


class WebSocketConnectionManager:
    """Manages WebSocket connections for real-time updates."""

    def __init__(self):
        """Initialize the connection manager."""
        self.active_connections: List[WebSocket] = []
        # Track subscriptions for each connection
        self.connection_subscriptions: Dict[WebSocket, Set[str]] = {}
    
    async def connect(self, websocket: WebSocket) -> None:
        """
        Accept a new WebSocket connection.
        
        Args:
            websocket: The WebSocket connection to accept
        """
        await websocket.accept()
        self.active_connections.append(websocket)
        # Initialize with default subscription (status only)
        self.connection_subscriptions[websocket] = {'status'}
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket) -> None:
        """
        Remove a WebSocket connection.
        
        Args:
            websocket: The WebSocket connection to remove
        """
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        # Clean up subscription tracking
        if websocket in self.connection_subscriptions:
            del self.connection_subscriptions[websocket]
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")

    def update_subscription(self, websocket: WebSocket, data_types: List[str]) -> None:
        """
        Update subscription preferences for a specific connection.
        
        Args:
            websocket: The WebSocket connection to update
            data_types: List of data types to subscribe to
        """
        if websocket in self.connection_subscriptions:
            self.connection_subscriptions[websocket] = set(data_types)
            logger.info(f"Updated subscription for connection: {data_types}")
    
    async def broadcast(self, message: Dict[str, Any]) -> None:
        """
        Broadcast a message to all connected clients.
        
        Args:
            message: The message to broadcast
        """
        if not self.active_connections:
            return

        # Remove disconnected clients
        disconnected = []
        for connection in self.active_connections:
            try:
                # Filter message based on connection's subscriptions
                filtered_message = self._filter_message_for_connection(connection, message)
                if filtered_message:  # Only send if there's relevant data
                    await connection.send_json(filtered_message)
            except Exception as e:
                logger.warning(f"Failed to send message to WebSocket: {e}")
                disconnected.append(connection)

        # Clean up disconnected clients
        for connection in disconnected:
            self.disconnect(connection)

    def _filter_message_for_connection(self, websocket: WebSocket, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Filter message content based on connection's subscriptions.
        
        Args:
            websocket: The WebSocket connection to filter for
            message: The original message
            
        Returns:
            Filtered message containing only subscribed data
        """
        if websocket not in self.connection_subscriptions:
            return message  # Send everything if no subscription info

        subscriptions = self.connection_subscriptions[websocket]
        filtered_message = {
            "type": message.get("type"),
            "timestamp": message.get("timestamp")
        }

        # Always include basic status data
        if "data" in message:
            filtered_message["data"] = message["data"]

        # Include optional data based on subscriptions (axis removed)
        if "drum" in subscriptions and "drum_data" in message:
            filtered_message["drum_data"] = message["drum_data"]
        if "leveler" in subscriptions and "leveler_data" in message:
            filtered_message["leveler_data"] = message["leveler_data"]
        if "print" in subscriptions and "print_data" in message:
            filtered_message["print_data"] = message["print_data"]

        return filtered_message

    def get_required_data_types(self) -> Set[str]:
        """
        Get all data types that are currently subscribed to by any connection.
        
        Returns:
            Set of all subscribed data types
        """
        all_subscriptions = set()
        for subscriptions in self.connection_subscriptions.values():
            all_subscriptions.update(subscriptions)
        return all_subscriptions

    @property
    def connection_count(self) -> int:
        """Get the number of active connections."""
        return len(self.active_connections)

    @property
    def has_connections(self) -> bool:
        """Check if there are any active connections."""
        return len(self.active_connections) > 0
