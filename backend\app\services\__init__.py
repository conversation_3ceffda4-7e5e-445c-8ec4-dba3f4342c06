"""
Services Package
===============

This package contains all business logic services for the recoater application.

Modules:
- communication: WebSocket and communication services
- job_management: Multi-material job management and coordination
- monitoring: Data gathering and status monitoring
- opcua: OPC UA server and coordination services
"""

# Import main services
from .communication import WebSocketConnectionManager
from .job_management import (
    MultiMaterialJobService,
    MultiMaterialJobError,
)

from .monitoring import RecoaterDataGatherer, StatusPollingService
from .opcua import OPCUAService

__all__ = [
    'WebSocketConnectionManager',
    'MultiMaterialJobService',
    'MultiMaterialJobError',
    'RecoaterDataGatherer',
    'StatusPollingService',
    'OPCUAService',
]
