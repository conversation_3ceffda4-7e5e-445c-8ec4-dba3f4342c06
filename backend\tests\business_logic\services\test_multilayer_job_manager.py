"""
Tests for Multi-Material Job Manager
====================================

Test suite for the multi-material job management functionality
including job creation, CLI file processing, and drum coordination.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict

from app.services.job_management import MultiMaterialJobService, MultiMaterialJobError
from app.models.multilayer_job import MultiMaterialJobState, JobStatus
from infrastructure.cli_editor.editor import ParsedCliFile, CliLayer, Point, Polyline


@pytest.fixture
def mock_recoater_client():
    """Create a mock recoater client for testing."""
    client = Mock()
    client.get_state = Mock(return_value={"state": "ready"})
    client._make_request = Mock()
    return client


@pytest.fixture
def job_manager(mock_recoater_client):
    """Create a job service instance for testing (replacing legacy manager)."""
    return MultiMaterialJobService(mock_recoater_client)


@pytest.fixture
def sample_cli_files():
    """Create sample CLI files for testing."""
    # Create sample polylines and layers
    points = [Point(x=0.0, y=0.0), Point(x=10.0, y=10.0)]
    polyline = Polyline(part_id=1, direction=0, points=points)
    
    # Create layers for each drum
    layers_drum_0 = [
        CliLayer(z_height=0.1, polylines=[polyline]),
        CliLayer(z_height=0.2, polylines=[polyline]),
        CliLayer(z_height=0.3, polylines=[polyline])
    ]

    layers_drum_1 = [
        CliLayer(z_height=0.1, polylines=[polyline]),
        CliLayer(z_height=0.2, polylines=[polyline])
    ]

    layers_drum_2 = [
        CliLayer(z_height=0.1, polylines=[polyline]),
        CliLayer(z_height=0.2, polylines=[polyline]),
        CliLayer(z_height=0.3, polylines=[polyline]),
        CliLayer(z_height=0.4, polylines=[polyline])
    ]
    
    # Create parsed CLI files
    cli_files = {
        0: ParsedCliFile(
            header_lines=["$$HEADERSTART", "$$UNITS/MM", "$$HEADEREND"],
            layers=layers_drum_0,
            is_aligned=False
        ),
        1: ParsedCliFile(
            header_lines=["$$HEADERSTART", "$$UNITS/MM", "$$HEADEREND"],
            layers=layers_drum_1,
            is_aligned=False
        ),
        2: ParsedCliFile(
            header_lines=["$$HEADERSTART", "$$UNITS/MM", "$$HEADEREND"],
            layers=layers_drum_2,
            is_aligned=False
        )
    }
    
    return cli_files


class TestMultiMaterialJobManager:
    """Test cases for MultiMaterialJobManager."""
    
    def test_initialization(self, job_manager):
        """Test job manager initialization."""
        assert job_manager.current_job is None
        assert len(job_manager.cli_cache) == 0
    
    def test_add_cli_file(self, job_manager, sample_cli_files):
        """Test adding CLI files to cache."""
        file_id = "test-file-1"
        job_manager.add_cli_file(file_id, sample_cli_files[0])
        
        assert file_id in job_manager.cli_cache
        entry = job_manager.cli_cache[file_id]
        assert isinstance(entry, dict)
        assert entry['parsed_file'] == sample_cli_files[0]
        assert entry['original_filename'] == 'unknown.cli'

    @pytest.mark.asyncio
    async def test_modern_workflow_success(self, job_manager, sample_cli_files):
        """Test successful modern workflow with 3 CLI files cached to drums."""
        # Use modern drum cache approach
        job_manager.cache_cli_file_for_drum(0, sample_cli_files[0], "file-drum-0.cli")
        job_manager.cache_cli_file_for_drum(1, sample_cli_files[1], "file-drum-1.cli") 
        job_manager.cache_cli_file_for_drum(2, sample_cli_files[2], "file-drum-2.cli")
        
        # Mock required methods for the workflow
        job_manager.cli_parser.generate_single_layer_ascii_cli = Mock(return_value=b"CLI")
        job_manager.recoater_client.upload_cli_data = AsyncMock()
        job_manager.recoater_client.start_print_job = Mock()
        job_manager.recoater_client.get_state = Mock(return_value={"state": "ready"})

        # Patch OPC UA service
        with patch("app.services.opcua.opcua_service.opcua_service") as mock_opcua:
            mock_opcua.set_job_active = AsyncMock()
            mock_opcua.update_layer_progress = AsyncMock()
            mock_opcua.set_recoater_ready_to_print = AsyncMock()
            mock_opcua.set_recoater_layer_complete = AsyncMock()
            mock_opcua.get_backend_error = Mock(return_value=False)
            mock_opcua.get_plc_error = Mock(return_value=False)
            mock_opcua.set_job_inactive = AsyncMock()

            # Test modern unified workflow
            result = await job_manager.start_layer_by_layer_job()
            assert result is True
            
            # Verify job state was created internally
            assert job_manager.current_job is not None
            assert job_manager.current_job.total_layers == 4  # Maximum of 3, 2, 4 layers
            
            # Verify OPC UA calls were made
            mock_opcua.set_job_active.assert_called_once()
            assert mock_opcua.update_layer_progress.call_count >= 1
    
    @pytest.mark.asyncio
    async def test_modern_workflow_too_many_files(self, job_manager, sample_cli_files):
        """Test modern workflow with too many files (should fail)."""
        # Cache 4 files when only 3 drums are supported
        job_manager.cache_cli_file_for_drum(0, sample_cli_files[0], "file1.cli")
        job_manager.cache_cli_file_for_drum(1, sample_cli_files[1], "file2.cli")
        job_manager.cache_cli_file_for_drum(2, sample_cli_files[2], "file3.cli")
        
        # Mock CLI file creation for invalid drum 3
        mock_cli = Mock()
        mock_cli.layers = [Mock() for _ in range(2)]
        
        # This should fail validation since drum 3 doesn't exist (only 0, 1, 2)
        with pytest.raises(ValueError, match="Invalid drum_id"):
            job_manager.cache_cli_file_for_drum(3, mock_cli, "file4.cli")
    
    @pytest.mark.asyncio
    async def test_create_job_dual_material_valid(self, job_manager, sample_cli_files):
        """Test job creation with valid dual material setup using modern workflow."""
        # Use modern drum cache approach instead of legacy create_job()
        job_manager.cache_cli_file_for_drum(0, sample_cli_files[0], "file1.cli")
        job_manager.cache_cli_file_for_drum(1, sample_cli_files[1], "file2.cli")
        # Drum 2 will automatically get empty template via modern workflow

        # Mock required methods
        job_manager.cli_parser.generate_single_layer_ascii_cli = Mock(return_value=b"CLI")
        job_manager.recoater_client.upload_cli_data = AsyncMock()
        job_manager.recoater_client.start_print_job = Mock()
        job_manager.recoater_client.get_state = Mock(return_value={"state": "ready"})

        # Patch OPC UA service
        with patch("app.services.opcua.opcua_service.opcua_service") as mock_opcua:
            mock_opcua.set_job_active = AsyncMock()
            mock_opcua.update_layer_progress = AsyncMock()
            mock_opcua.set_recoater_ready_to_print = AsyncMock()
            mock_opcua.set_recoater_layer_complete = AsyncMock()
            mock_opcua.get_backend_error = Mock(return_value=False)
            mock_opcua.get_plc_error = Mock(return_value=False)

            # Test modern unified workflow
            result = await job_manager.start_layer_by_layer_job()
            assert result is True

            # Verify job state was created
            assert job_manager.current_job is not None
            assert job_manager.current_job.total_layers == max(len(sample_cli_files[0].layers), len(sample_cli_files[1].layers))

    @pytest.mark.asyncio
    async def test_modern_workflow_missing_cache(self, job_manager):
        """Test modern workflow fails if no files cached for any drums."""
        # Don't cache any files
        
        # Should raise error for missing cached files
        with pytest.raises(MultiMaterialJobError, match="No CLI files cached for any drums"):
            await job_manager.start_layer_by_layer_job()
    
    @pytest.mark.asyncio
    async def test_modern_workflow_complete_flow(self, job_manager, sample_cli_files):
        """Test complete modern workflow from caching to job execution."""
        # Use modern caching approach
        job_manager.cache_cli_file_for_drum(0, sample_cli_files[0], "file1.cli")
        job_manager.cache_cli_file_for_drum(1, sample_cli_files[1], "file2.cli")
        job_manager.cache_cli_file_for_drum(2, sample_cli_files[2], "file3.cli")
        
        # Mock all required services
        job_manager.cli_parser.generate_single_layer_ascii_cli = Mock(return_value=b"CLI")
        job_manager.recoater_client.upload_cli_data = AsyncMock()
        job_manager.recoater_client.start_print_job = Mock()
        job_manager.recoater_client.get_state = Mock(return_value={"state": "ready"})

        # Patch OPC UA service
        with patch("app.services.opcua.opcua_service.opcua_service") as mock_opcua:
            mock_opcua.set_job_active = AsyncMock()
            mock_opcua.update_layer_progress = AsyncMock()
            mock_opcua.set_recoater_ready_to_print = AsyncMock()
            mock_opcua.set_recoater_layer_complete = AsyncMock()
            mock_opcua.get_backend_error = Mock(return_value=False)
            mock_opcua.get_plc_error = Mock(return_value=False)
            mock_opcua.set_job_inactive = AsyncMock()

            # Execute modern unified workflow
            result = await job_manager.start_layer_by_layer_job()
            
            assert result is True
            # Job may complete instantly in test, so just verify it was created and run
            assert job_manager.current_job is not None
            assert job_manager.current_job.total_layers == 4
    
    @pytest.mark.asyncio
    async def test_modern_workflow_no_cached_files(self, job_manager):
        """Test modern workflow fails when no files are cached.""" 
        # Don't cache any files
        
        # Should raise error since modern workflow checks cache
        with pytest.raises(MultiMaterialJobError, match="No CLI files cached"):
            await job_manager.start_layer_by_layer_job()
    
    @pytest.mark.asyncio
    async def test_modern_workflow_cancel(self, job_manager, sample_cli_files):
        """Test job cancellation during modern workflow."""
        # Cache files and start modern workflow
        job_manager.cache_cli_file_for_drum(0, sample_cli_files[0], "file1.cli")
        job_manager.cache_cli_file_for_drum(1, sample_cli_files[1], "file2.cli")

        # Verify files are cached before cancellation
        assert job_manager.has_cached_files() is True
        assert job_manager.get_cached_file_for_drum(0) is not None
        assert job_manager.get_cached_file_for_drum(1) is not None

        # Mock services to start job with slower execution to allow cancellation
        job_manager.cli_parser.generate_single_layer_ascii_cli = Mock(return_value=b"CLI")
        job_manager.recoater_client.upload_cli_data = AsyncMock()
        job_manager.recoater_client.start_print_job = Mock()
        # Make get_state return "printing" to simulate a longer-running job
        job_manager.recoater_client.get_state = Mock(return_value={"state": "printing"})

        with patch("app.services.opcua.opcua_service.opcua_service") as mock_opcua:
            mock_opcua.set_job_active = AsyncMock()
            mock_opcua.update_layer_progress = AsyncMock()
            mock_opcua.set_recoater_ready_to_print = AsyncMock()
            mock_opcua.set_recoater_layer_complete = AsyncMock()
            mock_opcua.get_backend_error = Mock(return_value=False)
            mock_opcua.get_plc_error = Mock(return_value=False)
            mock_opcua.set_job_inactive = AsyncMock()

            # Start job in background (don't await)
            import asyncio
            task = asyncio.create_task(job_manager.start_layer_by_layer_job())
            job_manager._background_task = task

            # Wait a short time to let the job start
            await asyncio.sleep(0.1)

            # Cancel job while it's running
            result = await job_manager.cancel_job()

            assert result is True
            # After cancellation, current_job should be None (cleaned up)
            assert job_manager.current_job is None
            # Background task reference should be cleared
            assert job_manager._background_task is None
            # Original task should be cancelled
            assert task.cancelled() or task.done()
            # Drum cache should be cleared after cancellation
            assert job_manager.has_cached_files() is False
            assert job_manager.get_cached_file_for_drum(0) is None
            assert job_manager.get_cached_file_for_drum(1) is None
            assert job_manager.get_cached_file_for_drum(2) is None

    @pytest.mark.asyncio
    async def test_cancel_job_clears_drum_cache(self, job_manager, sample_cli_files):
        """Test that cancelling a job clears all drum cache files."""
        # Cache files for all 3 drums
        job_manager.cache_cli_file_for_drum(0, sample_cli_files[0], "file1.cli")
        job_manager.cache_cli_file_for_drum(1, sample_cli_files[1], "file2.cli")
        job_manager.cache_cli_file_for_drum(2, sample_cli_files[2], "file3.cli")

        # Verify all drums have cached files
        assert job_manager.has_cached_files() is True
        assert job_manager.get_cached_file_for_drum(0) is not None
        assert job_manager.get_cached_file_for_drum(1) is not None
        assert job_manager.get_cached_file_for_drum(2) is not None

        # Create a minimal job state to simulate an active job
        from app.models.multilayer_job import MultiMaterialJobState
        job_manager.current_job = MultiMaterialJobState(
            file_ids={0: "file1.cli", 1: "file2.cli", 2: "file3.cli"},
            total_layers=3
        )
        job_manager.current_job.is_active = True

        # Cancel the job
        result = await job_manager.cancel_job()

        # Verify cancellation was successful
        assert result is True
        assert job_manager.current_job is None

        # Verify all drum cache files are cleared
        assert job_manager.has_cached_files() is False
        assert job_manager.get_cached_file_for_drum(0) is None
        assert job_manager.get_cached_file_for_drum(1) is None
        assert job_manager.get_cached_file_for_drum(2) is None

    @pytest.mark.asyncio
    async def test_get_job_status_no_job(self, job_manager):
        """Test getting status when no job is active."""
        status = await job_manager.get_job_status()
        assert isinstance(status, dict)
        assert status["is_active"] is False
        assert status["status"] in ("idle", "ready", "printing", "error")
        assert status["current_layer"] >= 0
        assert status["total_layers"] >= 0

    @pytest.mark.asyncio
    async def test_get_job_status_with_job(self, job_manager, sample_cli_files):
        """Test getting job status with active job."""
        # Cache files for modern workflow
        job_manager.cache_cli_file_for_drum(0, sample_cli_files[0], "file1.cli")
        job_manager.cache_cli_file_for_drum(1, sample_cli_files[1], "file2.cli")
        job_manager.cache_cli_file_for_drum(2, sample_cli_files[2], "file3.cli")

        # Create job state manually for status test
        from app.models.multilayer_job import MultiMaterialJobState
        job_state = MultiMaterialJobState(
            file_ids={0: "file1.cli", 1: "file2.cli", 2: "file3.cli"},
            total_layers=4
        )
        job_manager.current_job = job_state

        status = await job_manager.get_job_status()

        assert isinstance(status, dict)
        # Job ID should be available when a current_job exists
        assert status.get("job_id") == job_state.job_id
        # Drums should reflect 3 configured drums even if OPCUA not running
        assert "drums" in status
        assert len(status["drums"]) == 3

    def test_get_drum_status(self, job_manager, sample_cli_files):
        """Test getting individual drum status."""
        # Cache files for modern workflow
        job_manager.cache_cli_file_for_drum(0, sample_cli_files[0], "file1.cli")
        job_manager.cache_cli_file_for_drum(1, sample_cli_files[1], "file2.cli")
        job_manager.cache_cli_file_for_drum(2, sample_cli_files[2], "file3.cli")
        
        # Create job state manually
        from app.models.multilayer_job import MultiMaterialJobState
        job_state = MultiMaterialJobState(
            file_ids={0: "file1.cli", 1: "file2.cli", 2: "file3.cli"}, 
            total_layers=4
        )
        job_manager.current_job = job_state
        
        # Test valid drum
        drum_status = job_manager.get_drum_status(0)
        assert drum_status is not None
        assert drum_status["drum_id"] == 0
        
        # Test invalid drum
        drum_status = job_manager.get_drum_status(5)
        assert drum_status is None
    
    def test_generate_single_layer_cli(self, job_manager, sample_cli_files):
        """Test single layer CLI generation."""
        cli_file = sample_cli_files[0]
        
        # Test valid layer
        cli_data = job_manager._generate_single_layer_cli(cli_file, 0)
        assert isinstance(cli_data, bytes)
        assert b"$$LAYER" in cli_data
        assert b"$$POLYLINE" in cli_data
        
        # Test invalid layer index
        cli_data = job_manager._generate_single_layer_cli(cli_file, 10)
        assert cli_data == b""


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
