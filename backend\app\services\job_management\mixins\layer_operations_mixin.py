"""
LayerProcessingMixin
===================

Unified mixin for layer processing operations including job lifecycle management,
layer upload operations, and hardware communication. This consolidates functionality
from the old LayerOperationsMixin and parts of JobLifecycleMixin.

Key Responsibilities:
- Job Lifecycle: Cancel and monitor job execution
- Hardware Communication: Interface with recoater client
- Status Monitoring: Provide job and drum status information
- Error Management: Handle job-related errors

Required Attributes (provided by concrete service):
- recoater_client: RecoaterClient instance for hardware communication
- cli_parser: CLI editor for parsing and generating CLI files
- current_job: Optional[MultiMaterialJobState] for active job tracking
- cli_cache: Dict[str, CliCacheEntry] for generic file caching
- drum_cli_cache: Dict[int, Optional[Dict[str, Any]]] for per-drum caching

Public Methods:
- cancel_job(): Cancel the current job
- get_job_status(): Get current job status with progress
- get_drum_status(drum_id): Get status for specific drum

Private Helper Methods:
- _upload_layer_to_drum(drum_id, cli_data): Upload CLI data to specific drum
- _generate_single_layer_cli(parsed_file, layer_index): Generate CLI data for single layer  
- clear_job_error_flags(): Clear job-specific error states
"""
from __future__ import annotations

import asyncio
import logging
import time
import uuid
from typing import Dict, Optional, Any

from app.models.multilayer_job import (
    MultiMaterialJobState,
    JobStatus,
    LayerData,
)
from infrastructure.cli_editor.editor import Editor, ParsedCliFile
from ..models import CliCacheEntry, MultiMaterialJobError

logger = logging.getLogger(__name__)


class LayerProcessingMixin:
    """Unified mixin for layer processing and job lifecycle management."""

    # ---- Job Lifecycle Management ----

    async def cancel_job(self) -> bool:
        """Cancel the current job and stop any in-progress layer.

        Behaviour:
        - Mark job inactive and status cancelled
        - Ask recoater client to cancel current print if supported (dev/mock parity)
        - Cleanup OPC UA job state and clear error flags
        - Clear all drum cache files to ensure clean state for next job
        - Fully reset in-memory job reference to prevent stale re-activation
        """
        # Always clear drum cache regardless of job state to ensure clean state
        if hasattr(self, 'clear_drum_cache'):
            self.clear_drum_cache()
            logger.info("Cleared all drum cache files during job cancellation")

        if not getattr(self, 'current_job', None):
            return True
        try:
            # Mark job as cancelled immediately
            self.current_job.is_active = False
            self.current_job.status = JobStatus.CANCELLED

            # Cancel the background task if it exists
            if hasattr(self, '_background_task') and self._background_task is not None:
                if not self._background_task.done():
                    self._background_task.cancel()
                    try:
                        await self._background_task
                    except asyncio.CancelledError:
                        logger.debug("Background task cancelled successfully")
                    except Exception as e:
                        logger.warning(f"Background task cancellation raised exception: {e}")
                self._background_task = None

            # Best-effort: cancel current hardware print (non-blocking via thread)
            try:
                cancel_fn = getattr(self.recoater_client, "cancel_print_job", None)
                if callable(cancel_fn):
                    import asyncio as _asyncio
                    await _asyncio.to_thread(cancel_fn)
            except Exception as _e:
                logger.debug(f"Optional cancel_print_job ignored/failed: {_e}")

            # Cleanup OPC UA job state and clear errors
            if hasattr(self, 'cleanup_opcua_job'):
                await self.cleanup_opcua_job()
            if hasattr(self, 'clear_error_flags'):
                await self.clear_error_flags()

            job_id = getattr(self.current_job, 'job_id', None)
            # Fully reset in-memory job state to avoid stale status propagation
            self.current_job = None

            logger.info(f"Job {job_id} cancelled successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to cancel job: {e}")
            return False

    # ---- Status Management ----

    async def get_job_status(self) -> Optional[Dict[str, Any]]:
        """Get current job status using OPC UA as the source of truth.

        Returns a dict matching MultiMaterialJobStatusResponse fields.
        """
        # Attempt to read live values from OPC UA
        opcua = None
        try:
            opcua = self._resolve_opcua() if hasattr(self, "_resolve_opcua") else None
        except Exception:
            opcua = None

        # Defaults when OPC UA not available
        job_active = False
        total_layers = 0
        current_layer = 0
        backend_error = False
        plc_error = False
        ready_to_print = False
        layer_complete = False

        if opcua:
            try:
                # Read variables asynchronously from the in-proc OPC UA service
                job_active = bool(await opcua.read_variable("job_active"))
                total_layers = int(await opcua.read_variable("total_layers") or 0)
                current_layer = int(await opcua.read_variable("current_layer") or 0)
                backend_error = bool(await opcua.read_variable("backend_error") or False)
                plc_error = bool(await opcua.read_variable("plc_error") or False)
                ready_to_print = bool(await opcua.read_variable("recoater_ready_to_print") or False)
                layer_complete = bool(await opcua.read_variable("recoater_layer_complete") or False)
            except Exception as e:
                logger.warning(f"Failed to read OPC UA variables for status: {e}")


        # Compute progress percentage; keep logic aligned with FE computation
        progress_percentage = 0.0
        if total_layers > 0:
            progress_percentage = max(0.0, min(100.0, round((current_layer / total_layers) * 100.0, 2)))

        # Derive status string using recoater hardware state for refinement
        recoater_state = None
        try:
            # get_state() is sync; run it in a thread to avoid blocking
            recoater_resp = await asyncio.to_thread(self.recoater_client.get_state)
            if isinstance(recoater_resp, dict):
                recoater_state = recoater_resp.get("state")
        except Exception as e:
            logger.debug(f"Could not retrieve recoater state for status derivation: {e}")

        # Map to high-level status: one of "idle", "ready", "printing", "error"
        if backend_error or plc_error:
            status = "error"
        elif not job_active:
            status = "idle"
        else:
            if recoater_state == "printing":
                status = "printing"
            else:
                # Treat any non-error active state as ready/waiting
                status = "ready" if ready_to_print and not layer_complete else "ready"

        # Compose drums from in-memory job if available (file_ids etc. are not in OPC UA)
        drums_payload: Dict[int, Dict[str, Any]] = {}
        job_id = None
        if getattr(self, "current_job", None):
            job = self.current_job
            job_id = job.job_id
            for drum_id, drum in job.drums.items():
                drums_payload[drum_id] = {
                    "drum_id": drum.drum_id,
                    "status": drum.status,
                    "current_layer": drum.current_layer,
                    "total_layers": drum.total_layers,
                    "error_message": drum.error_message,
                    "file_id": drum.file_id,
                }

        error_message = ""
        if backend_error or plc_error:
            flags = []
            if backend_error:
                flags.append("backend_error")
            if plc_error:
                flags.append("plc_error")
            error_message = ", ".join(flags)

        return {
            "job_id": job_id,
            "is_active": job_active,
            "status": status,
            "current_layer": current_layer,
            "total_layers": total_layers,
            "progress_percentage": progress_percentage,
            "error_message": error_message,
            "drums": drums_payload,
        }

    def get_drum_status(self, drum_id: int) -> Optional[Dict[str, Any]]:
        """Get status for specific drum."""
        if not self.current_job or drum_id not in self.current_job.drums:
            return None

        drum = self.current_job.drums[drum_id]
        return {
            "drum_id": drum.drum_id,
            "status": drum.status,
            "current_layer": drum.current_layer,
            "total_layers": drum.total_layers,
            "error_message": drum.error_message,
            "file_id": drum.file_id,
            "last_update_time": drum.last_update_time,
        }

    # ---- Helper Methods ----

    async def _upload_layer_to_drum(self, drum_id: int, cli_data: bytes) -> None:
        """Upload CLI data to specific drum."""
        if not cli_data:
            logger.info(f"Loading empty CLI template for depleted drum {drum_id}")
            try:
                with open(self.job_config.EMPTY_LAYER_TEMPLATE_PATH, 'rb') as f:
                    cli_data = f.read()
                logger.info(f"Loaded empty CLI template ({len(cli_data)} bytes) for drum {drum_id}")
            except Exception as e:
                logger.error(f"Failed to load empty CLI template from configured path: {e}")
                raise MultiMaterialJobError(
                    f"Failed to load empty CLI template at {self.job_config.EMPTY_LAYER_TEMPLATE_PATH}: {e}"
                )
        # Use client high-level method that matches mock/real API
        # Offload sync call to a thread to avoid blocking
        await asyncio.to_thread(
            self.recoater_client.upload_drum_geometry,
            drum_id,
            cli_data,
            "text/plain",
        )

    def _generate_single_layer_cli(self, parsed_file: ParsedCliFile, layer_index: int) -> bytes:
        """Generate CLI data for a single layer."""
        if layer_index >= len(parsed_file.layers):
            return b""
        layer = parsed_file.layers[layer_index]
        # Build includes header and then delegate to editor for proper ASCII format
        return self.cli_parser.generate_single_layer_ascii_cli(layer, parsed_file.header_lines)

    async def clear_job_error_flags(self) -> bool:
        """Clear job-specific error flags (delegates to OPC UA coordination for system flags)."""
        try:
            # Attempt to clear mock hardware error state (dev mode) if method exists
            try:
                clear_fn = getattr(self.recoater_client, "clear_print_error", None)
                if callable(clear_fn):
                    import asyncio as _asyncio
                    await _asyncio.to_thread(clear_fn)
            except Exception as _e:
                logger.debug(f"Optional clear_print_error failed/ignored: {_e}")

            if self.current_job:
                self.current_job.error_message = ""
                self.current_job.retry_count = 0
                for drum in self.current_job.drums.values():
                    drum.error_message = ""

            logger.info("Cleared job-specific error flags")
            return True
        except Exception as e:
            logger.error(f"Failed to clear job error flags: {e}")
            return False
