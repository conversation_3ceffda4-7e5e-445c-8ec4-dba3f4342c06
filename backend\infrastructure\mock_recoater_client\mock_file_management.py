"""
Mock File Management Methods
============================

This module contains mock file management methods for the MockRecoaterClient.
These methods simulate the file management endpoints from the hardware API.
"""

import time
import threading
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class MockFileManagementMixin:
    """Mixin class providing mock file management methods for MockRecoaterClient."""

    def upload_drum_geometry(self, drum_id: int, file_data: bytes, content_type: str = "application/octet-stream") -> Dict[str, Any]:
        """
        Mock implementation for uploading drum geometry file.

        Args:
            drum_id: The ID of the drum to upload geometry to
            file_data: The binary file data to upload
            content_type: The content type of the file

        Returns:
            Mock success response
        """
        logger.info(f"Mock upload drum geometry: drum_id={drum_id}, file_size={len(file_data)}, content_type={content_type}")

        # Store the uploaded file data for persistent mock storage
        self._drum_geometries[drum_id] = {
            "file_data": file_data,
            "content_type": content_type
        }

        # Update drum state to simulate processing
        if drum_id not in self._drum_states:
            self._drum_states[drum_id] = "ready"

        # Simulate upload processing time
        self._drum_states[drum_id] = "uploading"

        # Schedule state change back to ready after a delay
        def reset_to_ready():
            time.sleep(2)  # 2 second processing time
            self._drum_states[drum_id] = "ready"

        threading.Thread(target=reset_to_ready, daemon=True).start()
        logger.info(f"Stored geometry file for drum {drum_id} in mock storage")

        return {
            "success": True,
            "drum_id": drum_id,
            "file_size": len(file_data),
            "content_type": content_type,
            "message": "Geometry file uploaded successfully"
        }

    def download_drum_geometry(self, drum_id: int) -> bytes:
        """
        Mock implementation for downloading drum geometry file.

        Args:
            drum_id: The ID of the drum to download geometry from

        Returns:
            Stored file data if available, otherwise mock PNG image data as bytes
        """
        logger.info(f"Mock download drum geometry: drum_id={drum_id}")

        # Return stored file data if available
        if drum_id in self._drum_geometries:
            stored_data = self._drum_geometries[drum_id]["file_data"]
            logger.info(f"Returning stored geometry file for drum {drum_id} (size: {len(stored_data)} bytes)")
            return stored_data
        else:
            # Generate color-coded preview for the specific drum
            logger.info(f"No stored file for drum {drum_id}, generating color-coded drum preview")
            try:
                # Generate a simple, deterministic PNG for testing consistency
                # This avoids issues with random elements in PNG generation
                import io
                from PIL import Image, ImageDraw

                # Create a simple colored rectangle based on drum_id for consistency
                img = Image.new('RGB', (100, 100), color='white')
                draw = ImageDraw.Draw(img)

                # Use drum-specific colors for consistency
                colors = ['blue', 'orange', 'green']
                color = colors[drum_id % len(colors)]
                draw.rectangle([10, 10, 90, 90], fill=color)

                # Convert to bytes
                img_bytes = io.BytesIO()
                img.save(img_bytes, format='PNG')
                png_bytes = img_bytes.getvalue()

                logger.info(f"Generated color-coded preview for drum {drum_id}")
                return png_bytes

            except Exception as e:
                logger.error(f"Failed to generate color-coded drum preview: {e}")
                # Fallback to simple PNG if generation fails
                mock_png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\rIDATx\x9cc\xf8\x0f\x00\x00\x01\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00IEND\xaeB`\x82'
                return mock_png_data

    def delete_drum_geometry(self, drum_id: int) -> Dict[str, Any]:
        """
        Mock implementation for deleting drum geometry file.

        Args:
            drum_id: The ID of the drum to delete geometry from

        Returns:
            Mock success response
        """
        logger.info(f"Mock delete drum geometry: drum_id={drum_id}")

        # Remove stored file data if it exists
        if drum_id in self._drum_geometries:
            del self._drum_geometries[drum_id]
            logger.info(f"Removed stored geometry file for drum {drum_id} from mock storage")
        else:
            logger.info(f"No stored file found for drum {drum_id}, nothing to delete")

        return {
            "success": True,
            "drum_id": drum_id,
            "message": "Geometry file deleted successfully"
        }
