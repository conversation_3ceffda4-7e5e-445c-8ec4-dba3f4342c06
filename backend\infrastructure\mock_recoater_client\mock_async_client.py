"""
Mock Async Client Methods
=========================

This module contains mock async client methods for the MockRecoaterClient.
These methods simulate the async and multi-material coordination endpoints.
"""


import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class MockAsyncClientMixin:
    """Mixin class providing mock async client methods for MockRecoaterClient."""

    async def upload_cli_data(self, drum_id: int, cli_data: bytes) -> Dict[str, Any]:
        """
        Mock async implementation for uploading CLI data to a specific drum.

        Args:
            drum_id: Target drum (0, 1, or 2)
            cli_data: ASCII CLI data to upload

        Returns:
            Mock response from the API
        """
        import asyncio

        # Simulate async upload with small delay
        await asyncio.sleep(0.1)

        # Use the existing upload_drum_geometry method
        return self.upload_drum_geometry(drum_id, cli_data, "application/octet-stream")

    async def get_drum_status(self, drum_id: int) -> Dict[str, Any]:
        """
        Mock async implementation for getting drum status.

        Args:
            drum_id: Target drum (0, 1, or 2)

        Returns:
            Dictionary containing drum status with 'ready' field
        """
        import asyncio

        # Simulate async status check with small delay
        await asyncio.sleep(0.05)

        # Get current drum state - ensure drums are initialized as ready
        if drum_id not in self._drum_states:
            self._drum_states[drum_id] = "ready"

        state = self._drum_states.get(drum_id, "ready")
        ready = state in ["ready", "idle", "standby"]

        return {
            "drum_id": drum_id,
            "state": state,
            "ready": ready,
            "current_layer": self._current_layers.get(drum_id, 0),
            "raw_info": self.get_drum(drum_id)
        }





    def set_multimaterial_job_active(self, active: bool) -> None:
        """
        Set the multi-material job active state for mock testing.

        Args:
            active: Whether the job is active
        """
        self._job_active = active
        logger.info(f"Mock multi-material job active: {active}")

    def advance_layer(self, drum_id: int) -> None:
        """
        Advance the current layer for a specific drum (for mock testing).

        Args:
            drum_id: Target drum (0, 1, or 2)
        """
        if drum_id in self._current_layers:
            self._current_layers[drum_id] += 1
        else:
            self._current_layers[drum_id] = 1

        logger.info(f"Mock drum {drum_id} advanced to layer {self._current_layers[drum_id]}")

    def reset_multimaterial_state(self) -> None:
        """Reset all multi-material state for mock testing."""
        self._drum_states = {0: "ready", 1: "ready", 2: "ready"}
        self._current_layers = {0: 0, 1: 0, 2: 0}
        self._job_active = False
        logger.info("Mock multi-material state reset")
