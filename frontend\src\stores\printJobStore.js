/**
 * Print Job Store
 * ===============
 * 
 * Pinia store for managing multi-material print job state, including
 * 3-drum coordination, job progress, error handling, and real-time updates.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import apiService from '../services/api'

export const usePrintJobStore = defineStore('printJob', () => {
  // State
  const multiMaterialJob = ref({
    isActive: false,
    jobId: null,
    currentLayer: 0,
    totalLayers: 0,
    progressPercentage: 0,
    status: 'idle', // idle, uploading, printing, waiting, completed, error
    errorMessage: '',
    drums: {
      0: { fileId: null, status: 'idle', errorMessage: '' },
      1: { fileId: null, status: 'idle', errorMessage: '' },
      2: { fileId: null, status: 'idle', errorMessage: '' }
    }
  })

  // Error flags for OPC UA coordination
  const errorFlags = ref({
    backendError: false,
    plcError: false,
    errorMessage: '',
    showCriticalModal: false
  })

  // Loading states
  const isLoading = ref(false)
  const isStartingJob = ref(false)
  const isCancellingJob = ref(false)
  const isClearingErrors = ref(false)

  // File upload state
  const uploadedFiles = ref({
    0: null, // { fileId: string, fileName: string, layerCount: number }
    1: null,
    2: null
  })

  // Last uploaded file tracking for File Management tab
  const lastUploadedFiles = ref({
    0: null, // { fileName: string, uploadTime: Date, source: 'direct' | 'cli_layer' }
    1: null,
    2: null
  })

  // Computed properties
  const isJobActive = computed(() => multiMaterialJob.value.isActive)
  const hasActiveJob = computed(() => multiMaterialJob.value.jobId !== null)
  const hasErrors = computed(() => errorFlags.value.backendError || errorFlags.value.plcError)
  const hasCriticalError = computed(() => errorFlags.value.showCriticalModal)
  


  const allFilesUploaded = computed(() => {
    return Object.values(uploadedFiles.value).every(file => file !== null)
  })

  const hasMinimumFiles = computed(() => {
    // Allow starting with at least 1 uploaded drum; missing drums use empty_layer.cli
    const uploadedCount = Object.values(uploadedFiles.value).filter(file => file !== null).length
    return uploadedCount >= 1
  })

  const canStartJob = computed(() => {
    return hasMinimumFiles.value &&
           !isJobActive.value &&
           !hasErrors.value &&
           !isLoading.value
  })

  const jobProgress = computed(() => {
    return Math.round(multiMaterialJob.value.progressPercentage || 0)
  })

  // Actions
  function updateJobStatus(statusData) {
    if (!statusData) return

    // Map snake_case API fields to our camelCase store shape
    const mapped = {
      // ?? (Nullish Coalescing) Operator returns the right-hand side when left side is null
      // This creates a new object where each property either gets a value from the API response
      // or the current store state as a fallback
      isActive: statusData.is_active ?? multiMaterialJob.value.isActive,
      jobId: statusData.job_id ?? multiMaterialJob.value.jobId,
      currentLayer: statusData.current_layer ?? multiMaterialJob.value.currentLayer,
      totalLayers: statusData.total_layers ?? multiMaterialJob.value.totalLayers,
      progressPercentage: statusData.progress_percentage ?? multiMaterialJob.value.progressPercentage,
      status: statusData.status ?? multiMaterialJob.value.status,
      errorMessage: statusData.error_message ?? ''
    }

    // Update core job fields
    multiMaterialJob.value = {
      ...multiMaterialJob.value,
      ...mapped
    }

    // Derive/system error flags if backend exposes them or via status/error_message
    const backendError = Boolean(statusData.backend_error) || mapped.status === 'error' || !!mapped.errorMessage
    const plcError = Boolean(statusData.plc_error)
    if (backendError || plcError) {
      setErrorFlags(backendError, plcError, mapped.errorMessage || (backendError ? 'Backend reported an error' : ''))
    }
  }

  function updateDrumStatus(drumId, drumData) {
    if (multiMaterialJob.value.drums[drumId]) {
      multiMaterialJob.value.drums[drumId] = {
        ...multiMaterialJob.value.drums[drumId],
        ...drumData
      }
    }
  }

  function setErrorFlags(backendError, plcError, message = '') {
    errorFlags.value.backendError = backendError
    errorFlags.value.plcError = plcError
    errorFlags.value.errorMessage = message
    
    // Show critical modal if either error flag is set
    if (backendError || plcError) {
      errorFlags.value.showCriticalModal = true
    }
  }

  function clearErrorFlags() {
    errorFlags.value.backendError = false
    errorFlags.value.plcError = false
    errorFlags.value.errorMessage = ''
    errorFlags.value.showCriticalModal = false
  }

  function closeCriticalModal() {
    errorFlags.value.showCriticalModal = false
  }

  function setFileUploaded(drumId, fileData) {
    uploadedFiles.value[drumId] = fileData
  }

  function clearUploadedFiles() {
    uploadedFiles.value = { 0: null, 1: null, 2: null }
  }

  function clearUploadedFile(drumId) {
    if (drumId in uploadedFiles.value) {
      uploadedFiles.value[drumId] = null
    }
  }

  // File Management tab tracking functions
  function setLastUploadedFile(drumId, fileName, source = 'direct') {
    lastUploadedFiles.value[drumId] = {
      fileName,
      uploadTime: new Date(),
      source
    }
  }

  function clearLastUploadedFiles() {
    lastUploadedFiles.value = { 0: null, 1: null, 2: null }
  }

  function clearLastUploadedFile(drumId) {
    if (drumId in lastUploadedFiles.value) {
      lastUploadedFiles.value[drumId] = null
    }
  }

  function getLastUploadedFileName(drumId) {
    const fileInfo = lastUploadedFiles.value[drumId]
    if (!fileInfo) return 'No file uploaded'

    if (fileInfo.source === 'cli_layer') {
      return fileInfo.fileName // Already includes layer range prefix
    }
    return fileInfo.fileName
  }

  function resetJobState() {
    multiMaterialJob.value = {
      isActive: false,
      jobId: null,
      currentLayer: 0,
      totalLayers: 0,
      progressPercentage: 0,
      status: 'idle',
      errorMessage: '',
      drums: {
        0: { fileId: null, status: 'idle', errorMessage: '' },
        1: { fileId: null, status: 'idle', errorMessage: '' },
        2: { fileId: null, status: 'idle', errorMessage: '' }
      }
    }
  }

  // API Actions
  async function startMultiMaterialJob() {
    if (!canStartJob.value) {
      throw new Error('Cannot start job: requirements not met')
    }

    isStartingJob.value = true
    try {
      // Start job; backend uses cached per-drum files (no payload)
      const response = await apiService.startMultiMaterialJob()

      if (response.data.success) {
        multiMaterialJob.value.jobId = response.data.job_id
        multiMaterialJob.value.isActive = true
        // Optimistically set totals to avoid 0/0 in dev until backend status ticks in
        const counts = Object.values(uploadedFiles.value)
          .filter(f => f && typeof f.layerCount === 'number')
          .map(f => f.layerCount)
        const maxLayers = counts.length ? Math.max(...counts) : 0
        multiMaterialJob.value.totalLayers = maxLayers
        multiMaterialJob.value.currentLayer = maxLayers > 0 ? 1 : 0
        multiMaterialJob.value.status = 'printing'
        multiMaterialJob.value.progressPercentage = maxLayers > 0 ? (100 * (multiMaterialJob.value.currentLayer - 1) / maxLayers) : 0
        return response.data
      } else {
        throw new Error(response.data.message || 'Failed to start job')
      }
    } finally {
      isStartingJob.value = false
    }
  }

  async function cancelMultiMaterialJob() {
    if (!hasActiveJob.value) {
      throw new Error('No active job to cancel')
    }

    isCancellingJob.value = true
    try {
      const response = await apiService.cancelMultiMaterialJob()

      if (response.data.success) {
        resetJobState()
        // Clear all file tracking since backend cache is cleared on job cancellation
        clearUploadedFiles()
        clearLastUploadedFiles()
        return response.data
      } else {
        throw new Error(response.data.message || 'Failed to cancel job')
      }
    } finally {
      isCancellingJob.value = false
    }
  }

  async function clearErrorFlagsAPI() {
    isClearingErrors.value = true
    try {
      const response = await apiService.clearErrorFlags()
      
      if (response.data.success) {
        clearErrorFlags()
        return response.data
      } else {
        throw new Error(response.data.message || 'Failed to clear error flags')
      }
    } finally {
      isClearingErrors.value = false
    }
  }

  async function fetchJobStatus() {
    try {
      const response = await apiService.getMultiMaterialJobStatus()
      updateJobStatus(response.data)
      return response.data
    } catch (error) {
      console.error('Failed to fetch job status:', error)
      throw error
    }
  }

  async function fetchDrumStatus(drumId) {
    try {
      const response = await apiService.getDrumStatus(drumId)
      updateDrumStatus(drumId, response.data)
      return response.data
    } catch (error) {
      console.error(`Failed to fetch drum ${drumId} status:`, error)
      throw error
    }
  }

  return {
    // State
    multiMaterialJob,
    errorFlags,
    isLoading,
    isStartingJob,
    isCancellingJob,
    isClearingErrors,
    uploadedFiles,
    lastUploadedFiles,

    // Computed
    isJobActive,
    hasActiveJob,
    hasErrors,
    hasCriticalError,

    allFilesUploaded,
    hasMinimumFiles,
    canStartJob,
    jobProgress,

    // Actions
    updateJobStatus,
    updateDrumStatus,
    setErrorFlags,
    clearErrorFlags,
    closeCriticalModal,
    setFileUploaded,
    clearUploadedFiles,
    clearUploadedFile,
    setLastUploadedFile,
    clearLastUploadedFiles,
    clearLastUploadedFile,
    getLastUploadedFileName,
    resetJobState,
    startMultiMaterialJob,
    cancelMultiMaterialJob,
    clearErrorFlagsAPI,
    fetchJobStatus,
    fetchDrumStatus
  }
})
