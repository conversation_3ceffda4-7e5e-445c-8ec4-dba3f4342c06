"""
Binary CLI Parser Mixin
=======================

This module provides the BinaryCliParser mixin for parsing binary CLI (Common Layer Interface) files
with support for aligned and unaligned formats.

Key Features:
- Parse binary CLI files with proper header extraction
- Handle aligned and unaligned binary formats
- Extract binary geometry data (layers, polylines, hatches)
- Process command codes and binary data structures
- Robust error handling with detailed error messages

Dependencies:
- Relies on cli_models for data structures and cli_exceptions for error handling.

Usage:
    class MyParser(BinaryCliParser):
        pass
    parser = MyParser()
    parsed = parser.parse_binary(binary_data)
"""
import io
import struct
import logging
from typing import List, Optional, Tuple

from .cli_models import Point, Polyline, Hatch, CliLayer, ParsedCliFile
from .cli_exceptions import CliParsingError


class BinaryCliParser:
    """Mixin for binary CLI files with support for aligned and unaligned formats."""

    @property
    def logger(self):
        """Get logger from parent class or create a default one."""
        if hasattr(self, '_logger') and self._logger:
            return self._logger
        return logging.getLogger(__name__)

    def _read_and_unpack(self, stream: io.BytesIO, fmt: str, size: int) -> tuple:
        """Reads bytes from a stream and unpacks them."""
        data = stream.read(size)
        if len(data) < size:
            raise EOFError(f"Unexpected EOF. Wanted {size} bytes, got {len(data)}.")
        return struct.unpack(fmt, data)

    def parse_binary(self, cli_byte_stream: bytes) -> ParsedCliFile:
        """Parse binary CLI file with proper header and geometry separation.
        
        The parsing is split into two phases:
        1. Parse header section until $$HEADEREND marker
        2. Parse binary geometry commands (layer, polyline, hatches)
        """
        stream = io.BytesIO(cli_byte_stream)
        
        header_lines, is_aligned = self._parse_binary_header(stream)
        layers = self._parse_binary_geometry(stream, is_aligned)
        
        return ParsedCliFile(header_lines=header_lines, is_aligned=is_aligned, layers=layers)
    
    def _parse_binary_header(self, stream: io.BytesIO) -> Tuple[List[str], bool]:
        """Parse the header section of a binary CLI file until $$HEADEREND marker."""
        header_end_marker = b"$$HEADEREND"
        header_buffer = bytearray()
        max_header_size = 8192
        
        # Read header until $$HEADEREND marker is found
        while True:
            byte = stream.read(1)
            if not byte:
                raise CliParsingError(f"EOF reached before '{header_end_marker.decode()}' was found.")
            header_buffer.append(byte[0])
            
            # Check if we found the marker
            if header_end_marker in header_buffer:
                marker_pos = header_buffer.find(header_end_marker)
                actual_header_bytes = header_buffer[:marker_pos + len(header_end_marker)]
                
                # Set stream position to after the header
                geometry_start_offset = stream.tell() - (len(header_buffer) - len(actual_header_bytes))
                stream.seek(geometry_start_offset)
                break
                
            if len(header_buffer) > max_header_size:
                raise CliParsingError(f"Header size exceeds {max_header_size} bytes limit.")
        
        # Process header text
        header_str = self._decode_header_bytes(header_buffer[:marker_pos + len(header_end_marker)])
        header_lines = [line.strip() for line in header_str.splitlines() if line.strip()]
        is_aligned = any("$$ALIGN" in line.upper() for line in header_lines)
        
        self.logger.info(f"CLI binary header parsed. Alignment detected: {is_aligned}")
        return header_lines, is_aligned
    
    def _decode_header_bytes(self, header_bytes: bytearray) -> str:
        """Safely decode header bytes to string with error handling."""
        try:
            return header_bytes.decode('ascii', errors='strict')
        except UnicodeDecodeError as e:
            self.logger.warning(f"Unicode decode error in header: {e}. Using replacement characters.")
            return header_bytes.decode('ascii', errors='replace')
    
    def _parse_binary_geometry(self, stream: io.BytesIO, is_aligned: bool) -> List[CliLayer]:
        """Parse the geometry section of a binary CLI file."""
        layers: List[CliLayer] = []
        current_layer: Optional[CliLayer] = None

        while True:
            try:
                command_code = self._read_command_code(stream, is_aligned)
                
                if command_code == 127:  # Layer command
                    current_layer = self._parse_binary_layer_command(stream)
                    layers.append(current_layer)
                
                elif command_code == 130:  # Polyline command
                    self._parse_binary_polyline_command(stream, current_layer)

                elif command_code == 132:  # Hatches command
                    self._parse_binary_hatches_command(stream, current_layer)
                
                else:
                    self.logger.warning(f"Unsupported command code {command_code} at offset {stream.tell()}. Stopping parse.")
                    break

            except EOFError:
                self.logger.info("Successfully reached end of CLI geometry data.")
                break
            except struct.error as e:
                raise CliParsingError(f"Struct unpacking error at offset {stream.tell()}: {e}")

        return layers
    
    def _read_command_code(self, stream: io.BytesIO, is_aligned: bool) -> int:
        """Read command code from binary stream, handling alignment if needed."""
        command_code = self._read_and_unpack(stream, "<H", 2)[0]
        if is_aligned:
            stream.read(2)  # Skip 2 alignment bytes
        return command_code
    
    def _parse_binary_layer_command(self, stream: io.BytesIO) -> CliLayer:
        """Parse a binary layer command and return a new CliLayer."""
        z = self._read_and_unpack(stream, "<f", 4)[0]
        return CliLayer(z_height=z, polylines=[], hatches=[])
    
    def _parse_binary_polyline_command(self, stream: io.BytesIO, current_layer: Optional[CliLayer]) -> None:
        """Parse a binary polyline command and add it to the current layer."""
        if not current_layer:
            raise CliParsingError("Found Polyline data before a Layer was defined.")
        
        part_id, direction, num_points = self._read_and_unpack(stream, "<iii", 12)
        points = self._read_binary_points(stream, num_points)
        
        current_layer.polylines.append(Polyline(part_id=part_id, direction=direction, points=points))
    
    def _parse_binary_hatches_command(self, stream: io.BytesIO, current_layer: Optional[CliLayer]) -> None:
        """Parse a binary hatches command and add it to the current layer."""
        if not current_layer:
            raise CliParsingError("Found Hatch data before a Layer was defined.")
        
        group_id, num_lines = self._read_and_unpack(stream, "<ii", 8)
        hatch_lines = self._read_binary_hatch_lines(stream, num_lines)
        
        current_layer.hatches.append(Hatch(group_id=group_id, lines=hatch_lines))
    
    def _read_binary_points(self, stream: io.BytesIO, num_points: int) -> List[Point]:
        """Read a series of points from binary stream."""
        points = []
        for _ in range(num_points):
            x = self._read_and_unpack(stream, "<f", 4)[0]
            y = self._read_and_unpack(stream, "<f", 4)[0]
            points.append(Point(x=x, y=y))
        return points
    
    def _read_binary_hatch_lines(self, stream: io.BytesIO, num_lines: int) -> List[Tuple[Point, Point]]:
        """Read a series of hatch lines from binary stream."""
        hatch_lines = []
        for _ in range(num_lines):
            x1, y1, x2, y2 = self._read_and_unpack(stream, "<ffff", 16)
            hatch_lines.append((Point(x=x1, y=y1), Point(x=x2, y=y2)))
        return hatch_lines