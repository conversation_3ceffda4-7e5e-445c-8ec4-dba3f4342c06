"""
Job Management Services
======================

Primary entry point for multi-material job coordination and management logic.
Prefer importing and using MultiMaterialJobService directly.

Exposed Public API:
- MultiMaterialJobService: Main service class for multi-material job operations

Public Methods (via MultiMaterialJobService):
- __init__(recoater_client, opcua=None, job_config=None): Initialize the service
- start_layer_by_layer_job(): Start iterative layer-by-layer printing job
- clear_all_error_flags(): Unified error clearing across job and OPC UA layers

LayerProcessingMixin Methods (Job Lifecycle + Layer Operations):
- create_job(file_ids): Create multi-material job from file mappings [LEGACY: Tests only]
- cancel_job(): Cancel the current job
- get_job_status(): Get current job status with progress
- get_drum_status(drum_id): Get status for specific drum
- clear_job_error_flags(): Clear job-specific error states

OPCUACoordinationMixin Methods (OPC UA Variable Management):
- setup_opcua_job(total_layers): Setup OPC UA state for job
- cleanup_opcua_job(): Cleanup OPC UA state after job completion
- reset_opcua_layer_flags(): Reset per-layer flags before uploads/print cycle
- update_opcua_layer_progress(progress): Update current layer progress
- signal_opcua_layer_complete(): Signal completion of current layer
- signal_opcua_ready_to_print(): Signal that recoater is ready to print
- handle_job_error(error): Set backend_error flag and update state
- wait_for_layer_completion(): Wait for layer completion via hardware polling
- clear_error_flags(): Clear all OPC UA error flags

CliCachingMixin Methods (CLI File Caching):
- add_cli_file(file_id, parsed_file, filename): Add file to generic cache
- get_cli_file(file_id): Get parsed CLI file data
- get_cli_file_with_metadata(file_id): Get file with metadata
- cli_file_exists(file_id): Check if file exists in cache
- cache_cli_file_for_drum(drum_id, parsed_file, filename): Cache file for specific drum
- get_cached_file_for_drum(drum_id): Get cached file for drum
- get_max_layers(): Get maximum layer count across drums
- has_cached_files(): Check if any drums have cached files
- clear_drum_cache(): Clear all drum caches
- clear_drum_cache_for_drum(drum_id): Clear specific drum cache
- get_cache_status(): Get comprehensive status of all caches

Private Helper Methods (via MultiMaterialJobService):
- _validate_and_setup_job(): Validate cached files and setup job state
- _process_all_layers(): Process all layers sequentially
- _process_layer(layer_index): Process single layer following exact workflow
- _upload_layer_to_drums(layer_index): Upload layer data to each drum with delay
- _prepare_and_start_print_job(layer_index): Signal recoater ready and start print job

Mixin Private Helper Methods:
- _upload_layer_to_drum(drum_id, cli_data): Upload CLI data to specific drum
- _generate_single_layer_cli(parsed_file, layer_index): Generate CLI data for single layer
- _load_empty_layer_template(): Load empty layer template file [LEGACY: create_job() only]
- _resolve_opcua(): Resolve the OPC UA service instance
- _set_error_state(error_message): Set coordination error state

Shared Types:
- CliCacheEntry: Structure for CLI file cache entries
- MultiMaterialJobError: Common exception for job operations
"""

# Public service API
from .multimaterial_job_service import MultiMaterialJobService

# Shared models and types
from .models import CliCacheEntry, MultiMaterialJobError


__all__ = [
    'MultiMaterialJobService',
    'CliCacheEntry',
    'MultiMaterialJobError',
]